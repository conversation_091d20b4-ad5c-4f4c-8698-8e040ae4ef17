<template>
  <div class="ListItem-wrapper">
    <div class="title">{{ title }}</div>
    <div v-if="hasLabel" class="label">
      <div class="label-item">{{ label[0] }}</div>
      <div class="label-item">{{ label[1] }}</div>
    </div>
    <div class="listItem" v-for="listItem in list" :key="listItem.name">
      <div class="listItem-item">{{ listItem.districtName }}</div>
      <div class="listItem-item">{{ listItem.pointNum }}</div>
    </div>
    <van-empty description="暂无数据" v-if="list.length == 0" />
  </div>
</template>
<script>
  export default {
    components: {},
    props: ["title", "hasLabel", "list", "label"],
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .ListItem-wrapper {
    margin-bottom: 12px;
    background-color: #fff;
    border-radius: 8px;
  }
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #4ca786;
    line-height: 40px;
    padding-left: 12px;
    background: linear-gradient(180deg, #eefcf7 0%, #ffffff 100%);
  }
  .label {
    padding: 0 12px 0 12px;
    display: flex;
    justify-content: space-between;
  }
  .listItem {
    padding: 0 12px 0 12px;
    display: flex;
    justify-content: space-between;
  }
  .listItem-item {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 40px;
  }
  .label-item {
    font-weight: bold;
    font-size: 14px;
    color: #162e25;
    line-height: 40px;
  }
</style>
