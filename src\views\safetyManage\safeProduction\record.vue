<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordFieldArr="recordFieldArr" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { SAFEPRODUCTION_TYPE } from "@/enums";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/safety/safetyproduction/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "activityName",
                value: "活动名称",
              },
              {
                key: "type",
                value: "活动类型",
                options: SAFEPRODUCTION_TYPE,
              },
              {
                key: "number",
                value: "参与人数",
                unit: "人",
              },
              {
                key: "personInCharge",
                value: "负责人",
              },
              {
                key: "activeDate",
                value: "活动日期",
              },
              {
                key: "address",
                value: "活动地点",
              },
              {
                key: "description",
                value: "活动内容",
              },
              {
                key: "fileUrl",
                value: "活动附件",
                isFile: true,
                isObject: true,
              },
            ],
          },
        ],
        fileList: [],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped></style>
