<template>
  <van-cell-group>
    <van-cell :title="item.roleName" v-for="(item, index) in roleList" :key="index" @click="changeRole(item)">
      <van-icon v-if="currentLevel == item.level" slot="right-icon" name="success" size="24" color="#4ca786" />
    </van-cell>
  </van-cell-group>
</template>

<script>
  import { getCurrentLevel, setCurrentLevel, setLevelId } from "@/utils/storage";
  import { getInfoApiFun } from "@/api/base";
  export default {
    data() {
      return {
        roleList: [],
        currentLevel: 0,
      };
    },
    created() {
      this.currentLevel = getCurrentLevel();
      this.initData();
    },
    methods: {
      async initData() {
        try {
          let res = await getInfoApiFun("", "/api/userrole/currentRoleList");
          this.roleList = res.data;
        } catch (error) {
          console.log(error);
        }
      },
      // 切换角色
      changeRole(item) {
        setCurrentLevel(item.level);
        setLevelId(item.roleId);
        this.currentLevel = item.level;
      },
    },
  };
</script>

<style lang="scss" scoped></style>
