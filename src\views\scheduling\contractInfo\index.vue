<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { getInfoApiFun } from "@/api/base";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "merchantFileName",
            value: "客商名称",
          },
          {
            key: "code",
            value: "合同编号",
          },
          {
            key: "typeName",
            value: "合同类型",
          },
          {
            key: "statusName",
            value: "合同状态",
          },
          {
            key: "effectiveDate",
            value: "生效日期",
          },
          {
            key: "expiryDate",
            value: "失效日期",
          },
        ],
        apis: {
          listPage: "/api/contract/contractinfo/listPage",
          contractStatusList: "/api/dict/contractStatus/list",
          contractTypeList: "/api/dict/contractType/list",
        },
        filterList: [
          {
            type: "Input",
            key: "merchantFileName",
            value: "客商名称",
          },
          {
            type: "Input",
            key: "code",
            value: "合同编号",
          },
          {
            type: "Options",
            key: "type",
            value: "合同类型",
            keyword: "id",
            valueKey: "name",
            enums: [],
          },
          {
            type: "Options",
            key: "status",
            value: "合同状态",
            keyword: "id",
            valueKey: "name",
            enums: [],
          },
          {
            type: "Date",
            key: "effectiveDate",
            value: "生效日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Date",
            key: "expiryDate",
            value: "失效日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFun("", this.apis.contractTypeList),
          getInfoApiFun("", this.apis.contractStatusList),
        ];
        let res = await Promise.all(promiseList);
        this.filterList[2].enums = res[0].data;
        this.filterList[3].enums = res[1].data;
      },
      // 查看详情
      itemClick(item) {
        this.$commonSkip("contractInfoRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
