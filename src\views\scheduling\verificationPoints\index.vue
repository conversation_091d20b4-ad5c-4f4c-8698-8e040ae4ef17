<template>
  <div class="container">
    <header class="header-tab bg-white">
      <ul class="header-tab-list">
        <li
          class="header-tab-item"
          :class="{ active: activeTab === index }"
          v-for="(item, index) in tabList"
          :key="index"
          @click="toggleHeader(index)"
        >
          <div class="header-tab-item-title">{{ item }}</div>
          <div class="header-tab-item-line"></div>
        </li>
      </ul>
    </header>
    <div class="search-box">
      <van-search
        v-model.trim="productionUnit"
        placeholder="请输入点位名称"
        clearable
        @search="onRefresh"
        @clear="onRefresh"
      />
    </div>
    <div class="commission-tabs" v-if="commissionList.length > 0">
      <van-tabs
        v-model="activeWaybill"
        color="#4ca786"
        title-active-color="#4ca786"
        line-width="60px"
        :ellipsis="false"
        @change="changeWaybill"
      >
        <van-tab :title="item.name" v-for="(item, index) in commissionList" :key="index"></van-tab>
      </van-tabs>
    </div>
    <div class="info-box" v-if="commissionList.length > 0">
      <template v-if="tableList.length">
        <van-field label="地磅核实重量（kg）" label-width="140" :border="false" v-show="activeTab === 0">
          <template #input>
            <van-stepper
              v-model="weighingWeight"
              min="0"
              default-value=""
              input-width="100%"
              :decimal-length="2"
              :show-plus="false"
              :show-minus="false"
            />
          </template>
        </van-field>
        <van-field label="点位重量总计（kg）" label-width="140" :border="false" v-show="activeTab === 0">
          <template #input>
            <van-stepper
              :value="pointWeightTotal"
              min="0"
              default-value=""
              input-width="100%"
              allow-empty
              disabled
              :decimal-length="2"
              :show-plus="false"
              :show-minus="false"
            />
          </template>
        </van-field>
        <van-field label="空桶数量" label-width="140" :border="false" v-show="activeTab === 0">
          <template #input>
            <van-stepper v-model="emptyBucketCount" min="0" max="56" default-value="" integer input-width="64%" />
          </template>
        </van-field>
      </template>
      <van-field label="地磅核实重量（kg）" label-width="140" :border="false" v-show="activeTab === 1">
        <template #input>
          <van-stepper
            :value="waybillWeight"
            min="0"
            default-value=""
            input-width="100%"
            disabled
            :decimal-length="2"
            :show-plus="false"
            :show-minus="false"
          />
        </template>
      </van-field>
      <van-field label="点位重量总计（kg）" label-width="140" :border="false" v-show="activeTab === 1">
        <template #input>
          <van-stepper
            :value="waybillWeight"
            min="0"
            default-value=""
            input-width="100%"
            allow-empty
            disabled
            :decimal-length="2"
            :show-plus="false"
            :show-minus="false"
          />
        </template>
      </van-field>
      <van-field label="空桶数量" label-width="140" :border="false" v-show="activeTab === 1">
        <template #input>
          <van-stepper
            :value="commissionList[activeWaybill].empty_bucket_count"
            min="0"
            max="56"
            default-value=""
            integer
            input-width="100%"
            disabled
            :show-plus="false"
            :show-minus="false"
          />
        </template>
      </van-field>
      <div class="weight-warning" v-show="weighingWeight != pointWeightTotal && activeTab === 0 && tableList.length">
        <van-icon name="warning" size="28" color="#ff6b81" />
        <div class="weight-warning-title">当前各点位核实重量总计之和与地磅核实重量不匹配</div>
      </div>
    </div>
    <main class="main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="item" v-for="(item, index) in tableList" :key="item.id">
              <div class="item-left">{{ index + 1 }}</div>
              <div class="item-right">
                <div class="item-top">
                  <div class="is-flex top-box">
                    <div class="item-title pi-one-line-clamp">{{ item.productionUnit }}</div>
                  </div>
                </div>
                <div class="is-flex flex-align-end">
                  <div class="item-text">
                    <span class="text-value">{{ item.address || "-" }}</span>
                    <span class="text-value">收运时间：{{ item.waybillTime }} </span>
                    <div class="item-weight">
                      <div class="text-value">核对重量（kg）：</div>
                      <van-stepper
                        v-model="item.weighingWeight"
                        min="0"
                        default-value=""
                        input-width="100%"
                        allow-empty
                        :disabled="activeTab === 1"
                        :decimal-length="2"
                        :show-plus="false"
                        :show-minus="false"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
    <div class="bottom-line"></div>
    <footer class="footer" v-if="activeTab === 0 && commissionList.length > 0 && tableList.length">
      <div class="footer-button" @click="submitWeightThrottling">提交</div>
    </footer>
  </div>
</template>

<script>
  import { getUserInfo } from "@/utils/storage";
  import { getListPageApiFun, getInfoApiFun, createApiFun } from "@/api/base";
  import { floatAdd, roundReserveDecimals } from "@/utils/index";
  import moment from "moment";
  export default {
    data() {
      return {
        userInfo: "",
        tabList: ["未确认点位", "已确认点位"],
        activeTab: 0,
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        tableList: [],
        apis: {
          listPage: "/api/waybill/commissionDetailListPage",
          roleList: "/api/userrole/currentRoleList",
          commissionList: "/api/waybill/commissionList",
          completeWeighingWeight: "/api/waybill/waybillDetail/completeWeighingWeight",
          getTotalWeighingWeight: "/api/waybill/getTotalWeighingWeight/",
          updateEmptyBucketCount: "/api/waybill/updateEmptyBucketCount",
        },
        routeTaskList: [],
        waybillType: "", //收运方式
        moment,
        levelList: [],
        productionUnit: "",
        activeWaybill: 0,
        commissionList: [],
        weighingWeight: 0,
        waybillWeight: 0,
        emptyBucketCount: 0,
        submitWeightThrottling: () => {},
      };
    },
    computed: {
      pointWeightTotal() {
        let arr = this.tableList.filter((item) => item.weighingWeight || item.weighingWeight === 0);
        if (arr.length === 0) return null;
        return arr.reduce((total, item) => {
          return roundReserveDecimals(floatAdd(total, Number(item.weighingWeight)));
        }, 0);
      },
    },
    created() {
      this.userInfo = getUserInfo();
      this.getRoleList();
      this.getCommissionList();
      this.submitWeightThrottling = this.$throttling(this.submitWeight, 500);
    },
    mounted() {},
    methods: {
      // 获取权限列表
      async getRoleList() {
        let res = await getInfoApiFun("", this.apis.roleList);
        this.levelList = res.data.map((item) => item.level);
      },
      // 获取路线收运单列表
      async getCommissionList() {
        let res = await createApiFun(
          { waybillStatus: 1, waybillDate: moment().format("YYYY-MM-DD") },
          this.apis.commissionList,
        );
        if (res.success) {
          this.commissionList = res.data;
          this.getTotalWeighingWeight();
          this.onRefresh();
        }
      },
      toggleHeader(index) {
        if (index === this.activeTab) {
          return;
        }
        this.activeTab = index;
        this.weighingWeight = 0;
        this.onRefresh();
      },
      // 根据收运单id获取电子收运单总过磅重量
      async getTotalWeighingWeight() {
        try {
          let res = await getInfoApiFun(this.commissionList[this.activeWaybill].id, this.apis.getTotalWeighingWeight);
          if (res.success) {
            this.waybillWeight = res.data;
          }
        } catch (error) {
          console.log(error);
        }
      },
      changeWaybill() {
        this.weighingWeight = 0;
        this.getTotalWeighingWeight();
        this.onRefresh();
      },
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          waybillStatus: 1,
          waybillId: this.commissionList[this.activeWaybill].id,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          productionUnit: this.productionUnit,
          isWeighing: this.activeTab,
          waybillDate: moment().format("YYYY-MM-DD"),
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            let dataList = res.data.datas.map((item) => {
              return {
                ...item,
                weighingWeight: this.activeTab === 1 ? item.weighingWeight : item.rubbishTotal,
              };
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        if (this.commissionList.length === 0) {
          this.tableList = [];
          this.finished = true;
          this.refreshing = false;
          return;
        }
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 异常上报
      async submitWeight() {
        console.log("this.pointWeightTotal ==> ", this.pointWeightTotal);
        console.log("this.weighingWeight ==> ", this.weighingWeight);

        if (this.pointWeightTotal != this.weighingWeight) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "当前各点位核实重量总计之和与地磅核实重量不匹配",
            duration: 1500,
          });
          return;
        }
        let params = this.tableList
          .filter((item) => item.weighingWeight || item.weighingWeight === 0)
          .map((list) => {
            return {
              id: list.id,
              weighingWeight: list.weighingWeight,
            };
          });
        this.$toast({
          type: "loading",
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });
        try {
          await createApiFun(
            { id: this.commissionList[this.activeWaybill].id, emptyBucketCount: this.emptyBucketCount },
            this.apis.updateEmptyBucketCount,
          );
          let res = await createApiFun(params, this.apis.completeWeighingWeight);
          if (res.success) {
            this.$toast({
              type: "success",
              message: `提交成功`,
              forbidClick: true,
              duration: 1500,
            });
            this.weighingWeight = 0;
            this.getCommissionList();
            // this.getTotalWeighingWeight();
            // this.onRefresh();
          }
          setTimeout(() => {
            this.$toast.clear();
          }, 1500);
        } catch (error) {
          this.$toast.clear();
        }
      },
      // 查看详情
      viewRecord(item) {
        if (this.activeTab === 0) {
          return;
        }
        this.$commonSkip("electronicWaybillRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    display: flex;
    flex-direction: column;
  }
  .header-tab {
    padding: 12px;
    .header-tab-list {
      display: flex;
      align-items: center;
      .header-tab-item {
        padding-bottom: 4px;
        margin-right: 12px;
        &:last-child {
          margin-right: 0;
        }
        .header-tab-item-title {
          font-size: 14px;
          color: #162e25;
          line-height: 22px;
        }
        .header-tab-item-line {
          width: 100%;
          height: 2px;
          background-color: #4ca786;
          margin-top: 2px;
          border-radius: 2px;
          opacity: 0;
        }
        &.active {
          .header-tab-item-title {
            color: #4ca786;
            font-weight: 500;
          }
          .header-tab-item-line {
            opacity: 1;
          }
        }
      }
    }
  }
  .commission-tabs {
    margin: 0 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e5e5;
    background-color: #fff;
  }
  .info-box {
    background-color: #f9fafb;
    padding: 0 12px;
    position: relative;
    & > .van-cell {
      background-color: #f9fafb;
    }
  }
  .title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
    text-align: center;
  }
  .footer {
    padding: 12px 12px 0 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .footer-button {
    width: 100%;
    padding: 11px 0;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    background-color: #4ca786;
    border-radius: 32px;
    text-align: center;
  }
  .create-button {
    padding: 10px;
    text-align: right;
    font-weight: 400;
    font-size: 14px;
    color: #fff;
    line-height: 20px;
    background-color: #4ca786;
    border-radius: 20px;
  }
  .tab-list {
    display: flex;
    align-items: center;
    padding: 12px;
    .tab-item {
      padding: 10px 15px;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      background-color: #dce0df;
      margin-right: 8px;
      border-radius: 20px;
      &.active {
        background-color: #4ca786;
      }
    }
  }
  .main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-top: 0;
    background-color: #fff;
  }
  .item {
    padding: 12px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: flex-start;
    .item-left {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      border-radius: 50%;
      background-color: #4ca786;
      color: #fff;
      font-size: 12px;
      transform: translateY(4px);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .item-right {
      flex: 1;
      overflow: hidden;
    }
  }

  .item-icon {
    font-size: 20px;
    margin-right: 4px;
  }

  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    flex: 1;
  }

  .item-img {
    width: 32px;
    height: 32px;
    overflow: hidden;
    margin-left: 17px;
  }

  .item-text {
    display: block;
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    flex: 1;
  }

  .text-value {
    display: block;
    margin-top: 8px;
  }
  .button-box {
    justify-content: space-between;
    padding: 12px;
  }
  .tip-text {
    font-size: 10px;
    color: #f53f3f;
  }
  .top-box {
    .item-title {
      line-height: 30px;
    }
  }
  .bg-white {
    background-color: #fff;
  }
  .weight-warning {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0;
    .weight-warning-title {
      font-size: 12px;
      margin-left: 10px;
    }
  }
  .item-weight {
    display: flex;
  }
  ::v-deep .van-search {
    padding-top: 0;
  }
  ::v-deep .van-search .van-search__content {
    background-color: #f3f4f6;
    border-radius: 6px;
  }
  ::v-deep .van-tabs--line .van-tabs__wrap {
    height: 32px;
  }
  ::v-deep .van-popover__action {
    font-size: 12px;
    width: 160px;
  }
</style>
