<template>
  <div>
    <div class="top-box" v-if="ifShowBox"></div>
    <div class="title-box">
      <svg class="title-icon mr-4" aria-hidden="true">
        <use xlink:href="#icon-ic_biaotizhuangshi"></use>
      </svg>
      <div class="title-text">{{ title }}</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "HeaderTitleIndex",
    props: {
      title: {
        type: String,
        default: "",
      },
      ifShowBox: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {};
    },

    mounted() {},

    methods: {},
  };
</script>
<style lang="scss" scoped>
  .title-box {
    border-top: 1px solid #ebebeb;
    display: flex;
    align-items: center;
    padding: 10px 6px;
    // background: linear-gradient(to bottom, #f1fdf9, #fff);
    background: #fff;
    .title-text {
      font-weight: 500;
      font-size: 16px;
      color: #4ca786;
    }
    .title-icon {
      width: 16px;
      height: 16px;
    }
  }
  .top-box {
    background: #fff;
    padding: 6px;
  }
</style>
