<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入车牌号/经办人名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listPageApi: "/api/vehicleDiscard/listPage",
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "operatorName",
            value: "经办人",
          },
          {
            key: "discardTime",
            value: "报废日期",
          },
        ],
        filterList: [
          {
            type: "Date",
            key: "discardBeginTime",
            value: "报废开始日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Date",
            key: "discardEndTime",
            value: "报废结束日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("discardRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
