<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="customerName"
    searchPlaceholder="请输入客商名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { VISIT_RECORD_TYPE, VISIT_HANDLE_STATUS } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "customerName",
            value: "客商名称",
          },
          {
            key: "followUpDate",
            value: "回访日期",
          },
          {
            key: "method",
            value: "回访方式",
            enums: VISIT_RECORD_TYPE,
          },
          {
            key: "personName",
            value: "回访人",
          },
          {
            key: "handlingStatus",
            value: "处理情况",
            enums: VISIT_HANDLE_STATUS,
          },
        ],
        apis: {
          listPage: "/api/followUpRecord/listPage",
        },
        filterList: [
          {
            type: "Options",
            key: "method",
            value: "回访方式",
            enums: VISIT_RECORD_TYPE,
          },
          {
            type: "Date",
            key: "followUpDate",
            value: "回访日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Options",
            key: "handlingStatus",
            value: "处理情况",
            enums: VISIT_HANDLE_STATUS,
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("visitRecordDetail", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
