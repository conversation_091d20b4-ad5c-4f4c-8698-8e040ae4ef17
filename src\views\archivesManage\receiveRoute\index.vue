<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    :ifShowMore="true"
    :listFieldArrOther="listFieldArrOther"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入路线名称/车牌号"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList/formIndex.vue";
  import { ROUTE_PROPERTY, ROUTE_STATUS } from "@/enums";
  import { getInfoApiFunByParams, getListApiFun } from "@/api/base";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "name",
            value: "路线名称",
          },
          {
            key: "code",
            value: "路线编号",
          },
          {
            key: "districtName",
            value: "所属区域",
          },
          {
            key: "type",
            value: "路线属性",
            enums: ROUTE_PROPERTY,
          },
          {
            key: "defaultVehiclePlateNumber",
            value: "默认车辆",
          },
          {
            key: "defaultDriverDossierName",
            value: "默认司机",
          },
          {
            key: "defaultDriverDossierPhone",
            value: "司机联系方式",
          },
        ],
        listFieldArrOther: [
          {
            key: "supercargoDossierOneName",
            value: "押运工",
          },
          {
            key: "supercargoDossierOnePhone",
            value: "押运工联系方式",
          },
          {
            key: "supercargoDossierTwoName",
            value: "押运工2",
          },
          {
            key: "supercargoDossierTwoPhone",
            value: "押运工2联系方式",
          },
          {
            key: "status",
            value: "路线状态",
            enums: ROUTE_STATUS,
          },
          {
            key: "pointNumber",
            value: "点位数量",
          },
        ],
        apis: {
          listPage: "/api/pickup/pickupPath/listPage",
          regionList: "/api/region/regionList",
          userList: "/api/baseuser/list",
        },
        filterList: [
          {
            type: "Options",
            key: "defaultDriverDossierId",
            value: "驾驶司机",
            keyword: "lgUnionId",
            valueKey: "fullName",
            enums: [],
          },
          {
            type: "Options",
            key: "type",
            value: "路线属性",
            enums: ROUTE_PROPERTY,
          },
          {
            type: "Options",
            key: "status",
            value: "路线状态",
            enums: ROUTE_STATUS,
          },
          {
            type: "Options",
            key: "districtId",
            value: "所属区域",
            keyword: "id",
            valueKey: "name",
            enums: [],
          },
        ],
      };
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFunByParams({ pid: "440100000000" }, this.apis.regionList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.filterList[3].enums = res[0].data;
        this.filterList[0].enums = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 查看详情
      itemClick(item) {
        this.$commonSkip("receiveRouteRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
