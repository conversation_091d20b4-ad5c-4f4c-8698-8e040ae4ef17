const TOKEN_KEY = "LOGAN-TOKEN-9e317bb4937c";
const UserInfoKey = "ASSISTANT_USER_INFO";
const MICRO_APP_PARAM = "MICRO-APP-PARAM-faf913f3a600";
const OCBS_URL_INFO = "OCBS_URL_INFO-wqewqe65165qw";
const CURRENT_LEVEL_KEY = "CURRENT-LEVEL";
const LEVEL_LIST_KEY = "LEVEL-LIST";
const DNA_KEY = "xcx_dna";
const CHANNEL_KEY = "xcx_channel";
const DNA_NAME = "dna_name";
const LEVEL_ID = "level_id";

export const getOcbsUrlInfo = () => {
  return window.localStorage.getItem(OCBS_URL_INFO);
};

export const setOcbsUrlInfo = (OcbsUrlInfo) => {
  window.localStorage.setItem(OCBS_URL_INFO, OcbsUrlInfo);
};
// export const getToken = () => {
//   return window.localStorage.getItem(TOKEN_KEY);
// };

export const setToken = (token) => {
  window.localStorage.setItem(TOKEN_KEY, token);
};
// 9月22日更新为避免影响其他系统，在AI助教PC端下 异步返回promise，其他环境同步返回token
export const getToken = () => {
  if (window.QCefClient) {
    return new Promise((resolve, reject) => {
      window.QCefQuery({
        request: "requestLoginInfo",
        onSuccess(response) {
          resolve("Bearer " + response);
        },
        onFailure(error_code, error_message) {
          reject({ error_code, error_message });
        },
      });
    });
  }
  return window.localStorage.getItem(TOKEN_KEY);
};
export const removeToken = () => {
  window.localStorage.removeItem(TOKEN_KEY);
};

export const getUserInfo = () => {
  let userInfo = window.localStorage.getItem(UserInfoKey);

  return userInfo ? JSON.parse(userInfo) : null;
};

export const setUserInfo = (userInfo) => {
  if (typeof userInfo !== "object") {
    console.error("用户信息只能为对象类型");
    return;
  }
  window.localStorage.setItem(UserInfoKey, JSON.stringify(userInfo));
};

export const removeUserInfo = () => {
  window.localStorage.removeItem(UserInfoKey);
};

export const setCurrentLevel = (currentLevel) => {
  window.localStorage.setItem(CURRENT_LEVEL_KEY, currentLevel);
};

export const getCurrentLevel = () => {
  return window.localStorage.getItem(CURRENT_LEVEL_KEY);
};

export const setLevelList = (levelList) => {
  window.localStorage.setItem(LEVEL_LIST_KEY, levelList);
};

export const getLevelList = () => {
  let levelList = window.localStorage.getItem(LEVEL_LIST_KEY);
  return levelList ? JSON.parse(levelList) : [];
};

export const clear = () => {
  removeToken();
  removeUserInfo();
};
export const clearAll = () => {
  removeToken();
  removeUserInfo();
  removeParam();
  // window.localStorage.clear();
};

export const setParam = (params) => {
  if (typeof params !== "object") {
    console.error("参数只能为对象类型");
    return;
  }
  window.sessionStorage.setItem(MICRO_APP_PARAM, JSON.stringify(params));
};

export const getParam = () => {
  let param = window.sessionStorage.getItem(MICRO_APP_PARAM);
  return param ? JSON.parse(param) : null;
};

export const removeParam = () => {
  window.sessionStorage.removeItem(MICRO_APP_PARAM);
};

export function removeLocalStorage(key) {
  //移除localStorage值
  return window.localStorage.removeItem(key);
}

export function setDna(value) {
  try {
    localStorage.setItem(DNA_KEY, value);
  } catch (e) {
    console.log("setItem error: ", e);
  }
}

export function getDna() {
  try {
    const value = localStorage.getItem(DNA_KEY);
    return value;
  } catch (e) {
    console.log("getItem error: ", e);
    return "";
  }
}

export function setChannel(value) {
  try {
    localStorage.setItem(CHANNEL_KEY, value);
  } catch (e) {
    console.log("setItem error: ", e);
  }
}

export function getChannel() {
  try {
    const value = localStorage.getItem(CHANNEL_KEY);
    return value;
  } catch (e) {
    console.log("getItem error: ", e);
    return "";
  }
}

export function setDnaName(value) {
  try {
    localStorage.setItem(DNA_NAME, value);
  } catch (e) {
    console.log("setItem error: ", e);
  }
}

export function getDnaName() {
  try {
    const value = localStorage.getItem(DNA_NAME);
    return value;
  } catch (e) {
    console.log("getItem error: ", e);
    return "";
  }
}

export function setLevelId(value) {
  try {
    localStorage.setItem(LEVEL_ID, value);
  } catch (e) {
    console.log("setItem error: ", e);
  }
}

export function getLevelId() {
  try {
    const value = localStorage.getItem(LEVEL_ID);
    return value;
  } catch (e) {
    console.log("getStorageSync error: ", e);
    return "";
  }
}
