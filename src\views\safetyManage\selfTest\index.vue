<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入车牌号/司机名称"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { SELFTEST_STATUS, EVALUATE_STATUS } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "driverName",
            value: "驾驶司机",
          },
          {
            key: "type",
            value: "自检类型",
            enums: SELFTEST_STATUS,
          },
          {
            key: "inspectTime",
            value: "自检时间",
          },
          {
            key: "status",
            value: "自检状态",
            enums: EVALUATE_STATUS,
          },
        ],
        listPageApi: "/api/vehicle/inspect/listPage",
        filterList: [
          {
            type: "Date",
            key: "inspectBeginTime",
            value: "自检开始日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Date",
            key: "inspectEndTime",
            value: "自检结束日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("selfTestRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
