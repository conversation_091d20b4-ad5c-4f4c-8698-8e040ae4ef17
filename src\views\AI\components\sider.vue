<template>
  <div class="sider-main">
    <div
      class="assistant-item flex-center"
      :class="checkId == item.id ? 'assistant-item-active' : ''"
      v-for="(item, index) in assistantItemList"
      :key="index"
      @click="handleCheck(item.id)"
    >
      <div class="icon flex-center">
        <i class="oIcon" :class="$iconName + item.icon"></i>
      </div>

      <span class="assistant-item-name">{{ item.name }}</span>
    </div>
  </div>
</template>

<script>
  export default {
    components: {},
    props: {},
    data() {
      return {
        assistantItemList: [
          { id: "xue<PERSON>", name: "学生学情", icon: "ic_nav_xueqing_24" },
          { id: "jiaoxuezhidao", name: "教学指导", icon: "ic_nav_jiaoan_24" },
          { id: "jiaoxueziyuan", name: "教学资源", icon: "ic_nav_jiaoxueziyuan_24" },
          { id: "beikejiaoan", name: "备课教案", icon: "ic_nav_beikejiaoan_24" },
        ],
        checkId: null,
      };
    },
    watch: {},
    computed: {},
    created() {},
    mounted() {
      this.checkId = "xueqing";
      this.$emit("changeSider", this.checkId);
    },
    methods: {
      handleCheck(id) {
        this.checkId = id;
        this.$emit("changeSider", this.checkId);
      },
    },
  };
</script>
<style lang="scss" scoped>
  .sider-main {
    width: 120px;
    height: 100vh;
    height: 100dvh;
    background: #ffffff;
    padding: 8px;
    overflow-y: auto;
  }
  .assistant-item {
    width: 100%;
    height: 40px;
    font-weight: 800;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #969799;
    margin-bottom: 8px;
    .icon {
      width: 32px;
      height: 32px;
    }
    .oIcon {
      font-size: 24px;
      font-weight: 500;
    }
    .smIcon {
      font-size: 20px;
    }
  }
  .assistant-item-active {
    color: $--color-primary !important;
  }
</style>
