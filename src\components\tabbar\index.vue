<template>
  <div>
    <van-tabbar :value="active" active-color="#162E25" inactive-color="#939C99">
      <van-tabbar-item v-for="(item, index) in tabList" :name="item.name" :key="index" :to="item.path" replace>
        <span>{{ item.text }}</span>
        <template #icon="props">
          <img :src="props.active ? item.active : item.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
    <div class="tabbar-height"></div>
  </div>
</template>

<script>
  export default {
    props: {
      active: {
        type: String,
        default: "home",
      },
    },
    data() {
      return {
        tabList: [
          {
            name: "home",
            text: "首页",
            path: "/home",
            inactive: require("@/assets/images/tabbar/ic_home.png"),
            active: require("@/assets/images/tabbar/ic_home_sel.png"),
          },
          {
            name: "workbench",
            text: "工作台",
            path: "/workbench",
            inactive: require("@/assets/images/tabbar/ic_workbench.png"),
            active: require("@/assets/images/tabbar/ic_workbench_sel.png"),
          },
          {
            name: "mine",
            text: "我的",
            path: "/mine",
            inactive: require("@/assets/images/tabbar/ic_mine.png"),
            active: require("@/assets/images/tabbar/ic_mine_sel.png"),
          },
        ],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .tabbar-height {
    height: 50px;
  }
</style>
