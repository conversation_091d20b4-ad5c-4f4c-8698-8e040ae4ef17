<template>
  <div class="AIAssistant-content flex-column">
    <div ref="listContainer" class="content-main">
      <div class="boxMessage" v-for="(item, index) in liaotian" :key="index">
        <!-- 导航 -->
        <div class="chat-view" v-if="item.template">
          <div class="avatar">
            <img src="@/assets/images/AIAssistant/头像_小乐AI助手.png" alt="" />
          </div>
          <div class="text-box">
            <span class="time">{{ item.time }}</span>
            <div class="chat-text xiaoLeVia-text">
              <h2>欢迎使用无废AI智能管家！</h2>
              <p>我是您的AI伙伴，专注于为您提供专业、高效的解答。</p>
              <div class="example">
                <p>关于收运平台业务运行情况，您可以这么问我</p>
                <div v-for="item in exmpleList" :key="item">
                  <p class="text-btn" @click="sendMessage(item)">{{ item }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 问 -->
        <div class="chat-view chat-view-my" v-if="item.customText">
          <div class="avatar">
            <img src="@/assets/images/AIAssistant/man.png" alt="" />
          </div>
          <div class="text-box">
            <span class="time">{{ item.time }}</span>
            <div class="chat-text teacher-text">
              <pre class="content-html">{{ item.customText }}</pre>
            </div>
            <div class="dialogue-btn mt-8 hover-btn">
              <span @click="anewEdit(item.customText)"
                ><img class="oIcon" src="@/assets/images/AIAssistant/ic_编辑2.png"
              /></span>
            </div>
          </div>
        </div>
        <!-- gtp回答 -->
        <div class="chat-view mt-12" v-if="item.text">
          <div class="avatar">
            <img src="@/assets/images/AIAssistant/头像_小乐AI助手.png" alt="" />
          </div>
          <div class="text-box">
            <span class="time">{{ item.time }}</span>
            <div class="chat-text xiaoLeVia-text">
              <pre v-html="item.text" class="content-html"></pre>
              <!-- <Table :tableList="item.tableList" v-if="item.tableList && item.tableList.length"></Table> -->
            </div>
            <div class="mt-8 flex-center-start">
              <div class="dialogue-btn">
                <span class="mr-8" @click="copyText(item.text)"
                  ><img class="oIcon mr-4" src="@/assets/images/AIAssistant/ic_复制.png" />复制</span
                >|<span class="ml-8" @click="reanswer(item.qurstionText)"
                  ><img class="oIcon mr-4" src="@/assets/images/AIAssistant/ic_重新回答.png" />重新回答</span
                >
              </div>
              <div class="dialogue-btn ml-16">
                <span class="mr-8" @click="item.ping = 1"
                  ><img v-if="item.ping == 1" class="oIcon" src="@/assets/images/AIAssistant/ic_好评.png" />
                  <img v-else class="oIcon" src="@/assets/images/AIAssistant/ic_好评_eee.png" /></span
                >|<span class="ml-8" @click="item.ping = 2"
                  ><img v-if="item.ping == 2" class="oIcon" src="@/assets/images/AIAssistant/ic_差评.png" />
                  <img v-else class="oIcon" src="@/assets/images/AIAssistant/ic_差评_eee.png"
                /></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 动态的字段显示，数据显示完了在显示真正的结果列表(类似障眼法) -->
      <div class="chat-view mt-12" v-if="isShow">
        <div class="avatar">
          <img src="@/assets/images/AIAssistant/头像_小乐AI助手.png" alt="" />
        </div>
        <div class="chat-text xiaoLeVia-text" v-if="stringData">
          <pre v-html="stringData" class="content-html"></pre>
        </div>
        <div class="content-html chat-text xiaoLeVia-text" v-else>
          <div class="step">
            <span style="--i: 1">正</span>
            <span style="--i: 2">在</span>
            <span style="--i: 3">思</span>
            <span style="--i: 4">考</span>
            <span style="--i: 5">，</span>
            <span style="--i: 6">请</span>
            <span style="--i: 7">稍</span>
            <span style="--i: 8">后</span>
            <span style="--i: 6">！</span>
            <span style="--i: 6">.</span>
            <span style="--i: 7">.</span>
            <span style="--i: 8">.</span>
          </div>
        </div>
      </div>
      <!-- 推荐模板-->
      <div class="recommended-template" v-if="showTemplate">
        <div class="template-title flex-center-between">
          <span>推荐模板</span>
          <i class="el-icon-close" @click="showTemplate = false"></i>
        </div>
        <div class="template-content">
          <span v-for="item in exmpleList" :key="item">
            <p class="mt-4" @click="clickTempla(item)">{{ item }}</p>
          </span>
        </div>
      </div>
    </div>
    <div class="content-bottom flex-column-center">
      <div class="input-field flex-center-between">
        <div class="input-field-left flex-center-start">
          <div
            class="inspiration-btn flex-center"
            slot="reference"
            :style="{ 'background-image': 'url(' + bgImg + ')' }"
            @click="showTemplate = true"
          >
            <img class="oIcon" src="@/assets/images/AIAssistant/ic_灵感.png" />灵感
          </div>
          <el-input
            type="textarea"
            placeholder="输入您的需求"
            v-model="textarea1"
            :autosize="{ minRows: 1, maxRows: 4 }"
            style="width: 100%"
            :disabled="isShow"
            resize="none"
            maxlength="200"
            show-word-limit
            @keydown.enter.native.prevent="handleEnterKey($event)"
          >
          </el-input>
        </div>
        <div class="input-field-right flex-center-end">
          <img class="oIcon" src="@/assets/images/AIAssistant/ic_发送.png" @click="sendMessage(textarea1)" />
        </div>
      </div>
      <span class="tips mt-12">所有内容均由AI生成，其内容无法保证真实准确，不代表我们的态度或观点，仅供参考。</span>
    </div>
  </div>
</template>

<script>
  import store from "@/store";
  // import Table from "./table.vue";
  import { getUserInfo, getToken } from "@/utils/storage";
  // import { fetchEventSource } from "@microsoft/fetch-event-source";

  export default {
    // components: { Table },
    props: {
      exmpleList: { type: Array, default: () => [] },
      AIlist: { type: Array, default: () => [] },
    },
    data() {
      return {
        bgImg: require(`@/assets/images/AIAssistant/bg_lingan.png`),
        textarea1: "",
        isShow: false,
        showTemplate: false, //模板开关
        isHideQrcode: false, //是否隐藏二维码
        stringData: "", //暂时字符
        userAvatar: "", //用户头像
        stringDataL: "",
        splfield: 0, // 控制第一次数据拼接的是text的字段，不然会少第一个字
        liaotian: [],
        userInfo: {}, //用户信息
        tableList: {}, //表格数据
      };
    },
    computed: {
      mqttMsg: () => store.getters.getMqtt,
    },
    watch: {
      mqttMsg(val) {
        try {
          let msg = JSON.parse(val.message);
          this.textarea1 = msg.payload.msgDatasDto.msgData.message;
          this.sendMessage(this.textarea1);
        } catch (error) {
          console.log(error);
        }
      },
      liaotian: {
        handler(val) {
          this.$emit("saveList", val);
        },
        deep: true,
      },
    },

    async created() {
      this.userInfo = getUserInfo();
      if (window.QCefClient) {
        this.mqttSubscribe(`assistant/${this.userInfo.lgUnionId}`);
      }
    },
    mounted() {
      if (this.$route.query.isHideQrcode) {
        this.isHideQrcode = this.$route.query.isHideQrcode;
      }
      this.liaotian = this.AIlist;
      if (!this.liaotian.length) {
        this.liaotian.push({ time: this.formatDate(), template: 1 });
      } else {
        this.scrollToBottom();
      }
      if (window.QCefClient) {
        window.QCefClient.addEventListener("AIAssistant", (data) => {
          const paramsObj = JSON.parse(data.message);
          // 判断如果输入的值是一样的则不做逻辑处理
          if (this.textarea1 == paramsObj.message) {
            return;
          }
          const paramsNewVal = { customText: paramsObj.message, time: this.formatDate() };
          this.liaotian.push(paramsNewVal);
          this.textarea1 = paramsObj.message;
        });
      }
    },
    methods: {
      clickTempla(item) {
        this.textarea1 = item;
        this.showTemplate = false;
      },
      /**
       * @description: 复制文本
       * @Date: 2024-04-16 19:23:40
       */
      copyText(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        this.$message.success("文本复制成功");
      },

      /**
       * @description: 重新回答
       * @Date: 2024-04-16 19:23:49
       */
      reanswer(questionText) {
        let params = { conversation_id: this.generateTimeId(), content: questionText };
        if (window.QCefClient) {
          params.dna = this.userInfo.dna;
          params.channelId = this.userInfo.channelId;
        } else {
          params.dna = this.$route.query.dna || "";
          params.channelId = this.$route.query.channelId || "";
        }
        this.chatProcess(params);
      },

      /**
       * @description: mqtt通讯
       * @Date: 2024-04-16 19:24:59
       */
      mqttSubscribe(topics) {
        if (window.QCefClient) {
          let jsonB = JSON.stringify({
            topics: [topics],
          });
          window.QCefClient.invokeMethod("mqttSubscribe", jsonB);
          console.log("订阅主题", jsonB);
        }
      },

      /**
       * @description: 重新编辑
       * @Date: 2024-04-13 17:05:23
       */
      anewEdit(str) {
        this.textarea1 = str;
      },
      /**
       * @description: 时间转换
       * @Date: 2024-04-13 16:05:06
       */
      formatDate() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份加1并补零
        const day = String(now.getDate()).padStart(2, "0"); // 补零
        const hours = String(now.getHours()).padStart(2, "0");
        const minutes = String(now.getMinutes()).padStart(2, "0");
        const seconds = String(now.getSeconds()).padStart(2, "0");
        return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
      },

      /**
       * @description: 限制输入事件
       * @Date: 2024-04-13 16:05:16
       */
      handleEnterKey(e) {
        if (e.shiftKey && e.keyCode == 13) {
          this.textarea1 += "\n";
        } else {
          if (/^\s+$/.test(this.textarea1)) {
            window.ELEMENT.Message.info("请输入内容进行提问");
            return;
          }
          this.sendMessage(this.textarea1);
        }
      },

      generateTimeId() {
        const now = Date.now().toString(); // 获取当前时间戳（毫秒）
        return now;
      },

      /**
       * @description: 发送数据
       * @Date: 2024-04-13 15:37:09
       */
      sendMessage(content) {
        try {
          if (!this.isShow) {
            if (!content) {
              window.ELEMENT.Message.info("请输入内容进行提问");
              return;
            }
            this.textarea1 = "";
            this.liaotian.push({ customText: content, time: this.formatDate() });

            let params = { conversation_id: this.generateTimeId(), content: content };
            if (window.QCefClient) {
              params.dna = this.userInfo.dna;
              params.channelId = this.userInfo.channelId;
            } else {
              params.dna = this.$route.query.dna || "";
              params.channelId = this.$route.query.channelId || "";
            }
            this.chatProcess(params);
            this.scrollToBottom();
          } else {
            window.ELEMENT.Message.info("请等待AI助手回复后再发送哦~");
          }
        } catch (error) {
          console.log("error", error);
        }
      },

      /**
       * @description: 流式接口方法
       * @Date: 2024-04-15 17:35:58
       */
      // 流式接口方法
      async chatProcess(params) {
        this.stringData = "";
        this.tableList = "";
        this.isShow = true;
        let shouldContinue = true; // 引入控制变量
        // let url = "http://*************:3640/api/v1/chat/stream";
        // let url = "http://************:8000/api/v1/chat/stream";
        // let url = "http://************:8000/api/v1/chat/stream"; // 豪哥电脑
        // let url = "http://************:8000/api/v1/chat/stream";
        let url = "http://*************:3666/api/v1/chat/stream";

        let baseConfig = {
          method: "POST",
          headers: {
            accept: "text/event-stream",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(params),
        };
        let token = await getToken();
        if (token) {
          baseConfig.headers.Authorization = "Bearer " + token;
        }
        let dataRes = await fetch(url, baseConfig);
        const reader = dataRes.body.getReader();
        let JSONObj = "";
        while (shouldContinue) {
          // 使用控制变量
          const textDecoder = new TextDecoder();
          const { done, value } = await reader.read();

          if (done) {
            shouldContinue = false; // 更新控制变量
            this.isShow = false;
            this.liaotian.push({
              qurstionText: params.content,
              time: this.formatDate(),
              text: this.stringData,
              // tableList: this.tableList,
              ping: "",
            });
            this.scrollToBottom();
          } else {
            const dstr = textDecoder.decode(value);
            // try {
            //   let strs = dstr.split("data:");
            //   for (let i = 0; i < strs.length; i++) {
            //     let str = strs[i];
            //     if (!str) continue;
            //     let info = JSON.parse(str);
            //     let text = info.content;
            //     if (info.table_data && info.table_data.length) {
            //       this.tableList = info.table_data;
            //     }
            //     this.stringData += String(text);
            //     this.scrollToBottom();
            //   }
            // } catch (error) {
            //   console.log("error", error);
            // }
            let strs = [];
            if (!dstr.startsWith("data:")) {
              JSONObj += dstr;
            } else {
              strs = dstr.split("data:");
            }
            if (JSONObj) {
              strs = JSONObj.split("data:");
            }
            // console.log(strs, "strs");
            for (let i = 0; i < strs.length; i++) {
              let str = strs[i];
              if (!str) continue;
              try {
                let info = JSON.parse(str);
                if (i == strs.length - 1) {
                  JSONObj = "";
                }
                let text = info.content;
                // if (info.table_data && info.table_data.length) {
                //   this.tableList = info.table_data;
                // }
                this.stringData += String(text);
                this.scrollToBottom();
              } catch (e) {
                JSONObj += dstr;
              }
              console.log(JSONObj, "JSONObj");
            }
          }
        }
      },
      // async chatProcess(params) {
      //   this.stringData = "";
      //   this.tableList = "";
      //   this.isShow = true;
      //   let url = "http://119.23.60.252:9001/api/v1/chat/stream";
      //   await fetchEventSource(url, {
      //     method: "POST",
      //     headers: {
      //       "Content-Type": "application/json",
      //     },
      //     body: JSON.stringify(params),

      //     async onmessage(msg) {
      //       const { data } = msg;
      //       if (data && JSON.parse(data)) {
      //         let strs = dstr.split("data:");
      //         for (let i = 0; i < strs.length; i++) {
      //           let str = strs[i];
      //           if (!str) {
      //             continue;
      //           }
      //           let info = JSON.parse(str); //拆除的数据转换成json格式
      //           let text = info.content;
      //           if (info.table_data && info.table_data.length) {
      //             this.tableList = info.table_data;
      //           }
      //           this.stringData += String(text);
      //           this.scrollToBottom();
      //         }
      //       }
      //     },

      //     onerror(err) {
      //       console.log("err-----------------", err);
      //       throw err;
      //     },
      //   });
      // },
      /**
       * @description: 优化AI点评数据进页面滚动到最底
       * @Date: 2024-04-13 15:37:51
       */
      scrollToBottom() {
        const container = this.$refs.listContainer;
        if (container) {
          setTimeout(() => {
            container.scrollTop = container.scrollHeight;
          }, 0);
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  .AIAssistant-content {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background: #ffffff;
    border-left: 1px solid #f6f7f8;
    .content-main {
      width: 100%;
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 24px 16px;
      position: relative;
      .avatar {
        border-radius: 50%;
        background: #eef6fe;
        margin: 0 15px;
        width: 32px;
        height: 32px;
        flex-shrink: 0;
        img {
          width: 100%;
          border-radius: 50%;
        }
      }
      .text-box {
        max-width: 100%;
      }
      .recommended-template {
        position: fixed;
        width: 50%;
        bottom: 110px;
        left: 130px;
        background: #ffffff;
        box-shadow: 0px 2px 24px 0px rgba(50, 50, 51, 0.1);
        border-radius: 8px;
        .template-title {
          padding: 11px 16px;
          font-size: 16px;
          font-weight: 600;
          border-bottom: 1px solid #f6f7f8;
        }
        .template-content {
          cursor: pointer;
          padding: 8px 16px;
          font-size: 14px;
          color: #828384;
          line-height: 30px;
          span:hover {
            color: #4ca786;
          }
        }
      }
      .chat-view-my {
        flex-direction: row-reverse;
        display: flex;
      }
      .chat-view {
        display: flex;
        width: 100%;
        margin: 0 auto 24px;
        .time {
          font-size: 12px;
          color: #969799;
          line-height: 17px;
        }
        .hover-btn {
          opacity: 0;
        }
        .text-box:hover .hover-btn {
          opacity: 1;
        }
        .dialogue-btn {
          cursor: pointer;
          background: #ffffff;
          box-shadow: 0px 1px 8px 0px rgba(50, 50, 51, 0.1);
          border-radius: 4px;
          padding: 6px 12px;
          font-size: 14px;
          display: inline-block;

          .chick-oIcon,
          span:hover {
            color: #4ca786;
          }
        }
        .chat-text {
          padding: 14px 16px;
          font-size: 14px;
          max-width: 100%;
          line-height: 28px;
          word-break: break-all;
        }
        .content-html {
          white-space: pre-wrap;

          .step span {
            position: relative;
            display: inline-block;
            color: #bfbfbf;
            font-size: 14px;
            animation: animate 1s ease-in-out infinite;
            animation-delay: calc(0.1s * var(--i));
          }
          @keyframes animate {
            0% {
              transform: translateY(0px);
            }
            20% {
              transform: translateY(-10px);
            }
            40%,
            100% {
              transform: translateY(0px);
            }
          }
        }

        .xiaoLeVia-text {
          border-radius: 8px;
          background-color: #f7f8fa;
          overflow: hidden;
          color: #171a1d;
          .example {
            padding-top: 16px;
            border-top: 1px solid #ebedf0;
            margin-top: 16px;
            .text-btn {
              cursor: pointer;
              color: #4ca786;
            }
          }
        }

        .teacher-text {
          border-radius: 8px;
          background-color: #4ca786;
          color: #ffffff;
        }

        .join-comment {
          width: 32px;
          height: 32px;
          margin: 0 0 0 8px;
          margin-top: auto;
          -webkit-user-select: none; /* Chrome, Safari, Opera */
          -moz-user-select: none; /* Firefox */
          -ms-user-select: none; /* Internet Explorer */
          user-select: none;
          cursor: pointer;
        }
      }
    }
    .content-bottom {
      width: 100%;
      min-height: 100px;
      padding: 12px;
      .input-field {
        width: 100%;
        min-height: 48px;
        background: #f7f8fa;
        border-radius: 24px;
        border: 1px solid #ebedf0;
        padding: 0 6px;
        display: flex;
        .input-field-left {
          flex: 1;
          padding-right: 10px;
          .inspiration-btn {
            cursor: pointer;
            width: 80px;
            height: 36px;
            color: #4ca786;
            font-size: 14px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }
        }
        .input-field-right {
          cursor: pointer;
          color: #c8c9cc;
          .assistant-icon {
            font-size: 24px;
            margin-right: 12px;
          }
        }
      }
      .tips {
        font-family: PingFangSC, PingFang SC;
        font-size: 12px;
        color: #c8c9cc;
        line-height: 17px;
      }
    }

    ::v-deep .el-textarea__inner {
      background: #f7f8fa;
      border: none;
    }

    .oIcon {
      width: 14px; /* 或者根据图片的实际宽度调整 */
      height: auto; /* 保持图片的宽高比 */
      vertical-align: middle; /* 使图片垂直居中，与原来的字体图标对齐方式一致 */
      margin-right: 4px; /* 根据 .mr-4 类可能提供的右边距 */
    }
    .boxMessage {
      margin: 0 auto;
      width: 100%;
    }
  }
</style>
