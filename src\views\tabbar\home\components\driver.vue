<template>
  <div class="container">
    <div class="list" v-if="taskList.length > 0">
      <div class="item" v-for="(item, index) in taskList" :key="index">
        <div class="item-top">
          <div class="flex-h" style="height: 32px">
            <div class="sctmp-iconfont icon-ic_yiyuan item-icon" style="color: #4e87fc"></div>
            <div class="item-title pi-one-line-clamp">{{ item.productionUnit }}</div>
            <img
              v-if="item.longitude && item.longitude != 0 && item.latitude && item.latitude != 0"
              class="item-img"
              src="@/assets/images/ic_luxian.png"
              @click="openMap(item)"
            />
          </div>
        </div>
        <div class="is-flex flex-align-end">
          <div class="item-text">
            <span class="text-value">所属路线名称：{{ item.name }}</span>
            <span class="text-value">地址：{{ item.address }}</span>
            <span class="text-value"
              >联系人：{{ item.productionUnitOperator }}&emsp;{{ item.productionUnitOperatorPhone }}</span
            >
            <span class="text-value" v-if="judgeDateAndAppointDate(item.effectiveDate)"
              >收运生效日期：{{ item.effectiveDate }}<span class="tip-text">（未到生效日期）</span></span
            >
            <span class="text-value">收运截止日期：{{ item.endDate }}</span>
          </div>
          <div
            class="item-prompt"
            @click="createWaybill(item)"
            v-if="userInfo?.lgUnionId === item.defaultDriverDossierId && !judgeDateAndAppointDate(item.effectiveDate)"
            >去确认</div
          >
        </div>
      </div>
    </div>
    <van-empty description="暂无数据" v-else />
  </div>
</template>

<script>
  import { getUserInfo } from "@/utils/storage";
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      currentLevel: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        taskList: [],
        userInfo: "",
      };
    },
    async created() {
      let userInfo = getUserInfo();
      if (userInfo) {
        let res = await createApiFun(
          {
            waybillStatus: 0,
          },
          "/api/waybill/commissionDetailList",
        );
        this.taskList = res.data.map((item) => {
          return {
            ...item,
            productionUnitOperatorPhone: item.productionUnitOperatorPhone
              ? this.$sm2Decrypt(item.productionUnitOperatorPhone)
              : "",
          };
        });
        this.userInfo = userInfo;
      } else {
        this.taskList = [];
        this.userInfo = "";
      }
    },
    methods: {
      initData() {},
      openMap() {},
      createWaybill(item) {
        let { id, productionUnit, productionUnitOperator, waybillType, address } = item;
        this.$commonSkip("electronicWaybillCreate", {
          id,
          productionUnit,
          productionUnitOperator,
          waybillType,
          address,
        });
      },
      judgeDateAndAppointDate(appointDate) {
        // 将日期字符串中的'-'替换为'/'
        let formattedDate = appointDate.replace(/-/g, "/");
        // 创建预约日期对象
        let otherDate = new Date(formattedDate);
        otherDate.setHours(0, 0, 0, 0);
        // 创建当前日期对象
        let nowDate = new Date();
        nowDate.setHours(0, 0, 0, 0);
        // 比较日期
        if (nowDate.getTime() < otherDate.getTime()) {
          return true;
        }
        return false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 0 12px;
  }

  .tab-list {
    margin-bottom: 10px;
  }

  .tab-item {
    margin-right: 8px;
    padding: 10px 15px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background-color: #dce0df;
    border-radius: 20px;
  }

  .tab-item.active {
    background-color: #4ca786;
  }

  .item {
    padding: 12px 0;
  }

  .item-icon {
    font-size: 20px;
    margin-right: 4px;
  }

  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    flex: 1;
  }

  .item-img {
    width: 32px;
    height: 32px;
    overflow: hidden;
    margin-left: 17px;
  }

  .item-text {
    display: block;
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    flex: 1;
  }

  .text-value {
    display: block;
    margin-top: 8px;
  }

  .item-prompt {
    font-weight: 400;
    font-size: 14px;
    color: #fff;
    line-height: 20px;
    padding: 6px 16px;
    border-radius: 16px;
    background-color: rgba(76, 167, 134, 1);
  }

  .tip-text {
    font-size: 10px;
    color: #f53f3f;
  }
</style>
