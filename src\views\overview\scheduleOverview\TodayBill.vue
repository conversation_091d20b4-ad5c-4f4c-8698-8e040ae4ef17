<template>
  <div class="ListItem-wrapper">
    <div class="title">今日收运单情况</div>
    <div class="sub-title">
      <div> 调度台账 </div>
      <div @click="clickMore" class="more">更多 ></div>
    </div>
    <div @click="itemClick(item)" class="list-item" v-for="item in list" :key="item.id">
      <div class="item-title">路线名称：{{ item.name }}</div>
      <div class="item-row">
        <div class="flex-item-text">路线编号：{{ item.code }}</div>
        <div class="flex-item-text">路线版本号：{{ item.versionNumber.toFixed(1) }}</div>
      </div>
      <div class="item-row">
        <div class="flex-item-text">路线状态：{{ ROUTE_STATUS[item.status] }}</div>
        <div class="flex-item-text">点位数量：{{ item.pointNumber }}</div>
      </div>
      <div class="item-row">
        <div class="flex-item-text">收运单生效日期：{{ item.effectiveDate }}</div>
      </div>
      <div class="item-row">
        <div class="flex-item-text">收运单编号：{{ item.waybillCode }}</div>
      </div>
      <div class="item-row">
        <div class="flex-item-text">所属区域：{{ item.districtName }}</div>
      </div>
      <div class="item-row">
        <div class="flex-item-text">路线属性：{{ ROUTE_PROPERTY[item.type] }}</div>
      </div>
      <div class="item-row">
        <div class="flex-item-text">默认车辆：{{ item.defaultVehiclePlateNumber }}</div>
      </div>
    </div>
    <van-empty description="暂无数据" v-if="list.length == 0" />
  </div>
</template>
<script>
  import { getListPageApiFun } from "@/api/base";
  import { ROUTE_STATUS, ROUTE_PROPERTY } from "@/enums";

  export default {
    components: {},
    data() {
      return {
        ROUTE_PROPERTY: ROUTE_PROPERTY,
        ROUTE_STATUS: ROUTE_STATUS,
        list: [],
        listPageApi: "/api/waybill/listPage",
      };
    },
    computed: {},
    created() {},
    mounted() {
      this.getList();
    },
    methods: {
      // 列表项点击事件
      itemClick(item) {
        let params = { waybillId: item.id };
        this.$commonSkip("dispatchLedgerRecord", params);
      },
      async getList() {
        let params = {
          pageNo: 1,
          pageSize: 2,
          issueStatus: 1,
        };
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          this.list = res.data.datas;
        } catch (error) {
          console.log(error);
        }
      },
      clickMore() {
        this.$commonSkip("dispatchLedger", {});
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .sub-title {
    display: flex;
    justify-content: space-between;
    padding-left: 12px;
    padding-right: 12px;
    line-height: 42px;
    font-size: 16px;
    color: #162e25;
    font-weight: bold;
  }
  .more {
    font-weight: 400;
    font-size: 14px;
    color: #939c99;
  }
  .ListItem-wrapper {
    margin-bottom: 12px;
    background-color: #fff;
    border-radius: 8px;
  }
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #4ca786;
    line-height: 40px;
    padding-left: 12px;
    background: linear-gradient(180deg, #eefcf7 0%, #ffffff 100%);
  }
  .label {
    padding: 0 12px 0 12px;
    display: flex;
    justify-content: space-between;
  }
  .listItem {
    padding: 0 12px 0 12px;
    display: flex;
    justify-content: space-between;
  }
  .listItem-item {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 40px;
  }
  .label-item {
    font-weight: bold;
    font-size: 14px;
    color: #162e25;
    line-height: 40px;
  }
  .list-item {
    padding: 12px;
    margin-bottom: 12px;
  }
  .item-title {
    font-weight: bold;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
  }
  .item-row {
    margin-top: 8px;
    display: flex;
  }
  .flex-item-text {
    flex: 1;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
  }
</style>
