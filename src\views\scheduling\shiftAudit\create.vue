<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <template v-if="$route.query.createTime">
              <van-field
                :value="$route.query.createTime"
                label="上报时间"
                placeholder="请输入上报时间"
                input-align="right"
                :border="false"
                readonly
              />
              <van-field
                :value="$route.query.deadline"
                label="截止日期"
                placeholder="请输入截止日期"
                input-align="right"
                :border="false"
                readonly
              />
            </template>
            <van-field
              :value="ruleForm.applyFullName"
              name="applyFullName"
              label="申请人名称"
              placeholder="请填写申请人名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              :value="ruleForm.applyPhone"
              name="applyPhone"
              label="申请人联系电话"
              placeholder="请填写申请人联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              readonly
              label-width="120"
            />
            <van-field
              :value="ruleForm.changeStartDate"
              name="changeStartDate"
              label="换班开始日期"
              placeholder="请选择换班开始日期"
              readonly
              input-align="right"
              error-message-align="right"
              :border="false"
              required
            />
            <van-field
              :value="ruleForm.changeEndDate"
              name="changeEndDate"
              label="换班结束日期"
              placeholder="请选择换班结束日期"
              readonly
              input-align="right"
              error-message-align="right"
              :border="false"
              required
            />
            <van-field name="remark" label="详情备注" label-width="100%" required :border="false" />
            <van-field
              :value="ruleForm.remark"
              name="remark"
              label=""
              placeholder="请填写详情备注"
              type="textarea"
              :border="false"
              :autosize="{ maxHeight: 200, minHeight: 100 }"
              :maxlength="800"
              show-word-limit
              readonly
            />
            <van-field
              label="是否批准换班"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              label-width="140"
            >
              <template #input>
                <el-switch
                  v-model="ruleForm.applyStatus"
                  active-text="是"
                  inactive-text="否"
                  active-color="#4CA786"
                  :active-value="1"
                  :inactive-value="0"
                >
                </el-switch>
              </template>
            </van-field>
            <van-field
              label="是否安排顶班人员"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              label-width="140"
              v-if="ruleForm.applyStatus === 1"
            >
              <template #input>
                <el-switch
                  v-model="ruleForm.isChange"
                  active-text="是"
                  inactive-text="否"
                  active-color="#4CA786"
                  :active-value="1"
                  :inactive-value="0"
                >
                </el-switch>
              </template>
            </van-field>
            <van-field
              v-model="ruleForm.changeFullName"
              name="changeUserId"
              label="顶班人员"
              placeholder="请选择顶班人员"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.changeUserId"
              required
              @click="showUserPicker = true"
              v-if="ruleForm.isChange === 1"
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-22"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >确认</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showUserPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="userOptions"
        @confirm="onUserConfirm"
        @cancel="showUserPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun, getListApiFun } from "@/api/base";
  export default {
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/waybill/change/auditChange",
          info: "/api/waybill/change/get/",
          userInfo: "/api/baseuser/getInfoByLgUnionId/",
          userList: "/api/baseuser/list",
        },
        ruleForm: {
          applyFullName: "", //申请人名称
          applyPhone: "", //申请人联系电话
          changeStartDate: "", //换班开始日期
          changeEndDate: "", //换班结束日期
          remark: "", //详情备注
          applyStatus: 0, //是否批准换班
          isChange: 0, //是否安排顶班人员
          changeUserId: "", //顶班人员
          changeFullName: "", //顶班人员
        },
        rules: {
          changeUserId: [{ required: true, message: "请选择顶班人员" }],
        },
        showUserPicker: false,
        userOptions: [],
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      async getRecord() {
        let res = await getInfoApiFun(this.$route.query.id, this.apis.info);
        let rsp = await getInfoApiFun("", this.apis.userInfo + res.data.applyUserId);
        this.ruleForm = res.data;
        let userIdentitys = rsp.data.userIdentity.split(",");
        let userData = [];
        if (userIdentitys.includes("3") && userIdentitys.includes("4")) {
          userData = await getListApiFun({ userIdentitys: [3, 4] }, this.apis.userList);
        } else {
          if (userIdentitys.includes("3")) {
            userData = await getListApiFun({ userIdentity: 3 }, this.apis.userList);
          } else if (userIdentitys.includes("4")) {
            userData = await getListApiFun({ userIdentity: 4 }, this.apis.userList);
          }
        }
        this.userOptions = userData?.data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      onUserConfirm(item) {
        this.ruleForm.changeUserId = item.lgUnionId;
        this.ruleForm.changeFullName = item.fullName;
        this.showUserPicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "审批成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
