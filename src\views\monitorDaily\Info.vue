<template>
  <div class="carCount-wrapper">
    <div class="card">
      <div class="card-item" v-for="item in itemList" :key="item.name">
        <div class="label">{{ item.name }}</div>
        <div class="value">{{ item.value }} {{ item.beside }}</div>
      </div>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";

  export default {
    components: {},
    props: {},
    data() {
      return {
        itemList: [
          { name: "超时未收运点位", value: "0", beside: "个" },
          { name: "新收运任务", value: "0", beside: "个" },
          { name: "异常上报数量", value: "0", beside: "个" },
          { name: "日常安全检查异常", value: "0", beside: "个" },
          { name: "今日出勤车辆", value: "0", beside: "辆" },
          { name: "今日空闲车辆", value: "0", beside: "辆" },
          { name: "车辆总数", value: "0", beside: "辆" },
        ],
        api: {
          getData: "/api/regulatory/baseInfo",
        },
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
    },
    methods: {
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          let summaryData = data.data;
          this.itemList[0].value = summaryData.overDueNum;
          this.itemList[1].value = summaryData.newCollectNum;
          this.itemList[2].value = summaryData.abnormalNum;
          this.itemList[3].value = summaryData.inspectionNum;
          this.itemList[4].value = summaryData.attendVehicles;
          this.itemList[5].value = summaryData.unAttendVehicles;
          this.itemList[6].value = summaryData.vehicleTotal;
        } catch (error) {
          console.log(error);
        }
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .carCount-wrapper {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    padding: 12px;
  }
  .card {
    background-color: #fff;
    width: 100%;
    border-radius: 8px;
  }
  .card-item {
    display: flex;
    justify-content: space-between;
  }
  .label {
    font-size: 14px;
    font-weight: 400;
    color: #162e25;
    line-height: 40px;
    margin-left: 12px;
  }
  .value {
    font-weight: 400;
    font-size: 14px;
    line-height: 40px;
    color: #5c6663;
    margin-right: 12px;
  }
</style>
