<template>
  <div>
    <van-field
      v-model="_value"
      :label="filterItem.value"
      :placeholder="`请选择${filterItem.value}`"
      input-align="right"
      is-link
      arrow-direction="down"
      label-width="120"
      readonly
      :border="false"
      @click="handleClick"
    />
    <van-popup v-model="showPicker" position="bottom">
      <van-datetime-picker
        type="year-month"
        v-model="currentDate"
        :min-date="filterItem.minDate"
        :max-date="filterItem.maxDate"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import componentMinxins from "./mixins";
  import moment from "moment";
  export default {
    mixins: [componentMinxins],
    data() {
      return {
        showPicker: false,
        currentDate: new Date(),
      };
    },
    mounted() {},
    methods: {
      // 点击
      handleClick() {
        this.showPicker = true;
        this.$nextTick(() => {
          let columns = this.$el.querySelectorAll(".van-picker__columns .van-picker-column");
          if (columns.length === 2) {
            columns[1].parentElement.removeChild(columns[1]);
          }
        });
      },
      // 确认
      onConfirm(value) {
        this._value = moment(value).format("YYYY");
        this.showPicker = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import "./common.scss";
</style>
