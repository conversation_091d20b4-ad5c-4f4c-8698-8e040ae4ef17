<template>
  <div class="carCount-wrapper">
    <div class="card">
      <div class="card-item" v-for="item in itemList" :key="item.name">
        <div class="label">{{ item.name }}</div>
        <div class="value">{{ item.value }} {{ item.beside }}</div>
      </div>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";

  export default {
    components: {},
    props: {},
    data() {
      return {
        api: {
          getData: "/api/integratedManagement/vehicle/summary",
        },
        itemList: [
          { name: "待保养车辆数量", value: "0", beside: "辆" },
          { name: "待二级维护车辆数量", value: "0", beside: "辆" },
          { name: "待投保车辆数量", value: "0", beside: "辆" },
          { name: "待处理违章事故数量", value: "0", beside: "辆" },
          { name: "过期车辆行驶证", value: "0", beside: "个" },
          { name: "过期车辆营运证", value: "0", beside: "个" },
          { name: "过期驾驶证", value: "0", beside: "个" },
          { name: "过期从业资格证", value: "0", beside: "个" },
        ],
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
    },
    methods: {
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          let summaryData = data.data;
          this.itemList[0].value = summaryData.vehicleMaintenance;
          this.itemList[1].value = summaryData.vehicleMaintenanceLevel2;
          this.itemList[2].value = summaryData.vehicleInsure;
          this.itemList[3].value = summaryData.vehicleViolationAccidents;

          this.itemList[4].value = summaryData.vehicleDrivingLicenseExpired;

          this.itemList[5].value = summaryData.vehicleOperationCertificateExpired;

          this.itemList[6].value = summaryData.driverLicenseExpired;

          this.itemList[7].value = summaryData.driverQualificationCertificateExpired;
        } catch (error) {
          console.log(error);
        }
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .carCount-wrapper {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    padding: 12px;
  }
  .card {
    background-color: #fff;
    width: 100%;
    border-radius: 8px;
  }
  .card-item {
    display: flex;
    justify-content: space-between;
  }
  .label {
    font-size: 14px;
    font-weight: 400;
    color: #162e25;
    line-height: 40px;
    margin-left: 12px;
  }
  .value {
    font-weight: 400;
    font-size: 14px;
    line-height: 40px;
    color: #5c6663;
    margin-right: 12px;
  }
</style>
