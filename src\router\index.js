import Vue from "vue";
import VueRouter from "vue-router";
import { setToken, setUserInfo, removeLocalStorage, setCurrentLevel, setLevelList, setLevelId } from "@/utils/storage";
import { TOKEN_KEY, UserInfoKey, LoginInfoKey, CURRENT_LEVEL_KEY, LEVEL_LIST_KEY } from "@/utils/commonValue";
import store from "@/store";
Vue.use(VueRouter);
const viewport = {
  content: "width=device-width, initial-scale=1.0, user-scalable=no",
};
const routes = [
  {
    path: "/refueling",
    name: "refueling",
    component: () => import("@/views/carManage/refueling/index"),
    meta: { title: "车辆加油记录台账", viewport: viewport },
  },
  {
    path: "/refuelingRecord",
    name: "refuelingRecord",
    component: () => import("@/views/carManage/refueling/record"),
    meta: { title: "车辆加油记录详情", viewport: viewport },
  },
  {
    path: "/refuelingCreate",
    name: "refuelingCreate",
    component: () => import("@/views/carManage/refueling/create"),
    meta: { title: "新增车辆加油记录", viewport: viewport },
  },
  {
    path: "/annualAudit",
    name: "annualAudit",
    component: () => import("@/views/carManage/annualAudit/index"),
    meta: { title: "车辆年审记录台账", viewport: viewport },
  },
  {
    path: "/annualAuditRecord",
    name: "annualAuditRecord",
    component: () => import("@/views/carManage/annualAudit/record"),
    meta: { title: "车辆年审记录详情", viewport: viewport },
  },
  {
    path: "/annualAuditCreate",
    name: "annualAuditCreate",
    component: () => import("@/views/carManage/annualAudit/create"),
    meta: { title: "新增车辆年审记录", viewport: viewport },
  },
  {
    path: "/evaluate",
    name: "evaluate",
    component: () => import("@/views/carManage/evaluate/index"),
    meta: { title: "车辆状态评价台账", viewport: viewport },
  },
  {
    path: "/evaluateRecord",
    name: "evaluateRecord",
    component: () => import("@/views/carManage/evaluate/record"),
    meta: { title: "车辆状态评价详情", viewport: viewport },
  },
  {
    path: "/evaluateCreate",
    name: "evaluateCreate",
    component: () => import("@/views/carManage/evaluate/create"),
    meta: { title: "新增车辆状态评价", viewport: viewport },
  },
  {
    path: "/insure",
    name: "insure",
    component: () => import("@/views/carManage/insure/index"),
    meta: { title: "车辆投保记录台账", viewport: viewport },
  },
  {
    path: "/insureRecord",
    name: "insureRecord",
    component: () => import("@/views/carManage/insure/record"),
    meta: { title: "车辆投保记录详情", viewport: viewport },
  },
  {
    path: "/insureCreate",
    name: "insureCreate",
    component: () => import("@/views/carManage/insure/create"),
    meta: { title: "新增车辆投保记录", viewport: viewport },
  },
  {
    path: "/violation",
    name: "violation",
    component: () => import("@/views/carManage/violation/index"),
    meta: { title: "车辆违章事故记录台账", viewport: viewport },
  },
  {
    path: "/violationRecord",
    name: "violationRecord",
    component: () => import("@/views/carManage/violation/record"),
    meta: { title: "车辆违章事故记录详情", viewport: viewport },
  },
  {
    path: "/violationCreate",
    name: "violationCreate",
    component: () => import("@/views/carManage/violation/create"),
    meta: { title: "新增车辆违章事故记录", viewport: viewport },
  },
  {
    path: "/discard",
    name: "discard",
    component: () => import("@/views/carManage/discard/index"),
    meta: { title: "车辆报废记录台账", viewport: viewport },
  },
  {
    path: "/discardRecord",
    name: "discardRecord",
    component: () => import("@/views/carManage/discard/record"),
    meta: { title: "车辆报废记录详情", viewport: viewport },
  },
  {
    path: "/discardCreate",
    name: "discardCreate",
    component: () => import("@/views/carManage/discard/create"),
    meta: { title: "新增车辆报废记录", viewport: viewport },
  },
  {
    path: "/vehicleMaintenance",
    name: "vehicleMaintenance",
    component: () => import("@/views/carManage/vehicleMaintenance/index"),
    meta: { title: "车辆维保记录台账", viewport: viewport },
  },
  {
    path: "/vehicleMaintenanceRecord",
    name: "vehicleMaintenanceRecord",
    component: () => import("@/views/carManage/vehicleMaintenance/record"),
    meta: { title: "车辆维保记录详情", viewport: viewport },
  },
  {
    path: "/vehicleMaintenanceCreate",
    name: "vehicleMaintenanceCreate",
    component: () => import("@/views/carManage/vehicleMaintenance/create"),
    meta: { title: "新增车辆维保记录", viewport: viewport },
  },
  {
    path: "/vehicles",
    name: "vehicles",
    component: () => import("@/views/archivesManage/vehicles/index"),
    meta: { title: "车辆档案台账", viewport: viewport },
  },
  {
    path: "/vehiclesRecord",
    name: "vehiclesRecord",
    component: () => import("@/views/archivesManage/vehicles/record"),
    meta: { title: "车辆档案详情", viewport: viewport },
  },
  {
    path: "/driver",
    name: "driver",
    component: () => import("@/views/archivesManage/driver/index"),
    meta: { title: "司机档案台账", viewport: viewport },
  },
  {
    path: "/driverRecord",
    name: "driverRecord",
    component: () => import("@/views/archivesManage/driver/record"),
    meta: { title: "司机档案详情", viewport: viewport },
  },
  {
    path: "/shipWorker",
    name: "shipWorker",
    component: () => import("@/views/archivesManage/shipWorker/index"),
    meta: { title: "押运工档案台账", viewport: viewport },
  },
  {
    path: "/shipWorkerRecord",
    name: "shipWorkerRecord",
    component: () => import("@/views/archivesManage/shipWorker/record"),
    meta: { title: "押运工档案详情", viewport: viewport },
  },
  {
    path: "/certificate",
    name: "certificate",
    component: () => import("@/views/archivesManage/certificate/index"),
    meta: { title: "证件管理台账", viewport: viewport },
  },
  {
    path: "/certificateRecord",
    name: "certificateRecord",
    component: () => import("@/views/archivesManage/certificate/record"),
    meta: { title: "证件管理详情", viewport: viewport },
  },
  {
    path: "/selfTest",
    name: "selfTest",
    component: () => import("@/views/safetyManage/selfTest/index"),
    meta: { title: "安全自检记录台账", viewport: viewport },
  },
  {
    path: "/selfTestRecord",
    name: "selfTestRecord",
    component: () => import("@/views/safetyManage/selfTest/record"),
    meta: { title: "安全自检记录详情", viewport: viewport },
  },
  {
    path: "/selfTestCreate",
    name: "selfTestCreate",
    component: () => import("@/views/safetyManage/selfTest/create"),
    meta: { title: "新增车辆安全自检记录", viewport: viewport },
  },
  {
    path: "/selfTestBeforeCreate",
    name: "selfTestBeforeCreate",
    component: () => import("@/views/safetyManage/selfTest/create"),
    meta: { title: "车前检填报", viewport: viewport },
  },
  {
    path: "/selfTestInCreate",
    name: "selfTestInCreate",
    component: () => import("@/views/safetyManage/selfTest/create"),
    meta: { title: "车中检填报", viewport: viewport },
  },
  {
    path: "/selfTestEdCreate",
    name: "selfTestEdCreate",
    component: () => import("@/views/safetyManage/selfTest/create"),
    meta: { title: "车后检填报", viewport: viewport },
  },
  {
    path: "/supervisoryTest",
    name: "supervisoryTest",
    component: () => import("@/views/safetyManage/supervisoryTest/index"),
    meta: { title: "监督抽查记录台账", viewport: viewport },
  },
  {
    path: "/supervisoryTestRecord",
    name: "supervisoryTestRecord",
    component: () => import("@/views/safetyManage/supervisoryTest/record"),
    meta: { title: "监督抽查记录详情", viewport: viewport },
  },
  {
    path: "/supervisoryTestCreate",
    name: "supervisoryTestCreate",
    component: () => import("@/views/safetyManage/supervisoryTest/create"),
    meta: { title: "", viewport: viewport },
  },
  {
    path: "/safeProduction",
    name: "safeProduction",
    component: () => import("@/views/safetyManage/safeProduction/index"),
    meta: { title: "安全生产记录台账", viewport: viewport },
  },
  {
    path: "/safeProductionRecord",
    name: "safeProductionRecord",
    component: () => import("@/views/safetyManage/safeProduction/record"),
    meta: { title: "安全生产记录详情", viewport: viewport },
  },
  {
    path: "/safeProductionCreate",
    name: "safeProductionCreate",
    component: () => import("@/views/safetyManage/safeProduction/create"),
    meta: { title: "新增安全生产记录", viewport: viewport },
  },
  {
    path: "/contractInfo",
    name: "contractInfo",
    component: () => import("@/views/contractManage/contractInfo/index"),
    meta: { title: "合同台账", viewport: viewport },
  },
  {
    path: "/contractInfoRecord",
    name: "contractInfoRecord",
    component: () => import("@/views/contractManage/contractInfo/record"),
    meta: { title: "合同详情", viewport: viewport },
  },
  {
    path: "/intelligentCollection",
    name: "intelligentCollection",
    component: () => import("@/views/archivesManage/intelligentCollection/index"),
    meta: { title: "智能收集柜档案", viewport: viewport },
  },
  {
    path: "/intelligentCollectionRecord",
    name: "intelligentCollectionRecord",
    component: () => import("@/views/archivesManage/intelligentCollection/record"),
    meta: { title: "智能收集柜详情", viewport: viewport },
  },
  {
    path: "/receiveRoute",
    name: "receiveRoute",
    component: () => import("@/views/archivesManage/receiveRoute/index"),
    meta: { title: "收运路线档案", viewport: viewport },
  },
  {
    path: "/receiveRouteRecord",
    name: "receiveRouteRecord",
    component: () => import("@/views/archivesManage/receiveRoute/record"),
    meta: { title: "收运路线详情", viewport: viewport },
  },
  {
    path: "/merchant",
    name: "merchant",
    component: () => import("@/views/archivesManage/merchant/index"),
    meta: { title: "客商档案", viewport: viewport },
  },
  {
    path: "/merchantRecord",
    name: "merchantRecord",
    component: () => import("@/views/archivesManage/merchant/record"),
    meta: { title: "客商档案详情", viewport: viewport },
  },
  {
    path: "/receiveShipPoint",
    name: "receiveShipPoint",
    component: () => import("@/views/archivesManage/receiveShipPoint/index"),
    meta: { title: "收运点位档案", viewport: viewport },
  },
  {
    path: "/receiveShipPointRecord",
    name: "receiveShipPointRecord",
    component: () => import("@/views/archivesManage/receiveShipPoint/record"),
    meta: { title: "收运点位详情", viewport: viewport },
  },
  {
    path: "/travel",
    name: "travel",
    component: () => import("@/views/carManage/travel/index"),
    meta: { title: "行驶记录台账", viewport: viewport },
  },
  {
    path: "/travelRecord",
    name: "travelRecord",
    component: () => import("@/views/carManage/travel/record"),
    meta: { title: "行驶记录详情", viewport: viewport },
  },
  {
    path: "/visitRecord",
    name: "visitRecord",
    component: () => import("@/views/serviceManage/visitRecord/index"),
    meta: { title: "回访记录台账", viewport: viewport },
  },
  {
    path: "/visitRecordDetail",
    name: "visitRecordDetail",
    component: () => import("@/views/serviceManage/visitRecord/record"),
    meta: { title: "回访记录详情", viewport: viewport },
  },
  {
    path: "/visitRecordCreate",
    name: "visitRecordCreate",
    component: () => import("@/views/serviceManage/visitRecord/create"),
    meta: { title: "填写回访登记表", viewport: viewport },
  },
  {
    path: "/complain",
    name: "complain",
    component: () => import("@/views/serviceManage/complain/index"),
    meta: { title: "投诉记录台账", viewport: viewport },
  },
  {
    path: "/complainRecord",
    name: "complainRecord",
    component: () => import("@/views/serviceManage/complain/record"),
    meta: { title: "投诉记录详情", viewport: viewport },
  },
  {
    path: "/complainCreate",
    name: "complainCreate",
    component: () => import("@/views/serviceManage/complain/create"),
    meta: { title: "填写投诉", viewport: viewport },
  },
  {
    path: "/serviceEvaluation",
    name: "serviceEvaluation",
    component: () => import("@/views/serviceManage/serviceEvaluation/index"),
    meta: { title: "服务评价记录台账", viewport: viewport },
  },
  {
    path: "/serviceEvaluationRecord",
    name: "serviceEvaluationRecord",
    component: () => import("@/views/serviceManage/serviceEvaluation/record"),
    meta: { title: "服务评价记录详情", viewport: viewport },
  },
  {
    path: "/serviceEvaluationCreate",
    name: "serviceEvaluationCreate",
    component: () => import("@/views/serviceManage/serviceEvaluation/create"),
    meta: { title: "填写服务评价", viewport: viewport },
  },
  {
    path: "/backlog",
    name: "backlog",
    component: () => import("@/views/transaction/backlog/index"),
    meta: { title: "待办事项", viewport: viewport },
  },
  {
    path: "/emergency",
    name: "emergency",
    component: () => import("@/views/transaction/emergency/index"),
    meta: { title: "告警通知", viewport: viewport },
  },
  {
    path: "/userInfo",
    name: "userInfo",
    component: () => import("@/views/userManage/userInfo/index"),
    meta: { title: "个人信息", viewport: viewport },
  },
  {
    path: "/electronicWaybill",
    name: "electronicWaybill",
    component: () => import("@/views/scheduling/electronicWaybill/index"),
    meta: { title: "电子收运单", viewport: viewport },
  },
  {
    path: "/electronicWaybillRecord",
    name: "electronicWaybillRecord",
    component: () => import("@/views/scheduling/electronicWaybill/record"),
    meta: { title: "电子收运单详情", viewport: viewport },
  },
  {
    path: "/electronicWaybillCreate",
    name: "electronicWaybillCreate",
    component: () => import("@/views/scheduling/electronicWaybill/create"),
    meta: { title: "填写电子收运单", viewport: viewport },
  },
  {
    path: "/exceptReport",
    name: "exceptReport",
    component: () => import("@/views/scheduling/exceptReport/index"),
    meta: { title: "异常上报管理", viewport: viewport },
  },
  {
    path: "/exceptReportRecord",
    name: "exceptReportRecord",
    component: () => import("@/views/scheduling/exceptReport/record"),
    meta: { title: "异常上报详情", viewport: viewport },
  },
  {
    path: "/exceptReportCreate",
    name: "exceptReportCreate",
    component: () => import("@/views/scheduling/exceptReport/create"),
    meta: { title: "异常上报", viewport: viewport },
  },
  {
    path: "/electronicWaybillAudit",
    name: "electronicWaybillAudit",
    component: () => import("@/views/scheduling/electronicWaybillAudit/index"),
    meta: { title: "电子收运单审核", viewport: viewport },
  },
  {
    path: "/electronicWaybillAuditList",
    name: "electronicWaybillAuditList",
    component: () => import("@/views/scheduling/electronicWaybillAudit/list"),
    meta: { title: "电子收运单审核", viewport: viewport },
  },
  {
    path: "/electronicWaybillAuditRecord",
    name: "electronicWaybillAuditRecord",
    component: () => import("@/views/scheduling/electronicWaybillAudit/record"),
    meta: { title: "电子收运单审核", viewport: viewport },
  },
  {
    path: "/dispatchLedger",
    name: "dispatchLedger",
    component: () => import("@/views/scheduling/dispatchLedger/index"),
    meta: { title: "电子收运单台账", viewport: viewport },
  },
  {
    path: "/dispatchLedgerRecord",
    name: "dispatchLedgerRecord",
    component: () => import("@/views/scheduling/dispatchLedger/record"),
    meta: { title: "电子收运单台账", viewport: viewport },
  },
  {
    path: "/initCollectCreate",
    name: "initCollectCreate",
    component: () => import("@/views/scheduling/initCollect/create"),
    meta: { title: "发起收运", viewport: viewport },
  },
  {
    path: "/myShift",
    name: "myShift",
    component: () => import("@/views/scheduling/shiftManage/myShift"),
    meta: { title: "我的换班", viewport: viewport },
  },
  {
    path: "/shift",
    name: "shift",
    component: () => import("@/views/scheduling/shiftManage/index"),
    meta: { title: "换班历史", viewport: viewport },
  },
  {
    path: "/shiftRecord",
    name: "shiftRecord",
    component: () => import("@/views/scheduling/shiftManage/record"),
    meta: { title: "换班详情", viewport: viewport },
  },
  {
    path: "/shiftCreate",
    name: "shiftCreate",
    component: () => import("@/views/scheduling/shiftManage/create"),
    meta: { title: "申请换班", viewport: viewport },
  },
  {
    path: "/shiftAudit",
    name: "shiftAudit",
    component: () => import("@/views/scheduling/shiftAudit/index"),
    meta: { title: "换班审批", viewport: viewport },
  },
  {
    path: "/shiftAuditCreate",
    name: "shiftAuditCreate",
    component: () => import("@/views/scheduling/shiftAudit/create"),
    meta: { title: "换班审批", viewport: viewport },
  },
  {
    path: "/bsChange",
    name: "bsChange",
    component: () => import("@/views/scheduling/bsChange/index.vue"),
    meta: { title: "经营状态变更", viewport: viewport },
  },
  {
    path: "/examineConfirm",
    name: "examineConfirm",
    component: () => import("@/views/performanceAppraisal/examineConfirm/index.vue"),
    meta: { title: "考核确认", viewport: viewport },
  },
  {
    path: "/examineConfirmCreate",
    name: "examineConfirmCreate",
    component: () => import("@/views/performanceAppraisal/examineConfirm/create.vue"),
    meta: { title: "考核确认", viewport: viewport },
  },
  {
    path: "/electronicWaybillAdjust",
    name: "electronicWaybillAdjust",
    component: () => import("@/views/scheduling/electronicWaybill/adjust"),
    meta: { title: "调整车辆", viewport: viewport },
  },
  {
    path: "/examineMark",
    name: "examineMark",
    component: () => import("@/views/performanceAppraisal/examineMark/index.vue"),
    meta: { title: "考核评分", viewport: viewport },
  },
  {
    path: "/examineMarkCreate",
    name: "examineMarkCreate",
    component: () => import("@/views/performanceAppraisal/examineMark/create.vue"),
    meta: { title: "考核评分", viewport: viewport },
  },
  {
    path: "/examineLedger",
    name: "examineLedger",
    component: () => import("@/views/performanceAppraisal/examineLedger/index.vue"),
    meta: { title: "考核结果", viewport: viewport },
  },
  {
    path: "/examineLedgerRecord",
    name: "examineLedgerRecord",
    component: () => import("@/views/performanceAppraisal/examineLedger/record.vue"),
    meta: { title: "考核结果", viewport: viewport },
  },
  {
    path: "/examineRank",
    name: "examineRank",
    component: () => import("@/views/performanceAppraisal/examineRank/index.vue"),
    meta: { title: "考核排名", viewport: viewport },
  },
  {
    path: "/monitorOverview",
    name: "monitorOverview",
    component: () => import("@/views/overview/monitorOverview/index.vue"),
    meta: { title: "监控总览", viewport: viewport },
  },
  {
    path: "/operateOverview",
    name: "operateOverview",
    component: () => import("@/views/overview/operateOverview/index.vue"),
    meta: { title: "运营总览", viewport: viewport },
  },
  {
    path: "/carCount",
    name: "carCount",
    component: () => import("@/views/carCount/index.vue"),
    meta: { title: "车辆统计", viewport: viewport },
  },
  {
    path: "/monitorDaily",
    name: "monitorDaily",
    component: () => import("@/views/monitorDaily/index.vue"),
    meta: { title: "监管日报", viewport: viewport },
  },
  {
    path: "/scheduleOverview",
    name: "scheduleOverview",
    component: () => import("@/views/overview/scheduleOverview/index.vue"),
    meta: { title: "调度总览", viewport: viewport },
  },
  {
    path: "/vehicleWeigh",
    name: "vehicleWeigh",
    component: () => import("@/views/carManage/vehicleWeigh"),
    meta: { title: "车辆称重台账", viewport: viewport },
  },
  {
    path: "/merchantWaybill",
    name: "merchantWaybill",
    component: () => import("@/views/scheduling/merchantWaybill"),
    meta: { title: "电子收运单", viewport: viewport },
  },
  {
    path: "/merchantWaybillRecord",
    name: "merchantWaybillRecord",
    component: () => import("@/views/scheduling/merchantWaybill/record"),
    meta: { title: "电子收运单详情", viewport: viewport },
  },
  {
    path: "/receiveShipPointCreate",
    name: "receiveShipPointCreate",
    component: () => import("@/views/archivesManage/receiveShipPoint/create"),
    meta: { title: "", viewport: viewport },
  },
  {
    path: "/aIAssistant",
    name: "aIAssistant",
    component: () => import("@/views/AI/index"),
    meta: { title: "无废AI智能管家", viewport: viewport },
  },
  {
    path: "/receiveRouteCreate",
    name: "receiveRouteCreate",
    component: () => import("@/views/archivesManage/receiveRoute/create"),
    meta: { title: "", viewport: viewport },
  },
  {
    path: "/dispatchLedgerTemporary",
    name: "dispatchLedgerTemporary",
    component: () => import("@/views/scheduling/dispatchLedger/temporary"),
    meta: { title: "临时调整", viewport: viewport },
  },
  {
    path: "/backlogRecord",
    name: "backlogRecord",
    component: () => import("@/views/transaction/backlog/record"),
    meta: { title: "", viewport: viewport },
  },
  {
    path: "/shiftNew",
    name: "shiftNew",
    component: () => import("@/views/scheduling/shiftManage/new"),
    meta: { title: "新增换班", viewport: viewport },
  },
  {
    path: "/overtimeApproval",
    name: "overtimeApproval",
    component: () => import("@/views/scheduling/overtimeApproval/index"),
    meta: { title: "加班台账", viewport: viewport },
  },
  {
    path: "/overtimeApproval",
    name: "overtimeApproval",
    component: () => import("@/views/scheduling/overtimeApproval/index"),
    meta: { title: "加班台账", viewport: viewport },
  },
  {
    path: "/overtimeApprovalRecord",
    name: "overtimeApprovalRecord",
    component: () => import("@/views/scheduling/overtimeApproval/record"),
    meta: { title: "加班台账详情", viewport: viewport },
  },
  {
    path: "/overtimeApprovalCreate",
    name: "overtimeApprovalCreate",
    component: () => import("@/views/scheduling/overtimeApproval/create"),
    meta: { title: "加班申请", viewport: viewport },
  },
  {
    path: "/myOvertime",
    name: "myOvertime",
    component: () => import("@/views/scheduling/myOvertime/index"),
    meta: { title: "我的加班", viewport: viewport },
  },
  {
    path: "/myOvertimeRecord",
    name: "myOvertimeRecord",
    component: () => import("@/views/scheduling/myOvertime/record"),
    meta: { title: "加班详情", viewport: viewport },
  },
  {
    path: "/visitRecordList",
    name: "visitRecordList",
    component: () => import("@/views/serviceManage/visitRecord/list"),
    meta: { title: "填写回访登记表", viewport: viewport },
  },
  {
    path: "/qrcode",
    name: "qrcode",
    component: () => import("@/views/qrcode/index"),
    meta: { title: "绑定点位", viewport: viewport },
  },
  {
    path: "/initShipTask",
    name: "initShipTask",
    component: () => import("@/views/scheduling/initShipTask/index"),
    meta: { title: "发起收运任务", viewport: viewport },
  },
  {
    path: "/noticeRecord",
    name: "noticeRecord",
    component: () => import("@/views/integrateManage/notice/record"),
    meta: { title: "公告详情", viewport: viewport },
  },
  {
    path: "/home",
    name: "home",
    component: () => import("@/views/tabbar/home/<USER>"),
    meta: { title: "首页", viewport: viewport },
  },
  {
    path: "/workbench",
    name: "workbench",
    component: () => import("@/views/tabbar/workbench/index"),
    meta: { title: "工作台", viewport: viewport },
  },
  {
    path: "/mine",
    name: "mine",
    component: () => import("@/views/tabbar/mine/index"),
    meta: { title: "我的", viewport: viewport },
  },
  {
    path: "/authority",
    name: "authority",
    component: () => import("@/views/authority/index"),
    meta: { title: "登录", viewport: viewport },
  },
  {
    path: "/role",
    name: "role",
    component: () => import("@/views/userManage/role/index"),
    meta: { title: "角色切换", viewport: viewport },
  },
  {
    path: "/nextMenus",
    name: "nextMenus",
    component: () => import("@/views/tabbar/workbench/nextMenus"),
    meta: { title: "", viewport: viewport },
  },
  {
    path: "/toolbox",
    name: "toolbox",
    component: () => import("@/views/tabbar/toolbox/index"),
    meta: { title: "全部工具", viewport: viewport },
  },
  {
    path: "/verificationPoints",
    name: "verificationPoints",
    component: () => import("@/views/scheduling/verificationPoints/index"),
    meta: { title: "已收运点位核对", viewport: viewport },
  },
];

const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => err);
};

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

const createRouter = () =>
  new VueRouter({
    mode: "hash",
    routes: routes,
  });

const router = createRouter();
router.beforeEach(async (to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta?.title || "";
  }
  if (to.meta.content) {
    let head = document.getElementByTagName("head");
    let meta = document.createElemnet("meta");
    meta.name = "viewport";
    meta.content = "width=device-width, initial-scale=1.0, user-scalable=no";
    head[0].appendChild(meta);
  }
  if (to.query.tokenData) {
    try {
      setToken(to.query.tokenData);
    } catch (error) {
      return;
    }
  }
  let userInfo = "";
  if (to.query.userInfo) {
    try {
      userInfo = JSON.parse(decodeURIComponent(to.query.userInfo));
      setUserInfo(userInfo);
    } catch (error) {
      userInfo = "";
    }
  }
  if (to.query.currentLevel) {
    try {
      setCurrentLevel(to.query.currentLevel);
    } catch (error) {
      return;
    }
  }
  if (to.query.levelId) {
    try {
      setLevelId(to.query.levelId);
    } catch (error) {
      return;
    }
  }
  if (to.query.levelList) {
    try {
      setLevelList(to.query.levelList);
    } catch (error) {
      return;
    }
  }
  if (to.query.clearAllStorage) {
    removeLocalStorage(TOKEN_KEY);
    removeLocalStorage(UserInfoKey);
    removeLocalStorage(LoginInfoKey);
    removeLocalStorage(CURRENT_LEVEL_KEY);
    removeLocalStorage(LEVEL_LIST_KEY);
  }
  store.dispatch("clearCancel");
  next();
  sessionStorage.setItem("router-path", to.path);
});

export default router;
