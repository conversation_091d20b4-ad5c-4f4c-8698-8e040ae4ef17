<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入车牌号/司机名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "driverName",
            value: "驾驶司机",
          },
          {
            key: "fuelTime",
            value: "加油/充电日期",
          },
        ],
        listPageApi: "/api/vehicleFuel/listPage",
        filterList: [
          {
            type: "Date",
            key: "fuelBeginTime",
            value: "加油/充电开始日期",
            minDate: new Date(moment().subtract(100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(),
          },
          {
            type: "Date",
            key: "fuelEndTime",
            value: "加油/充电结束日期",
            minDate: new Date(moment().subtract(100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("refuelingRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
