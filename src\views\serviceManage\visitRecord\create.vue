<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <FormTitle :title="'基础信息'" />
            <van-field
              v-model="ruleForm.customerName"
              name="customerName"
              label="客商名称"
              placeholder="请输入客商名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.customerName"
              required
              maxlength="20"
            />
            <van-field
              v-model="ruleForm.customerContactNumber"
              name="customerContactNumber"
              label="客商联系电话"
              placeholder="请输入客商联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.customerContactNumber"
              required
              maxlength="11"
            />
            <van-field
              v-model="ruleForm.customerEmail"
              name="customerEmail"
              label="客商电子邮件"
              placeholder="请输入客商电子邮件"
              input-align="right"
              error-message-align="right"
              :border="false"
              maxlength="20"
            />
            <van-field
              v-model="ruleForm.methodName"
              name="method"
              label="回访方式"
              placeholder="请选择回访方式"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.method"
              required
              @click="showMethodPicker = true"
            />
            <van-field
              v-model="ruleForm.personName"
              name="personName"
              label="回访人"
              placeholder="请输入回访人名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.personName"
              required
              maxlength="15"
            />
            <van-field
              v-model="ruleForm.followUpDate"
              name="followUpDate"
              label="回访日期"
              placeholder="请选择回访日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.followUpDate"
              required
              @click="openDatePicker('followUpDate')"
            />
            <div class="question-box"></div>
            <ul class="question-list" v-if="questionList.length > 0">
              <FormTitle :title="'回访登记表'" />
              <li class="question-item" v-for="(item, index) in questionList" :key="index">
                <van-field
                  :label="`${index + 1}.${item.textName}(${QUESTION_TYPE[item.type]})`"
                  :border="false"
                  label-width="100%"
                  :required="item.required"
                >
                </van-field>
                <div class="question-text-box" v-if="item.type === 1">
                  <van-field
                    v-model="item.textValue"
                    rows="5"
                    autosize
                    label=""
                    type="textarea"
                    maxlength="500"
                    show-word-limit
                    class="question-text"
                    :name="item.id"
                    :rules="[{ required: item.required, message: `${errorMessage[item.type]}` }]"
                  />
                </div>
                <van-field
                  v-else
                  label=""
                  :border="false"
                  label-width="0"
                  :name="item.id"
                  :rules="[{ required: item.required, message: `${errorMessage[item.type]}` }]"
                >
                  <template #input>
                    <van-radio-group v-model="item.textValue" checked-color="#4ca786" v-if="item.type === 2">
                      <van-radio
                        class="question-radio"
                        :name="child"
                        v-for="(child, childIndex) in item.options"
                        :key="childIndex"
                        >{{ child }}</van-radio
                      >
                    </van-radio-group>
                    <van-checkbox-group v-model="item.textValue" checked-color="#4ca786" v-else-if="item.type === 3">
                      <van-checkbox
                        class="question-checkbox"
                        shape="square"
                        :name="child"
                        v-for="(child, childIndex) in item.options"
                        :key="childIndex"
                        >{{ child }}</van-checkbox
                      >
                    </van-checkbox-group>
                  </template>
                </van-field>
              </li>
            </ul>
            <van-empty description="暂无登记表数据" v-else />
          </main>
        </div>
        <footer class="create-footer">
          <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="$commonBack()"
            >返回</van-button
          >
          <van-button
            native-type="button"
            class="round-22 ml-11"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showMethodPicker" position="bottom">
      <van-picker
        show-toolbar
        default-index="4"
        :columns="VISIT_RECORD_TYPE"
        @confirm="onMethodConfirm"
        @cancel="showMethodPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import moment from "moment";
  import FormTitle from "@/components/FormTitle";
  import { VISIT_RECORD_TYPE, VISIT_HANDLE_STATUS, QUESTION_TYPE } from "@/enums";
  export default {
    components: { FormTitle },
    data() {
      return {
        step: 0, //新增步骤
        submitFormThrottling: () => {},
        apis: {
          create: "/api/followUpRecord/save",
          form: "/api/followUpRecord/distribute/",
        },
        showMethodPicker: false,
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        VISIT_RECORD_TYPE,
        VISIT_HANDLE_STATUS,
        QUESTION_TYPE,
        ruleForm: {
          customerName: "", //客商名称
          customerContactNumber: "", //客商联系电话
          customerEmail: "", //客商电子邮件
          method: 4, //回访方式
          methodName: "小程序填写",
          personName: "", //回访人
          followUpDate: moment(new Date()).format("YYYY-MM-DD"), //回访日期
          handlingStatus: 0, //处理情况
        },
        rules: {
          customerName: [{ required: true, message: "请输入客商名称" }],
          customerContactNumber: [{ required: true, message: "请输入客商联系电话" }],
          method: [{ required: true, message: "请选择回访方式" }],
          personName: [{ required: true, message: "请输入回访人名称" }],
          followUpDate: [{ required: true, message: "请选择回访日期" }],
        },
        questionList: [],
        errorMessage: ["", "请输入内容", "请选择单选内容", "请选择多选内容"],
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getVisitForm();
    },
    methods: {
      // 获取已发布回访登记表
      async getVisitForm() {
        let res = await getInfoApiFun(this.$route.query.id, this.apis.form);
        if (res.success) {
          this.questionList = res.data.questions.map((item) => {
            return {
              id: item.id,
              registrationFormId: item.id,
              textName: item.name,
              textValue: item.type === 3 ? [] : "",
              type: item.type,
              options: item.optionList,
              required: item.required,
            };
          });
        }
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择自检类型
      onMethodConfirm(item, index) {
        this.ruleForm.methodName = item;
        this.ruleForm.method = index;
        this.showMethodPicker = false;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let infos = JSON.parse(JSON.stringify(this.questionList));
            infos.forEach((item) => {
              item.textValue = JSON.stringify(item.textValue);
            });
            let params = {
              distributeId: this.$route.query.id,
              registrationId: this.$route.query.registrationId,
              infos,
              ...this.ruleForm,
            };
            try {
              let res = await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "填写回访登记表成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .question-box {
    background: #f7faf9;
    padding: 6px;
  }
  .question-radio,
  .question-checkbox {
    margin-bottom: 10px;
  }
  .question-text {
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .question-text-box {
    padding: 10px 16px;
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
