<template>
  <div class="create-container">
    <van-index-bar :index-list="indexList">
      <div class="create-content">
        <van-index-anchor index="路线基础信息">
          <FormTitle :title="'路线基础信息'" />
        </van-index-anchor>
        <main class="create-main">
          <div class="record-item" v-for="(item, index) in baseList" :key="index">
            <div class="record-left">{{ item.name }}</div>
            <div class="record-right">{{ item.enums ? item.enums[ruleForm[item.key]] : ruleForm[item.key] }}</div>
          </div>
        </main>
      </div>
      <div class="create-content">
        <van-index-anchor index="车辆、人员信息">
          <FormTitle :title="'车辆、人员信息'" />
        </van-index-anchor>
        <main class="create-main">
          <div class="record-item" v-for="(item, index) in otherList" :key="index">
            <div class="record-left">{{ item.name }}</div>
            <div class="record-right">{{ item.enums ? item.enums[ruleForm[item.key]] : ruleForm[item.key] }}</div>
          </div>
        </main>
      </div>
      <div class="create-content">
        <van-index-anchor index="收运点位信息">
          <FormTitle :title="'收运点位信息'" />
        </van-index-anchor>
        <ul class="list" v-if="tableData.length > 0">
          <li class="list-item" v-for="(item, index) in tableData" :key="item.id">
            <div class="list-index">{{ index + 1 }}</div>
            <div class="list-content">
              <div class="item-title">点位名称：{{ item.name }}</div>
              <div class="item-text">点位编号：{{ item.code }}</div>
              <div class="item-text">点位类型：{{ POINT_TYPE[item.type] }}</div>
              <div class="item-text">点位收运方式：{{ POINT_RECEIVING_METHOD[item.baggingMethod] }}</div>
              <div class="item-text">点位地址：{{ item.address }}</div>
              <div class="item-text">联系人：{{ item.contact }}</div>
              <div class="item-text">联系方式：{{ item.contactPhone }}</div>
              <div class="item-text">收运周期：{{ COLLECTION_CYCLE[item.period] }}</div>
              <div class="item-text">收运频次：{{ item.frequency }}</div>
              <div class="item-text">经营状态：{{ REMOVE_STATUS[item.isUndock] }}</div>
            </div>
          </li>
        </ul>
        <van-empty description="暂无收运点位数据" v-else />
      </div>
    </van-index-bar>
    <div class="record-footer" v-if="ruleForm.status != 2">
      <van-button native-type="button" class="round-32" block type="info" color="#4CA786" @click="toEdit"
        >去编辑</van-button
      >
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import {
    ROUTE_STATUS,
    ROUTE_PROPERTY,
    POINT_TYPE,
    REMOVE_STATUS,
    COLLECTION_CYCLE,
    POINT_RECEIVING_METHOD,
  } from "@/enums";
  import FormTitle from "@/components/FormTitle";
  export default {
    components: { FormTitle },
    data() {
      return {
        id: "",
        step: 0, //新增步骤
        apis: {
          info: "/api/pickup/pickupPath/get/",
        },
        POINT_TYPE,
        REMOVE_STATUS,
        COLLECTION_CYCLE,
        POINT_RECEIVING_METHOD,
        titleList: [
          { sort: "①", name: "路线基础信息" },
          { sort: "②", name: "车辆、人员信息" },
          { sort: "③", name: "收运点位" },
        ],
        baseList: [
          { key: "name", name: "路线名称" },
          { key: "code", name: "路线编号" },
          { key: "status", name: "路线状态", enums: ROUTE_STATUS },
          { key: "districtName", name: "所属区域" },
          { key: "type", name: "路线属性", enums: ROUTE_PROPERTY },
        ],
        indexList: ["路线基础信息", "车辆、人员信息", "收运点位信息"],
        otherList: [
          { key: "defaultVehiclePlateNumber", name: "默认收运车辆" },
          { key: "defaultDriverDossierName", name: "默认驾驶司机" },
          { key: "defaultDriverDossierPhone", name: "司机联系方式" },
          { key: "supercargoDossierOneName", name: "默认押运工1" },
          { key: "supercargoDossierOnePhone", name: "押运工联系方式" },
          { key: "supercargoDossierTwoName", name: "默认押运工2" },
          { key: "supercargoDossierTwoPhone", name: "押运工联系方式" },
        ],
        ruleForm: {},
        tableData: [],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取收运路线档案详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.defaultDriverDossierPhone = this.ruleForm.defaultDriverDossierPhone
            ? this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone)
            : "";
          this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
            : "";
          this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
            : "";
          this.tableData = res.data.pickupPointList.map((item) => {
            return {
              ...item,
              contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
            };
          });
          this.tableData.forEach((list) => {
            for (let key in list) {
              list[key] = list[key] || list[key] === 0 ? list[key] : "-";
            }
          });
        }
      },
      // 切换tab栏
      toggleTab(index) {
        this.step = index;
      },
      toEdit() {
        this.$commonSkip("receiveRouteCreate", { id: this.id });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px 12px 0;
    ::v-deep .van-index-anchor {
      padding: 0;
    }
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
    padding: 0 12px;
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    word-break: break-all;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    .list-index {
      width: 18px;
      height: 18px;
      background-color: #4ca786;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      margin-right: 10px;
    }
    .list-content {
      flex: 1;
      overflow: hidden;
    }
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  .record-footer {
    width: 100%;
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
  ::v-deep .van-index-bar__sidebar {
    display: none;
  }
  ::v-deep .van-index-bar {
    flex: 1;
    overflow: auto;
  }
</style>
