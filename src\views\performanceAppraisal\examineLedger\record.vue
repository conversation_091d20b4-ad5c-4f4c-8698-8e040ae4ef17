<template>
  <div class="record-container" v-if="ruleForm && ruleForm.id">
    <template v-if="ruleForm.period === 1">
      <main class="record-main">
        <van-field
          :value="formItem.assessSchemeName"
          label="考核方案名称："
          input-align="right"
          :border="false"
          label-width="160"
          required
          readonly
        />
        <van-field
          :value="`${formItem.year}`"
          label="考核年度："
          input-align="right"
          :border="false"
          label-width="160"
          required
          readonly
        />
        <van-field
          :value="ruleForm.personList[0].fullName"
          label="被考核人员："
          input-align="right"
          :border="false"
          label-width="160"
          required
          readonly
        />
        <van-field
          :value="formItem.totalScore"
          label="总分："
          input-align="right"
          :border="false"
          label-width="160"
          readonly
        />
        <van-field
          :value="formItem.assessGradeName"
          label="考核等级："
          input-align="right"
          :border="false"
          label-width="160"
          readonly
        />
        <van-field
          :value="formItem.integrate"
          label="本年绩效积分："
          input-align="right"
          :border="false"
          label-width="160"
          readonly
        />
        <van-field
          :value="formItem.accumulativeIntegrate"
          label="累积绩效积分："
          input-align="right"
          :border="false"
          label-width="160"
          readonly
        />
        <van-field
          :value="formItem.payIncentives"
          label="年度奖金："
          input-align="right"
          :border="false"
          label-width="160"
          readonly
        />
        <van-field
          :value="formItem.deductSalary"
          label="扣款："
          input-align="right"
          :border="false"
          label-width="160"
          readonly
        />
        <van-field
          :value="formItem.completeDate"
          label="考核完成日期："
          input-align="right"
          :border="false"
          label-width="160"
          readonly
        />
      </main>
    </template>
    <template v-else>
      <van-tabs v-model="activeTab" color="#4ca786" title-active-color="#4ca786" sticky>
        <van-tab title="考核信息">
          <main class="record-main">
            <baseTitle title="基础信息">
              <template #right>
                <van-icon
                  :name="moreList[0] ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#909399"
                  @click="toggleMore(0)"
                />
              </template>
            </baseTitle>
            <div v-show="moreList[0]">
              <van-field
                :value="formItem.assessSchemeName"
                label="考核方案名称："
                input-align="right"
                :border="false"
                label-width="120"
                required
                readonly
              />
              <van-field
                :value="ruleForm.personList[0].fullName"
                label="被考核人员："
                input-align="right"
                :border="false"
                required
                readonly
              />
              <van-field
                :value="`${formItem.year}-${formItem.month >= 10 ? '' : '0'}${formItem.month}`"
                label="考核月度："
                input-align="right"
                :border="false"
                required
                readonly
              />
              <van-field :value="formItem.totalScore" label="总分：" input-align="right" :border="false" readonly />
              <van-field
                :value="formItem.assessGradeName"
                label="考核等级："
                input-align="right"
                :border="false"
                readonly
              />
              <van-field
                :value="formItem.completeDate"
                label="考核完成日期："
                input-align="right"
                :border="false"
                label-width="120"
                readonly
              />
            </div>
          </main>
          <template v-if="ruleForm.flowScoreList.length > 0">
            <div v-for="(item, index) in ruleForm.flowScoreList" :key="index">
              <div class="border-line"></div>
              <main class="record-main pb-16">
                <baseTitle :title="ASSESSMENT_FLOWS[item.flowCode]">
                  <template #right>
                    <van-icon
                      :name="item.more ? 'arrow-down' : 'arrow-up'"
                      size="20"
                      color="#909399"
                      @click="toggleFlowMore(item)"
                    />
                  </template>
                </baseTitle>
                <div v-show="item.more">
                  <van-field
                    :value="item.auditorName"
                    label="考核人员："
                    input-align="right"
                    :border="false"
                    required
                    readonly
                  />
                  <el-table :data="item.dimensionScoreList" :header-cell-style="{ background: '#F5F7F9' }" border>
                    <el-table-column prop="name" label="维度名称" align="center"></el-table-column>
                    <el-table-column prop="weight" label="维度满分" align="center"></el-table-column>
                    <el-table-column prop="score" label="打分" align="center"></el-table-column>
                  </el-table>
                </div>
              </main>
            </div>
          </template>
          <div class="border-line"></div>
          <main class="record-main pb-16">
            <baseTitle title="扣分项">
              <template #right>
                <van-icon
                  :name="moreList[1] ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#909399"
                  @click="toggleMore(1)"
                />
              </template>
            </baseTitle>
            <div v-show="moreList[1]">
              <el-table
                class="table-bottom"
                :data="ruleForm.demeritPoint"
                :header-cell-style="{ background: '#F5F7F9' }"
                border
              >
                <el-table-column prop="type" label="扣分项" align="center">
                  <template #default="{ row }">{{ DEDUCTION_ITEM[row.type] }}</template>
                </el-table-column>
                <el-table-column prop="deductSalary" label="扣除绩效工资金额" align="center"></el-table-column>
                <el-table-column prop="deductScore" label="扣除总分" align="center"></el-table-column>
                <el-table-column prop="memo" label="备注" align="center"></el-table-column>
              </el-table>
            </div>
          </main>
          <div class="border-line"></div>
          <main class="record-main">
            <baseTitle title="签字信息">
              <template #right>
                <van-icon
                  :name="moreList[2] ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#909399"
                  @click="toggleMore(2)"
                />
              </template>
            </baseTitle>
            <div v-show="moreList[2]">
              <van-field label="被考核人签字" :border="false" required label-width="100%" />
              <van-field class="sign-field" label="" :border="false" label-width="0">
                <template #input>
                  <img :src="ruleForm.signFile" alt="" class="sign-img" v-if="ruleForm.signFile" />
                  <van-empty description="暂无签名数据" v-else></van-empty>
                </template>
              </van-field>
            </div>
          </main>
        </van-tab>
        <van-tab title="绩效明细">
          <main class="record-main">
            <baseTitle title="绩效总览">
              <template #right>
                <van-icon
                  :name="moreList[3] ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#909399"
                  @click="toggleMore(3)"
                />
              </template>
            </baseTitle>
            <div class="pb-10" v-show="moreList[3]">
              <overview
                :detailItem="formItem"
                :performanceDetailJson="performanceDetailJson"
                :dataDetailFields="dataDetailFields"
              ></overview>
            </div>
            <div class="border-line"></div>
            <main class="create-main">
              <baseTitle title="绩效明细">
                <template #right>
                  <van-icon
                    :name="moreList[4] ? 'arrow-down' : 'arrow-up'"
                    size="20"
                    color="#909399"
                    @click="toggleMore(4)"
                  />
                </template>
              </baseTitle>
              <div class="pb-10" v-show="moreList[4]">
                <dataDetail
                  showTotal
                  :tableData="performanceDetailJson?.dataDetail"
                  :dataDetailFields="dataDetailFields"
                ></dataDetail>
              </div>
            </main>
          </main>
        </van-tab>
      </van-tabs>
    </template>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { EXAMINE_PERIOD, PERFORMANCE_RULE, DEDUCTION_ITEM, ASSESSMENT_FLOWS } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  import overview from "../examineMark/components/overview.vue";
  import dataDetail from "../examineConfirm/components/dataDetail.vue";
  export default {
    components: {
      baseTitle,
      overview,
      dataDetail,
    },
    data() {
      return {
        recordId: "",
        apis: {
          info: "/api/access/record/get/",
          calc: "/api/access/record/calc",
          get: "/api/assess/form/get/",
        },
        activeTab: 0,
        ruleForm: {},
        formItem: {},
        demeritPoint: [],
        EXAMINE_PERIOD,
        PERFORMANCE_RULE,
        DEDUCTION_ITEM,
        ASSESSMENT_FLOWS,
        moreList: [true, true, true, true, true],
        componentName: "",
        performanceDetailJson: {},
        dataDetailFields: [],
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 展开/收缩
      toggleMore(index) {
        this.moreList.splice(index, 1, !this.moreList[index]);
      },
      toggleFlowMore(item) {
        item.more = !item.more;
      },
      // 获取详情
      async getRecord() {
        let resp = await getInfoApiFun(this.$route.query.id, this.apis.info);
        if (resp.success) {
          this.ruleForm = resp.data;
          this.ruleForm.personList = [resp.data.user];
          this.ruleForm.demeritPoint = resp.data.demeritPoint || [];
          if (this.ruleForm.flowScoreList && this.ruleForm.flowScoreList.length > 0) {
            this.ruleForm.flowScoreList.forEach((list) => {
              this.$set(list, "more", true);
            });
          }
        }
        let res = await getInfoApiFun(this.$route.query.id, this.apis.get);
        if (res.success) {
          try {
            res.data.performanceDetailJson = JSON.parse(res.data.performanceDetailJson);
            res.data.clinicGroupParam = JSON.parse(res.data.clinicGroupParam);
          } catch (error) {
            res.data.performanceDetailJson = {};
            res.data.clinicGroupParam = {};
          }
          this.formItem = res.data;
          this.performanceDetailJson = res.data.performanceDetailJson;
          this.performanceDetailJson.dataDetail.forEach((list) => {
            for (let key in list) {
              this.dataDetailFields.push(key);
            }
          });
          this.dataDetailFields = [...new Set(this.dataDetailFields)];
          if (!this.formItem.assessGradeName) {
            this.formItem.assessGradeName = "--";
          }
          if (!this.formItem.completeDate) {
            this.formItem.completeDate = "--";
          }
          console.log("this.formItem ==> ", this.formItem);
          console.log("this.performanceDetailJson ==> ", this.performanceDetailJson);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background: #f0f2f1;
  }
  .record-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .sign-img {
    width: 100%;
    height: 300px;
    overflow: hidden;
  }
  .pb-16 {
    padding-bottom: 16px;
  }
  .pb-10 {
    padding-bottom: 10px;
  }
  ::v-deep .sign-field .van-field__control {
    width: 100%;
  }
  ::v-deep .van-empty {
    width: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
