<template>
  <div class="page-container">
    <main class="page-main">
      <ul class="list" v-if="tableData.length > 0">
        <li class="list-item" v-for="(item, index) in tableData" :key="index" @click="itemClick(item)">
          <div class="item-left">
            <div class="item-title">生效日期：{{ item.effectiveDate }}</div>
            <div class="item-text">任务总量：{{ item.num }}</div>
          </div>
          <div class="item-right">
            <span class="sctmp-iconfont icon-ic_xiazuan"></span>
          </div>
        </li>
      </ul>
      <van-empty description="暂无数据" v-else />
    </main>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  export default {
    data() {
      return {
        tableData: [],
        apis: {
          list: "/api/waybill/statisticsEffectiveDateList",
        },
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let res = await createApiFun({}, this.apis.list);
        if (res.success) {
          this.tableData = res.data;
        }
      },
      // 列表项点击事件
      itemClick(item) {
        this.$commonSkip("electronicWaybillAuditList", { effectiveDate: item.effectiveDate });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
</style>
