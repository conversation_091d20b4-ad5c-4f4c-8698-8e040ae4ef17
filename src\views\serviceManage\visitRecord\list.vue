<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入回访登记表名称"
    :hasRightIcon="false"
  >
    <template #right-button="{ item }">
      <van-button class="round-4" native-type="button" type="info" color="#4CA786" size="mini" @click="itemClick(item)"
        >去填写</van-button
      >
    </template>
  </pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "registrationName",
            value: "登记表名称",
          },
          {
            key: "publishTime",
            value: "下发时间",
          },
        ],
        apis: {
          listPage: "/api/followUpRecord/acquireDistribute",
        },
        filterList: [
          {
            type: "Date",
            key: "beginDistributeDate",
            value: "最近下发开始日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Date",
            key: "endDistributeDate",
            value: "最近下发结束日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("visitRecordCreate", { id: item.id, registrationId: item.registrationId });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .round-4 {
    border-radius: 4px;
  }
</style>
