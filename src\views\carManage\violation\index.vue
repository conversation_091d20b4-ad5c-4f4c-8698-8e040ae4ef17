<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入车牌号/司机名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listPageApi: "/api/vehicle/illegal/listPage",
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "driverName",
            value: "司机",
          },
          {
            key: "occurrenceTime",
            value: "发生时间",
          },
        ],
        filterList: [
          {
            type: "Time",
            key: "occurrenceBeginTime",
            value: "发生开始时间",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Time",
            key: "occurrenceEndTime",
            value: "发生结束时间",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("violationRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
