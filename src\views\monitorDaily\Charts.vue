<template>
  <div class="safe-wrapper">
    <div class="card">
      <div class="title">本年各月异常上报数量变化趋势</div>
      <div class="flex-wrapper">
        <div class="echarts-wrapper">
          <div class="echarts-wrapper" id="Charts1"> </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="title">本年各月日常安检异常数量变化趋势</div>
      <div class="flex-wrapper">
        <div class="echarts-wrapper">
          <div class="echarts-wrapper" id="Charts2"> </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="title">本年各月故障车辆数量变化趋势</div>
      <div class="flex-wrapper">
        <div class="echarts-wrapper">
          <div class="echarts-wrapper" id="Charts3"> </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";
  import * as echarts from "echarts";
  export default {
    components: {},
    props: {},
    data() {
      return {
        api: {
          getData: "/api/regulatory/trend/analysis",
        },
        summaryData: {},
        xAxisData: [],
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
      this.dataCb();

      this.init1();
      this.init2();
      this.init3();
    },
    methods: {
      getXAxisData() {
        // 获取当前日期
        const now = new Date();

        // 获取当前年份的当前月份（注意：月份是从0开始的，所以8月是7）
        const currentMonth = now.getMonth() + 1;

        // 使用Array.from()方法生成从1到当前月份的数组
        // 第二个参数是一个函数，它接受两个参数：index（当前索引）和array（当前数组，但在这个场景下我们不需要它）
        // 我们通过index+1来确保数组从1开始
        const monthsArray = Array.from({ length: currentMonth }, (_, index) => index + 1);
        this.xAxisData = monthsArray.map((item) => {
          return `${item}月`;
        });
      },
      dataCb() {
        this.getXAxisData();
        if (this.summaryData.abnormalSituation) {
          this.getSeriesData1();
        }
        if (this.summaryData.inspectSituation) {
          this.getSeriesData2();
        }
        if (this.summaryData.faultSituation) {
          this.getSeriesData3();
        }
      },
      getSeriesData1() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.abnormalSituation.forEach((item) => {
          let value = item.reportNum;
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData1 = mappedUndefinedArray;
      },
      getSeriesData2() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.inspectSituation.forEach((item) => {
          let value = item.inspectNum;
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData2 = mappedUndefinedArray;
      },
      getSeriesData3() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.faultSituation.forEach((item) => {
          let value = item.faultNum;
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData3 = mappedUndefinedArray;
      },
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          this.summaryData = data.data;
        } catch (error) {
          console.log(error);
        }
      },
      init1() {
        var myChart = echarts.init(document.getElementById("Charts1"));
        myChart.setOption({
          color: ["#006BF2"],
          tooltip: {
            show: false,
          },
          xAxis: {
            type: "category",
            data: this.xAxisData,
          },

          grid: {
            top: "15%",
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          yAxis: [
            {
              type: "value",
              axisLabel: {
                formatter: "{value} 个",
              },
            },
          ],
          series: [
            {
              silent: true, // 禁止点击事件
              smooth: true,
              name: "Charts1",
              type: "line",
              data: this.seriesData1,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: "RGBA(0, 107, 242, 0.2)",
                      },
                      {
                        offset: 1,
                        color: "RGBA(0, 107, 242, 0)",
                      },
                    ],
                    false,
                  ),
                  shadowColor: "RGBA(74, 155, 254, 0.2)",
                  shadowBlur: 20,
                },
              },
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}个", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
          ],
        });
      },
      init2() {
        var myChart = echarts.init(document.getElementById("Charts2"));
        myChart.setOption({
          color: ["#FF7D00"],
          tooltip: {
            show: false,
          },
          xAxis: {
            type: "category",
            data: this.xAxisData,
          },

          grid: {
            top: "15%",
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          yAxis: [
            {
              type: "value",
              axisLabel: {
                formatter: "{value} 个",
              },
            },
          ],
          series: [
            {
              silent: true, // 禁止点击事件
              smooth: true,
              name: "Charts2",
              type: "line",
              data: this.seriesData2,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: "RGBA(255, 129, 9, 0.2)",
                      },
                      {
                        offset: 1,
                        color: "RGBA(255, 129, 9, 0)",
                      },
                    ],
                    false,
                  ),
                  shadowColor: "RGBA(74, 155, 254, 0.2)",
                  shadowBlur: 20,
                },
              },
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}个", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
          ],
        });
      },
      init3() {
        var myChart = echarts.init(document.getElementById("Charts3"));
        myChart.setOption({
          color: ["#E72A2A"],
          tooltip: {
            show: false,
          },
          xAxis: {
            type: "category",
            data: this.xAxisData,
          },

          grid: {
            top: "15%",
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          yAxis: [
            {
              type: "value",
              axisLabel: {
                formatter: "{value} 个",
              },
            },
          ],
          series: [
            {
              silent: true, // 禁止点击事件
              smooth: true,
              name: "Charts3",
              type: "line",
              data: this.seriesData3,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: "RGBA(231, 42, 42, 0.2)",
                      },
                      {
                        offset: 1,
                        color: "RGBA(231, 42, 42, 0)",
                      },
                    ],
                    false,
                  ),
                  shadowColor: "RGBA(74, 155, 254, 0.2)",
                  shadowBlur: 20,
                },
              },
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}个", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
          ],
        });
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .safe-wrapper {
    padding: 12px 12px 1px 12px;
    background-color: #f7faf9;
    flex: 1;
    overflow: auto;
  }
  .title {
    font-weight: bold;
    font-size: 16px;
    line-height: 22px;
  }
  .card {
    background-color: #fff;
    padding: 10px 12px;
    margin-bottom: 12px;
  }
  .echarts-wrapper {
    position: relative;
    width: 100%;
    height: 236px;
  }
  .flex-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
</style>
