<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <FormTitle :title="'基础信息'" />
            <van-field
              v-model="ruleForm.customerName"
              name="customerName"
              label="客商名称"
              placeholder="请输入客商名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.customerName"
              required
              maxlength="20"
            />
            <van-field
              v-model="ruleForm.customerContactNumber"
              name="customerContactNumber"
              label="客商联系电话"
              placeholder="请输入客商联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.customerContactNumber"
              required
              maxlength="11"
            />
            <van-field
              v-model="ruleForm.customerEmail"
              name="customerEmail"
              label="客商电子邮件"
              placeholder="请输入客商电子邮件"
              input-align="right"
              error-message-align="right"
              :border="false"
              maxlength="20"
            />
            <van-field
              v-model="ruleForm.evaluationDate"
              name="evaluationDate"
              label="评价日期"
              placeholder="请选择评价日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.evaluationDate"
              required
              @click="openDatePicker('evaluationDate')"
            />
            <van-field
              v-model="ruleForm.returnDocumentNumber"
              name="returnDocumentNumber"
              label="回单据编号"
              placeholder="请输入回单据编号"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.returnDocumentNumber"
              required
              maxlength="20"
            />
            <FormTitle :title="'服务评价'" :ifShowBox="true" />
            <van-field name="star" label="评价等级" label-width="100%" :border="false" required />
            <van-rate
              class="ml-12"
              color="#ffd21e"
              v-model="ruleForm.star"
              :size="30"
              void-icon="star"
              void-color="#eee"
            />
            <van-field name="content" label="评价详情" label-width="100%" :border="false" required />
            <van-field
              v-model="ruleForm.content"
              name="content"
              label=""
              placeholder="请输入评价详情"
              type="textarea"
              :border="false"
              :autosize="{ maxHeight: 200, minHeight: 100 }"
              :maxlength="500"
              :rules="rules.content"
              show-word-limit
            />
            <FormTitle title="附件信息" :ifShowBox="true" />
            <van-field :border="false" label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">附件</div>
                  <div class="label-text">(请上传附件，支持图片、文档、PDF)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.fileList"
              :isRequired="false"
              :maxNumber="5"
              formId="fileList"
              acceptString=".png,.jpg,.jpeg,.gif,.doc,.docx,.pdf,.xls,.xlsx"
              formPlaceHolder="请上传附件"
            ></imageUpload>
          </main>
        </div>
        <footer class="create-footer">
          <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="$commonBack()"
            >返回</van-button
          >
          <van-button
            native-type="button"
            class="round-22 ml-11"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import FormTitle from "@/components/FormTitle";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  export default {
    components: { FormTitle, imageUpload },
    data() {
      return {
        step: 0, //新增步骤
        submitFormThrottling: () => {},
        apis: {
          create: "/api/serviceEvaluationRecord/save",
        },
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        ruleForm: {
          customerName: "", //客商名称
          customerContactNumber: "", //客商联系电话
          customerEmail: "", //客商电子邮件
          evaluationDate: moment(new Date()).format("YYYY-MM-DD"), //评价日期
          returnDocumentNumber: "", //回单据编号
          content: "", //评价内容
          star: null, //评价等级
          fileList: [], //附件列表
        },
        rules: {
          customerName: [{ required: true, message: "请输入客商名称" }],
          customerContactNumber: [{ required: true, message: "请输入客商联系电话" }],
          evaluationDate: [{ required: true, message: "请选择评价日期" }],
          returnDocumentNumber: [{ required: true, message: "请输入回单据编号" }],
          content: [{ required: true, message: "请输入评价详情" }],
          star: [{ required: true, message: "请选择评价等级" }],
        },
        validateList: [
          ["customerName", "customerContactNumber", "evaluationDate", "returnDocumentNumber"],
          ["content"],
        ],
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    methods: {
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 步骤点击事件
      handleStepClick() {
        this.$refs.formCreate
          .validate(this.validateList[this.step])
          .then(() => {
            this.step++;
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      // 提交
      onSubmit() {
        if (!this.ruleForm.star) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "评价等级不能为空",
            duration: 1500,
          });
          return;
        }
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "填写服务评价成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
