<template>
  <div class="detail-container" v-if="ruleForm">
    <van-field
      :value="PERFORMANCE_RULE[ruleIndex]"
      label="绩效方案名称："
      input-align="right"
      :border="false"
      required
      label-width="120px"
      readonly
    />
    <!-- <van-field value="" label="绩效计算公式：" input-align="right" :border="false" label-width="100%" readonly />
    <div class="formula"
      >（小型床位总收运点数 × 当月小型床位各点提成标准 + {{ ruleObj[ruleIndex] }}绩效工资标准 ×
      {{ ruleObj[ruleIndex] }}线路系数）</div
    >
    <van-field value="" label="桶装点位（小型床位）收运绩效总计（元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`${ruleForm.bucketCount} x ${ruleForm.bucketUnitPrice || 0} = ${ruleForm.bucketPrice}（元）`"
      label=""
      label-width="0"
      readonly
    />
    <van-field value="" label="袋装点位（小型床位）收运绩效总计（元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`${ruleForm.bagCount} x ${ruleForm.bagUnitPrice || 0} = ${ruleForm.bagPrice}（元）`"
      label=""
      label-width="0"
      readonly
    />
    <van-field
      :value="ruleForm.totalPoint"
      label="诊所点位总收运数（个）："
      input-align="right"
      :border="false"
      label-width="230"
      readonly
    />
    <van-field
      :value="ruleForm.factor"
      label="诊所线路系数："
      input-align="right"
      :border="false"
      label-width="120px"
      readonly
    />
    <van-field value="" label="点位绩效总计（元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`${ruleForm.pointPrice} + ${ruleForm.clinicGroupPrice} = ${roundReserveDecimals(
        floatAdd(ruleForm.pointPrice, ruleForm.clinicGroupPrice),
      )}`"
      label=""
      label-width="0"
      readonly
    /> -->
    <van-field value="" label="点位收运绩效明细：" input-align="right" :border="false" label-width="100%" readonly />
    <ul class="detail-list">
      <li class="detail-item" v-for="(item, index) in ruleForm.detailInfo" :key="index">
        <div class="detail-left">
          <div class="detail-title">日期</div>
          <div class="detail-value">{{ item.date }}</div>
        </div>
        <div class="detail-middle">
          <div class="middle-item">
            <div class="middle-item-title">桶装点位（小型床位）收运数量</div>
            <div class="middle-item-value">{{ item.bucketCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">袋装点位（小型床位）收运数量</div>
            <div class="middle-item-value">{{ item.bagCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">诊所点位收运数量</div>
            <div class="middle-item-value">{{ item.dayPoint }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">当月累计诊所点位收运数量</div>
            <div class="middle-item-value">{{ item.monthTotalPoint }}</div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { PERFORMANCE_RULE } from "@/enums";
  import { floatAdd, roundReserveDecimals } from "@/utils";
  export default {
    props: {
      detailId: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 5,
      },
    },
    data() {
      return {
        floatAdd,
        PERFORMANCE_RULE,
        roundReserveDecimals,
        apis: {
          info: "/api/access/record/detail/",
        },
        ruleForm: {},
        ruleObj: {
          5: "诊所组",
          6: "诊所组（南沙）",
          7: "特殊诊所组",
        },
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import "./detail.scss";
  .detail-middle {
    border-right: none;
  }
  .detail-list {
    max-height: 640px;
    overflow-y: auto;
  }
</style>
