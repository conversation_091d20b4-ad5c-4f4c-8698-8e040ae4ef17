<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入活动名称/负责人名称"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { SAFEPRODUCTION_TYPE } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "activityName",
            value: "活动名称",
          },
          {
            key: "personInCharge",
            value: "负责人",
          },
          {
            key: "type",
            value: "活动类型",
            enums: SAFEPRODUCTION_TYPE,
          },
          {
            key: "activeDate",
            value: "活动日期",
          },
          {
            key: "address",
            value: "活动地点",
          },
          {
            key: "number",
            value: "参与人数",
          },
        ],
        listPageApi: "/api/safety/safetyproduction/listPage",
        filterList: [
          {
            type: "Date",
            key: "activeBeginDate",
            value: "活动开始日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Date",
            key: "activeEndDate",
            value: "活动结束日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Options",
            key: "type",
            value: "活动类型",
            enums: SAFEPRODUCTION_TYPE,
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("safeProductionRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
