<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入车牌号"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import { OPERATIONAL_NATURE, CAR_STATUS, WORKING_STATUS } from "@/enums";
  import { getInfoApiFun } from "@/api/base";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "vehicleModelName",
            value: "车辆型号",
          },
          {
            key: "operationalNature",
            value: "营运性质",
            enums: OPERATIONAL_NATURE,
          },
          {
            key: "status",
            value: "车辆状态",
            enums: CAR_STATUS,
          },
          {
            key: "workingStatus",
            value: "工作状态",
            enums: WORKING_STATUS,
          },
        ],
        apis: {
          listPage: "/api/vehicle/dossier/listPage",
          vehicleModel: "/api/dict/vehicleModel/list", //车辆型号
        },
        filterList: [
          {
            type: "Options",
            key: "operationalNature",
            value: "营运性质",
            enums: OPERATIONAL_NATURE,
          },
        ],
      };
    },
    mounted() {
      this.getOptionsList();
    },
    methods: {
      // 获取数据列表
      async getOptionsList() {
        let promiseList = [getInfoApiFun("", this.apis.vehicleModel)];
        try {
          let res = await Promise.all(promiseList);
          this.filterList[1].enums = res[0].data;
        } catch (error) {
          console.warn(error);
        }
      },
      // 查看详情
      itemClick(item) {
        this.$commonSkip("vehiclesRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
