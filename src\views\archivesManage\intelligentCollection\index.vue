<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入设备名称/设备编号/地址"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import { CURRENT_STATUS, DEVICE_STATUS } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "deviceName",
            value: "设备名称",
          },
          {
            key: "runStatus",
            value: "运行状态",
            enums: CURRENT_STATUS,
          },
          {
            key: "deviceStatus",
            value: "设备状态",
            enums: DEVICE_STATUS,
          },
          {
            key: "deviceAddress",
            value: "设备地址",
          },
        ],
        apis: {
          listPage: "/api/holding/holdingTank/listPage",
        },
        filterList: [
          {
            type: "Options",
            key: "deviceStatus",
            value: "设备状态",
            enums: DEVICE_STATUS,
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("intelligentCollectionRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
