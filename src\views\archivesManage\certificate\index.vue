<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import { CERT_TYPES } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "certType",
            value: "证件类型",
            enums: CERT_TYPES,
          },
          {
            key: "fullName",
            value: "所属人员",
          },
          {
            key: "plateNumber",
            value: "所属车辆车牌号",
          },
          {
            key: "certNo",
            value: "证件号",
          },
        ],
        apis: {
          listPage: "/api/certificate/listPage",
        },
        filterList: [
          {
            type: "Options",
            key: "certType",
            value: "证件类型",
            enums: CERT_TYPES,
          },
          {
            type: "Input",
            key: "fullName",
            value: "所属人员",
          },
          {
            type: "Input",
            key: "plateNumber",
            value: "所属车辆车牌号",
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("certificateRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
