<template>
  <div class="create-container">
    <div class="create-box">
      <div class="create-content">
        <main class="create-main">
          <baseTitle title="基础信息">
            <template #right>
              <van-icon
                :name="moreList[0] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(0)"
              />
            </template>
          </baseTitle>
          <div v-show="moreList[0]">
            <van-field
              :value="recordForm.applyNumber"
              label="申请编号"
              placeholder="请输入申请编号"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              :value="recordForm.submitUserName"
              label="提交人"
              placeholder="请输入提交人名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              :value="recordForm.defaultVehiclePlateNumber"
              label="车牌号"
              placeholder="请输入车牌号"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              :value="recordForm.pointNumber"
              label="加班点位"
              placeholder="请输入加班点位"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              :value="recordForm.applyDate"
              label="加班日期"
              placeholder="请输入加班日期"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              label="加班类型"
              placeholder="请输入加班类型"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            >
              <template #input>
                <span v-for="(child, childIndex) in recordForm.type?.split(',')" :key="childIndex">
                  <span>{{ POINT_TYPE[child] }}</span>
                  <span v-if="childIndex != recordForm.type?.split(',')?.length - 1">,</span>
                </span>
              </template>
            </van-field>
            <van-field
              :value="recordForm.defaultDriverDossierName"
              label="司机姓名"
              placeholder="请输入司机姓名"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              :value="recordForm.supercargoDossierOneName || '无'"
              label="押运工1"
              placeholder="请输入押运工1姓名"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="recordForm.supercargoDossierTwoName || '无'"
              label="押运工2"
              placeholder="请输入押运工2姓名"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
            />
          </div>
        </main>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="收运点位信息">
            <template #right>
              <van-icon
                :name="moreList[1] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(1)"
              />
            </template>
          </baseTitle>
          <div v-show="moreList[1]">
            <ul class="list" v-if="recordForm.pointList?.length > 0">
              <li class="list-item" v-for="(item, index) in recordForm.pointList" :key="item.id">
                <div class="list-index">{{ index + 1 }}</div>
                <div class="list-content">
                  <div class="item-title">点位名称：{{ item.productionUnit }}</div>
                  <div class="item-text">点位编号：{{ item.code }}</div>
                  <div class="item-text">点位类型：{{ POINT_TYPE[item.type] }}</div>
                  <div class="item-text">点位收运方式：{{ POINT_RECEIVING_METHOD[item.baggingMethod] }}</div>
                  <div class="item-text">收运重量：{{ item.rubbishTotal }}</div>
                  <div class="item-img item-text">
                    <div>图片：</div>
                    <van-badge
                      :content="item.picture.length > 1 ? item.picture.length : ''"
                      v-if="item.picture.length > 0"
                    >
                      <van-image
                        width="60"
                        height="60"
                        :src="item.picture[0].url"
                        @click="imagePreview(item.picture)"
                      />
                    </van-badge>
                  </div>
                </div>
              </li>
            </ul>
            <van-empty description="暂无收运点位数据" v-else />
          </div>
        </main>
        <template v-if="recordForm.verifyStatus != 0">
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="审批信息">
              <template #right>
                <van-icon
                  :name="moreList[2] ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#909399"
                  @click="toggleMore(2)"
                />
              </template>
            </baseTitle>
            <div v-show="moreList[2]">
              <van-field
                :value="recordForm.verifyUserName"
                label="审批人"
                placeholder="请输入审批人"
                input-align="right"
                error-message-align="right"
                :border="false"
                readonly
                required
              />
              <van-field
                :value="recordForm.verifyTime"
                label="审批时间"
                placeholder="请输入审批时间"
                input-align="right"
                error-message-align="right"
                :border="false"
                readonly
                required
              />
              <div
                class="opinion-result"
                :class="{
                  'success-result': recordForm.verifyStatus == 1,
                  'danger-result': recordForm.verifyStatus == 2,
                }"
                >{{ recordForm.opinion }}</div
              >
              <div class="pb-10"></div>
            </div>
          </main>
        </template>
      </div>
    </div>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex"></van-image-preview>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { POINT_TYPE, OVERTIME_APPROVAL_STATUS, POINT_RECEIVING_METHOD } from "@/enums";
  export default {
    components: {
      baseTitle,
    },
    computed: {},
    data() {
      return {
        apis: {
          info: "/api/overtime/info/",
          verify: "/api/overtime/verify",
        },
        ruleForm: {
          opinion: "",
        },
        rules: {
          opinion: [{ required: true, message: "请输入审批意见" }],
        },
        recordForm: {},
        id: "",
        moreList: [true, true, true],
        POINT_TYPE,
        OVERTIME_APPROVAL_STATUS,
        POINT_RECEIVING_METHOD,
        previewImages: [],
        previewIndex: 0,
        showPreview: false,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 展开/收缩
      toggleMore(index) {
        this.moreList.splice(index, 1, !this.moreList[index]);
      },
      // 图片预览
      imagePreview(fileList) {
        this.previewImages = fileList.map((list) => list.url);
        this.previewIndex = 0;
        this.showPreview = true;
      },
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.recordForm = res.data;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .mt-12 {
    margin-top: 12px;
  }
  .delete-icon {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .pb-10 {
    padding-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .list {
    padding-bottom: 12px;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    .list-index {
      width: 18px;
      height: 18px;
      background-color: #4ca786;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      margin-right: 10px;
    }
    .list-content {
      flex: 1;
      overflow: hidden;
    }
    .delete-icon {
      position: absolute;
      top: 6px;
      right: 6px;
    }
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  .item-img {
    display: flex;
  }
  .opinion-result {
    padding: 12px;
    &.success-result {
      background-color: #f0fdf4;
      color: #3d9765;
    }
    &.danger-result {
      background-color: #f8c4c4;
      color: #dc2625;
    }
  }
  .pb-10 {
    padding-bottom: 10px;
  }
</style>
