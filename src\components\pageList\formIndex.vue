<template>
  <div class="page-container">
    <header class="page-header">
      <div class="header-input">
        <van-field
          v-model="keyword"
          label=""
          :placeholder="searchPlaceholder"
          right-icon="search"
          clearable
          @change="onRefresh"
          @clear="onRefresh"
        >
          <template #right-icon>
            <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
          </template>
        </van-field>
      </div>
      <div class="filter-button" @click="showPop = !showPop">
        <span class="filter-title">筛选</span>
        <span class="sctmp-iconfont icon-ic_xia filter-icon" v-show="!showPop"></span>
        <span class="sctmp-iconfont icon-ic_shang filter-icon" v-show="showPop"></span>
      </div>
    </header>
    <div class="filter-box" v-show="showPop">
      <template v-if="filterList.length > 0">
        <div v-for="(filterItem, filterIndex) in filterList" :key="filterIndex">
          <component
            :ref="filterItem.type"
            :is="filterItem.type"
            v-model="filterForm[filterItem.key]"
            labelWidth="150"
            :filterItem="filterItem"
          />
        </div>
        <div class="filter-bottom">
          <div class="reset-button" @click="resetList">重置</div>
          <div class="search-button" @click="searchList">查询</div>
        </div>
      </template>
    </div>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index" @click="itemClick(item)">
              <div class="item-content">
                <div class="item-left">
                  <div
                    :class="[arrIndex === 0 ? 'item-title' : 'item-text']"
                    v-for="(arr, arrIndex) in listFieldArr"
                    :key="arrIndex"
                  >
                    <template
                      v-if="
                        arr.connectField && arr.connectValue
                          ? arr.connectValue.includes(arr[arr.connectField])
                          : arr.connectField
                          ? item[arr.connectField]
                          : true
                      "
                    >
                      {{ arr.value }}：
                      <template v-if="Object.prototype.toString.call(arr.enums) === '[object Object]'">{{
                        arr.enums[item[arr.key]] || "-"
                      }}</template>
                      <template v-else-if="arr.enums && arr.enums.length > 0">
                        <template v-if="arr.isMultiple">
                          <span
                            v-for="(child, childIndex) in Array.isArray(item[arr.key])
                              ? item[arr.key]
                              : item[arr.key].split(',')"
                            :key="childIndex"
                          >
                            <span>{{ arr.enums[child] || "-" }}</span>
                            <span
                              v-if="
                                childIndex <
                                (Array.isArray(item[arr.key])
                                  ? item[arr.key].length - 1
                                  : item[arr.key].split(',').length - 1)
                              "
                              >、</span
                            >
                          </span>
                        </template>
                        <template v-else>{{ arr.enums[item[arr.key]] || "-" }}</template>
                      </template>
                      <template v-else-if="arr.slot">
                        <slot :name="arr.slot" v-bind="item"></slot>
                      </template>
                      <template v-else>{{ item[arr.key] || item[arr.key] === 0 ? item[arr.key] : "-" }}</template>
                    </template>
                  </div>
                </div>
                <div class="item-right" v-if="hasRightIcon">
                  <span class="sctmp-iconfont icon-ic_xiazuan"></span>
                </div>
              </div>
              <div v-if="ifShowMore">
                <div class="item-left" v-if="item.ifMoreIcon">
                  <div class="item-text" v-for="(arr, arrIndex) in listFieldArrOther" :key="arrIndex">
                    <template
                      v-if="
                        arr.connectField && arr.connectValue
                          ? arr.connectValue.includes(arr[arr.connectField])
                          : arr.connectField
                          ? item[arr.connectField]
                          : true
                      "
                    >
                      {{ arr.value }}：
                      <template v-if="Object.prototype.toString.call(arr.enums) === '[object Object]'">{{
                        arr.enums[item[arr.key]] || "-"
                      }}</template>
                      <template v-else-if="arr.enums && arr.enums.length > 0">
                        <template v-if="arr.isMultiple">
                          <span
                            v-for="(child, childIndex) in Array.isArray(item[arr.key])
                              ? item[arr.key]
                              : item[arr.key].split(',')"
                            :key="childIndex"
                          >
                            <span>{{ arr.enums[child] || "-" }}</span>
                            <span
                              v-if="
                                childIndex <
                                (Array.isArray(item[arr.key])
                                  ? item[arr.key].length - 1
                                  : item[arr.key].split(',').length - 1)
                              "
                              >、</span
                            >
                          </span>
                        </template>
                        <template v-else>{{ arr.enums[item[arr.key]] || "-" }}</template>
                      </template>
                      <template v-else-if="arr.slot">
                        <slot :name="arr.slot" v-bind="item"></slot>
                      </template>
                      <template v-else>{{ item[arr.key] || item[arr.key] === 0 ? item[arr.key] : "-" }}</template>
                    </template>
                  </div>
                </div>
                <div class="flex-center footer-btn">
                  <div v-if="!item.ifMoreIcon" class="more-btn" @click.stop="item.ifMoreIcon = true">
                    <van-icon name="arrow-down" class="mr-2" />展开
                  </div>
                  <div v-else class="more-btn" @click.stop="item.ifMoreIcon = false">
                    <van-icon name="arrow-up" class="mr-2" />收起
                  </div>
                </div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>

<script>
  import components from "./componentImport";
  import { getListPageApiFun } from "@/api/base";
  export default {
    props: {
      // 分页列表搜索keyword关键字
      searchField: {
        type: String,
        default: "keyword",
      },
      // 搜索框placeholder
      searchPlaceholder: {
        type: String,
        default: "",
      },
      // 是否需要展示右箭头icon
      hasRightIcon: {
        type: Boolean,
        default: true,
      },
      // 分页列表接口
      listPageApi: {
        type: String,
        default: "",
      },
      // 筛选列表
      filterList: {
        type: Array,
        default: () => [],
      },
      // 列表字段数组
      listFieldArr: {
        type: Array,
        default: () => [],
      },
      // 其他参数
      otherParams: {
        type: Object,
        default: () => {},
      },
      // 更多信息
      listFieldArrOther: {
        type: Array,
        default: () => [],
      },
      // 是否需要展示更多icon
      ifShowMore: {
        type: Boolean,
        default: false,
      },
      // getList前的回调
      myExtraParamsCallBack: {
        type: Function,
        require: false,
        default: (val) => {
          return val;
        },
      },
    },
    components: components,
    data() {
      return {
        showPop: false,
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        tableList: [],
        filterForm: {},
        keyword: "",
        smKey: [
          "phone",
          "idCard",
          "contactPhone",
          "productionUnitOperatorPhone",
          "phoneNumber",
          "defaultDriverDossierPhone",
          "supercargoDossierOnePhone",
          "supercargoDossierTwoPhone",
        ],
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 切换筛选弹窗
      togglePop() {
        this.showPop = !this.showPop;
      },
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          ...this.filterForm,
          ...this.otherParams,
        };
        params[this.searchField] = this.keyword;
        params = this.myExtraParamsCallBack(params);
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas.map((item) => {
              for (let key in item) {
                if (this.smKey.includes(key)) {
                  item[key] = item[key] ? this.$sm2Decrypt(item[key]) : "";
                }
              }
              return item;
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
            if (this.ifShowMore) {
              this.tableList = this.tableList.map((item) => {
                return {
                  ...item,
                  ifMoreIcon: false,
                };
              });
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 列表项点击事件
      itemClick(item) {
        this.$emit("itemClick", item);
      },
      // 重置
      resetList() {
        this.filterForm = {};
        this.onRefresh();
        this.togglePop();
      },
      // 查询
      searchList() {
        this.onRefresh();
        this.togglePop();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
  }
  .header-input {
    flex: 1;
    overflow: hidden;
  }
  .filter-button {
    display: flex;
    align-items: center;
    margin-left: 16px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
      margin-right: 2px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
      margin-top: 2px;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .filter-box {
    background-color: #fff;
  }
  .filter-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    margin-bottom: 16px;
  }
  .reset-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #dce0df;
    border-radius: 20px;
  }
  .search-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #4ca786;
    border-radius: 20px;
    margin-left: 8px;
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  .item-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    .footer-box {
      padding: 6px 8px;
      background: #f0f2f1;
      border-radius: 3px;
      opacity: 0.5;
      display: flex;
      align-items: center;
      .footer-text {
        font-weight: 400;
        font-size: 12px;
        color: #5c6663;
        line-height: 14px;
        margin-right: 4px;
      }
      .footer-icon {
        font-size: 14px;
        color: #5c6663;
      }
    }
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
    position: relative;
  }
  .footer-btn {
    font-size: 14px;
    color: #939c99;
    .more-btn {
      width: 100%;
      padding: 10px 0 5px;
      text-align: center;
    }
  }
</style>
