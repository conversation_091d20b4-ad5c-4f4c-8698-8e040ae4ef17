<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.plateNumber"
              name="plateNumber"
              label="车牌号"
              placeholder="请选择车牌号"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.plateNumber"
              required
              @click="showCarPicker = true"
            />
            <van-field
              v-model="ruleForm.operatorName"
              name="operatorName"
              label="经办人"
              placeholder="请输入经办人"
              input-align="right"
              error-message-align="right"
              required
              maxlength="20"
              :border="false"
              :rules="rules.operatorName"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="年审信息填报"></baseTitle>
            <van-field
              v-model="ruleForm.costs"
              name="costs"
              label="年审金额(元)"
              placeholder="请输入年审金额"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rules.costs"
              required
            />
            <van-field
              v-model="ruleForm.organizationName"
              name="organizationName"
              label="年审单位"
              placeholder="请输入年审单位"
              input-align="right"
              error-message-align="right"
              required
              maxlength="100"
              :border="false"
              :rules="rules.organizationName"
            />
            <van-field
              v-model="ruleForm.odd"
              name="odd"
              label="年审单号"
              placeholder="请输入年审单号"
              input-align="right"
              error-message-align="right"
              maxlength="20"
              :border="false"
            />
            <van-field
              v-model="ruleForm.recentAnnualReviewTime"
              name="recentAnnualReviewTime"
              label="最近年审日期"
              placeholder="请选择最近年审日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.recentAnnualReviewTime"
              required
              @click="openDatePicker('recentAnnualReviewTime')"
            />
            <div class="tips">
              <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
              <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
            </div>
            <van-field
              v-model="ruleForm.nextAnnualReviewTime"
              name="nextAnnualReviewTime"
              label="下次年审日期"
              placeholder="请选择下次年审日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.nextAnnualReviewTime"
              required
              @click="openDatePicker('nextAnnualReviewTime')"
            />
            <div class="tips">
              <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
              <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
            </div>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="年审凭证"></baseTitle>
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">凭证</div>
                  <div class="label-text">(请上传年审单据)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.fileList"
              :isRequired="true"
              formId="fileList"
              formPlaceHolder="请上传年审单据"
            ></imageUpload>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="onCarConfirm"
        @cancel="showCarPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFunByParams } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: {
      imageUpload,
      baseTitle,
    },
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/vehicle/annualReview/create",
          carList: "/api/vehicle/dossier/list",
          defaultCar: "/api/vehicle/dossier/findByPhone",
        },
        showCarPicker: false,
        carOptions: [],
        defaultCarIndex: 0,
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        ruleForm: {
          plateNumber: "", //车牌号
          operatorName: "", //经办人
          costs: "", //年审金额
          organizationName: "", //年审单位
          recentAnnualReviewTime: "", //最近年审时间
          nextAnnualReviewTime: "", //下次年审时间
          odd: "", //年审单号
          remarks: "", //备注
          fileList: [], //年审凭证
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号" }],
          operatorName: [{ required: true, message: "请输入经办人" }],
          costs: [{ required: true, message: "请输入年审金额" }],
          organizationName: [{ required: true, message: "请输入年审单位" }],
          recentAnnualReviewTime: [{ required: true, message: "请选择最近年审日期" }],
          nextAnnualReviewTime: [{ required: true, message: "请选择下次年审日期" }],
        },
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.getUserDefaultCar();
      },
      // 获取当前用户默认车牌号
      async getUserDefaultCar() {
        let res = await getInfoApiFunByParams(
          { phone: this.userInfo.phone ? this.$sm2Encrypt(this.userInfo.phone) : "" },
          this.apis.defaultCar,
        );
        if (res.data) {
          this.ruleForm.plateNumber = res.data.plateNumber;
          let index = this.carOptions.findIndex((option) => option.id == res.data.id);
          if (index > 0) {
            this.defaultCarIndex = index;
          }
        }
      },
      // 确认选择车牌号
      onCarConfirm(item) {
        this.ruleForm.plateNumber = item.name;
        this.showCarPicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增车辆年审记录成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
