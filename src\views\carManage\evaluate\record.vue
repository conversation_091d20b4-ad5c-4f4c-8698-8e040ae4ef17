<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList">
    <template #default="{ form }">
      <ul class="record-box" v-if="form.detailList">
        <baseTitle class="record-title" title="车辆状况评价明细"></baseTitle>
        <li class="record-item" v-for="(item, index) in form.detailList" :key="index">
          <div class="record-left">{{ item.configName }}</div>
          <div class="record-right" :class="{ active: item.status == 1 }">{{ EVALUATE_STATUS[item.status] }}</div>
        </li>
      </ul>
    </template>
  </pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { EVALUATE_STATUS } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: { pageRecord, baseTitle },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicle/evaluation/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "operatorName",
                value: "经办人",
              },
            ],
          },
          {
            title: "状态信息填报",
            recordFieldArr: [
              {
                key: "conditionTime",
                value: "评价日期",
              },
            ],
          },
        ],
        EVALUATE_STATUS,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped>
  .record-box {
    background-color: #fff;
    border-radius: 8px;
  }
  .record-item {
    padding: 12px 24px;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 16px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 16px;
    text-align: right;
    word-break: break-all;
    &.active {
      color: #f53f3f;
    }
  }
</style>
