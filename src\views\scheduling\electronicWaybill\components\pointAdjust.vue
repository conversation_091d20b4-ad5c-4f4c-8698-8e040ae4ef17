<template>
  <div class="point-container">
    <van-button class="cancel-button" type="default" size="small" @click="closePointAdjust">取消</van-button>
    <van-button class="save-button" type="primary" size="small" @click="savePointAdjust">保存</van-button>
    <div class="map-box">
      <mapContainer :config="mapConfig" @initMap="initMap"></mapContainer>
      <div class="locate-icon" @click="toLocate"></div>
    </div>
    <main class="point-main">
      <div class="main-input">
        <van-field
          v-model="placeValue"
          label=""
          placeholder="搜索地点"
          right-icon="search"
          clearable
          @change="searchPlace"
          @clear="clearPlace"
        >
          <template #right-icon>
            <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
          </template>
        </van-field>
      </div>
      <div class="main-table">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index" @click="itemClick(item)">
              <div class="item-left">
                <div class="left-name">{{ item.name }}</div>
                <div class="left-address">
                  <span>{{ item.distance }}m</span>
                  <span> | </span>
                  <span>{{ item.address }}</span>
                </div>
              </div>
              <div class="item-right" v-show="item.active">
                <van-icon name="success" color="#4ca786" size="24" />
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </div>
    </main>
  </div>
</template>

<script>
  import mapContainer from "@/components/mapContainer";
  export default {
    props: {
      lng: {
        type: [String, Number],
        default: "",
      },
      lat: {
        type: [String, Number],
        default: "",
      },
      isSave: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      mapContainer,
    },
    data() {
      return {
        map: "",
        currentPosition: [], //当前定位位置
        placeValue: "", //搜索地点
        loading: false,
        finished: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        tableList: [], //附近地点列表
        geocoder: "",
        geolocation: "",
        msearch: "",
        mapConfig: {
          viewMode: "3D",
          zoom: 16, //地图级别
          center: [113.2744826, 23.1820811], //地图中心点
        },
        selectPosition: [], // 选择的位置
      };
    },
    beforeDestroy() {
      if (this.map) {
        this.map.destroy();
        this.map = "";
      }
    },
    mounted() {},
    methods: {
      // 初始化地图
      async initMap(map) {
        if (map) {
          this.map = map;
          window.AMap.plugin("AMap.Geocoder", () => {
            this.geocoder = new window.AMap.Geocoder({
              city: "全国", //城市设为广州，默认：“全国”
            }); //创建工具条插件实例
          });
          await window.AMap.plugin("AMap.Geolocation", () => {
            this.geolocation = new window.AMap.Geolocation({
              enableHighAccuracy: true, //是否使用高精度定位，默认:true
              timeout: 10000, //超过10秒后停止定位，默认：5s
              position: "RB", //定位按钮的停靠位置
              offset: [10, 20], //定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
              zoomToAccuracy: true, //定位成功后是否自动调整地图视野到定位点
              showButton: false,
            });
            this.map.addControl(this.geolocation);
          });
          window.AMap.plugin("AMap.PlaceSearch", () => {
            this.msearch = new window.AMap.PlaceSearch({
              city: "020", //城市设为广州，默认：“全国”
              type: "餐饮服务|商务住宅|生活服务|交通设施服务|金融保险服务|公司企业|道路附属设施|地名地址信息|公共设施", //数据类别
              pageSize: this.pageSize, //每页结果数,默认10
              pageIndex: this.pageNo, //请求页码，默认1
              extensions: "base", //返回信息详略，默认为base（基本信息）
              citylimit: true,
            }); //创建工具条插件实例
            this.geolocation.getCurrentPosition((status, result) => {
              if (status == "complete") {
                this.currentPosition = [result.position.lng, result.position.lat];
                this.placeValue = "";
                if (this.isSave) {
                  this.generateMark([this.lng, this.lat]);
                  this.selectPosition = [this.lng, this.lat];
                  this.onRefresh();
                } else {
                  this.selectPosition = this.currentPosition;
                  this.onRefresh();
                }
              }
            });
          });
          map.on("click", this.mapClick);
        }
      },
      // 返回当前位置
      toLocate() {
        this.map.clearInfoWindow();
        this.map.clearMap();
        this.placeValue = "";
        this.selectPosition = this.currentPosition;
        this.onRefresh();
      },
      onRefresh() {
        this.tableList = [];
        this.msearch.setPageIndex(1);
        this.onLoad();
        this.map.setCenter(this.selectPosition);
      },
      // 初始化数据
      async onLoad() {
        this.msearch.searchNearBy("", this.selectPosition, 2000, (status, result) => {
          if (status == "complete" && result.info == "OK") {
            let { poiList } = result;
            let dataList = poiList.pois;
            dataList.forEach((item) => {
              item.active = false;
            });
            this.tableList = this.tableList.concat(dataList);
            this.tableList[0].active = true;
            this.total = poiList.count;
            this.msearch.setPageIndex(this.pageNo + 1);
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        });
      },
      // 地图点击事件
      async mapClick(e) {
        this.map.clearInfoWindow();
        this.map.clearMap();
        this.generateMark(e.lnglat);
        this.selectPosition = e.lnglat;
        this.onRefresh();
      },
      // 列表项点击事件
      itemClick(item) {
        if (item.active) return;
        this.tableList.forEach((list) => {
          list.active = false;
          if (JSON.stringify(list) === JSON.stringify(item)) {
            list.active = true;
          }
        });
        this.map.clearInfoWindow();
        this.map.clearMap();
        this.generateMark([item.location.lng, item.location.lat]);
      },
      // 搜索地点
      async searchPlace() {
        let position = await this.positiveGeocoding(this.placeValue);
        this.generateMark(position);
        this.selectPosition = position;
        this.onRefresh();
      },
      // 清除搜索
      async clearPlace() {
        this.map.clearInfoWindow();
        this.map.clearMap();
        this.selectPosition = this.currentPosition;
        this.onRefresh();
      },
      // 生成点位
      generateMark(lnglat) {
        const icon = new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
          imageSize: new window.AMap.Size(25, 34),
        });
        let marker = new window.AMap.Marker({
          position: lnglat,
          icon: icon,
          offset: new window.AMap.Pixel(-13, -30),
        });
        this.map.add(marker);
        this.map.setCenter(marker.getPosition());
      },
      // 关闭弹窗
      closePointAdjust() {
        this.$emit("closePointAdjust");
      },
      // 保存地址、经纬度
      savePointAdjust() {
        let item = this.tableList.filter((list) => list.active)[0];
        this.$emit("savePointAdjust", item);
      },
      // 正地理编码
      positiveGeocoding(value) {
        return new Promise((resolve, reject) => {
          this.geocoder.getLocation(value, (status, result) => {
            if (status === "complete" && result.info === "OK") {
              resolve([result.geocodes[0].location.lng, result.geocodes[0].location.lat]);
            } else {
              reject();
            }
          });
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .point-container {
    position: relative;
    height: 100%;
  }
  .map-box {
    height: 400px;
    position: relative;
    .locate-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #fff;
      box-shadow: 0 0 5px silver;
      cursor: pointer;
      background-image: url(https://a.amap.com/jsapi/static/image/plugin/locate.png);
      background-size: 24px;
      background-repeat: no-repeat;
      background-position: 50%;
      position: absolute;
      bottom: 20px;
      right: 10px;
    }
  }

  .cancel-button {
    position: absolute;
    border-radius: 4px;
    top: 10px;
    left: 10px;
    z-index: 100;
  }
  .save-button {
    border-radius: 4px;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
  }
  .point-main {
    height: calc(100vh - 400px);
    height: calc(100dvh - 400px);
    display: flex;
    flex-direction: column;
    background-color: #fff;
    .main-input {
      padding: 12px 20px;
    }
    .main-table {
      flex: 1;
      overflow-y: auto;
    }
  }
  .list-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f2f1;
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    .item-left {
      flex: 1;
      overflow: hidden;
      .left-name {
        font-size: 16px;
        font-weight: bold;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .left-address {
        font-size: 14px;
        color: #909399;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 10px;
      }
    }
  }
  ::v-deep .main-input .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .main-input .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .main-input .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
