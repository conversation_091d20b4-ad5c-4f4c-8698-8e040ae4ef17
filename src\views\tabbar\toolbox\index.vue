<template>
  <div class="page-container">
    <div class="main-box flex-1">
      <!-- 常用工具 -->
      <div class="custom-box">
        <card-box title="常用工具" :isRight="false" :bgColor="'linear-gradient( 180deg, #EFFFFA 0%, #FFFFFF 100%);'">
          <div class="grid-list" v-if="customList.length > 0">
            <div class="grid-item" v-for="(item, index) in customList" :key="index" @click="deleteCustomMenu(item)">
              <div class="grid-image">
                <van-image class="grid-icon" :src="item.icon" fit="fill" />
                <van-image class="toolbox-icon" :src="toolboxDelete" fit="fill" />
              </div>
              <div class="grid-text">{{ item.name }}</div>
            </div>
          </div>
          <van-empty class="custom-empty" description="暂无常用工具" v-else />
        </card-box>
      </div>
      <div class="menus-grid" v-if="menusData.length > 0">
        <div v-for="item in menusData" :key="item.id">
          <card-box :title="item.name" :isRight="false">
            <div class="grid-list">
              <div class="grid-item" v-for="second in item.menus" :key="second.id" @click="toggleCustomMenu(second)">
                <div class="grid-image">
                  <van-image class="grid-icon" :src="second.icon" fit="fill" />
                  <van-image class="toolbox-icon" :src="toolboxDelete" fit="fill" v-if="second.isCustom" />
                  <van-image class="toolbox-icon" :src="toolboxAdd" fit="fill" v-else />
                </div>
                <div class="grid-text">{{ second.name }}</div>
              </div>
            </div>
          </card-box>
        </div>
      </div>
      <van-empty description="暂无权限" v-else />
    </div>
  </div>
</template>

<script>
  import cardBox from "../home/<USER>/cardBox.vue";
  import { getUserInfo, getLevelId } from "@/utils/storage";
  import { createApiFun, deleteApiFun } from "@/api/base";
  export default {
    components: {
      cardBox,
    },
    data() {
      return {
        toolboxAdd: require("@/assets/images/toolbox_add.png"),
        toolboxDelete: require("@/assets/images/toolbox_delete.png"),
        menusData: [],
        customList: [],
        customMenuIds: [],
      };
    },
    async created() {
      let userInfo = getUserInfo();
      if (userInfo) {
        await this.getUseCustomList();
        this.getAuthorizationDistinct();
      } else {
        this.menusData = [];
        this.customList = [];
        this.customMenuIds = [];
      }
    },
    methods: {
      // 获取常用工具列表
      async getUseCustomList() {
        try {
          let res = await createApiFun("", "/api/custom/list");
          res.data.forEach((item) => {
            try {
              item.icon = JSON.parse(item.icon).url;
            } catch (error) {
              item.icon = "";
            }
          });
          this.customList = res.data;
          this.customMenuIds = res.data.map((i) => i.menuId);
        } catch (error) {
          this.customList = [];
          this.customMenuIds = [];
        }
      },
      // 获取全部权限菜单
      async getAuthorizationDistinct() {
        let roleId = getLevelId();
        try {
          let res = await createApiFun(
            {
              roleId,
              menuType: 36,
              isPolymerization: true,
            },
            "/api/userrole/authorizationRole",
          );
          let menus = this.formatData(res.data.menus);
          this.menusData = menus;
        } catch (error) {
          this.menusData = [];
        }
      },
      //格式化图标数据
      formatData(list) {
        let newArr = [];
        list.forEach((item) => {
          let obj = {
            ...item,
            icon: item.icon ? JSON.parse(item.icon).url : null,
            checkedIcon: item.checkedIcon ? JSON.parse(item.checkedIcon).url : null,
            menus: [],
            isCustom: this.customMenuIds.includes(item.id),
          };
          if (item.menus && item.menus.length) {
            obj.menus = this.formatData(item.menus);
          }
          newArr.push(obj);
        });
        return newArr;
      },
      // 保存或删除常用工具
      async toggleCustomMenu(item) {
        if (item.isCustom) {
          try {
            await deleteApiFun(item.id, "/api/custom/delete/");
            this.dealMenusDataCustom(item.id, item.parentId, 1);
            this.$toast({
              type: "success",
              message: "删除成功",
              forbidClick: true,
              duration: 1500,
            });
          } catch (error) {
            console.log(error);
          }
        } else {
          try {
            await createApiFun(
              {
                menuId: item.id,
              },
              "/api/custom/save",
            );
            this.dealMenusDataCustom(item.id, item.parentId, 0);
            this.$toast({
              type: "success",
              message: "保存成功",
              forbidClick: true,
              duration: 1500,
            });
          } catch (error) {
            console.log(error);
          }
        }
        this.getUseCustomList();
      },
      // 删除常用工具
      async deleteCustomMenu(item) {
        let { menuId, parentId } = item;
        try {
          await deleteApiFun(menuId, "/api/custom/delete/");
          this.dealMenusDataCustom(menuId, parentId, 1);
          this.$toast({
            type: "success",
            message: "保存成功",
            forbidClick: true,
            duration: 1500,
          });
        } catch (error) {
          console.log(error);
        }
        this.getUseCustomList();
      },
      // 保存或删除后处理菜单栏状态
      dealMenusDataCustom(menuId, parentId, type) {
        let menuIndex = this.data.menusData.findIndex((data) => data.id === parentId);
        let menus = this.data.menusData.filter((data) => data.id === parentId)[0].menus;
        let childIndex = menus.findIndex((item) => item.id === menuId);
        if (menuIndex >= 0 && childIndex >= 0) {
          this.menusData[menuIndex].menus[childIndex].isCustom = type === 1 ? false : true;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
  }
  .main-box {
    background-color: #f7faf9;
    overflow-y: auto;
    padding: 0 16px;
  }

  .custom-box {
    margin-top: 12px;
  }

  .grid-list {
    display: grid;
    grid-gap: 16px 8px;
    grid-template-columns: repeat(4, 1fr);
    padding: 16px 0;
  }

  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 6px;
  }

  .grid-text {
    font-size: 12px;
    margin-top: 8px;
    text-align: center;
    line-height: 14px;
  }

  .grid-icon {
    width: 32px;
    height: 32px;
  }

  .custom-empty .van-empty {
    padding: 0;
  }

  .grid-image {
    position: relative;
  }

  .toolbox-icon {
    width: 16px;
    height: 16px;
    position: absolute;
    top: -5px;
    right: -5px;
  }
</style>
