<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="路线基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.name"
              name="name"
              label="路线名称"
              placeholder="请输入路线名称"
              input-align="right"
              error-message-align="right"
              required
              maxlength="32"
              :border="false"
              :rules="rules.name"
            />
            <van-field
              v-model="ruleForm.code"
              name="code"
              label="路线编号"
              placeholder="请输入路线编号"
              input-align="right"
              error-message-align="right"
              required
              maxlength="6"
              :border="false"
              :rules="rules.code"
            />
            <van-field
              name="status"
              label="路线状态"
              placeholder=""
              input-align="right"
              error-message-align="right"
              required
              :border="false"
              :rules="rules.status"
            >
              <template #input>
                <van-radio-group v-model="ruleForm.status" direction="horizontal" icon-size="20">
                  <van-radio :name="index" v-for="(item, index) in ROUTE_STATUS.slice(0, 2)" :key="index">{{
                    item
                  }}</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              :value="ruleForm.districtName"
              name="districtId"
              label="所属区域"
              placeholder="请选择所属区域"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.districtId"
              required
              @click="showDistrictPicker = true"
            />
            <van-field
              :value="ROUTE_PROPERTY[ruleForm.type]"
              name="type"
              label="路线属性"
              placeholder="请选择路线属性"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.type"
              required
              @click="showTypePicker = true"
            />
            <van-field
              name="waybillType"
              label="收运方式"
              placeholder=""
              input-align="right"
              error-message-align="right"
              required
              :border="false"
              :rules="rules.waybillType"
            >
              <template #input>
                <van-radio-group v-model="ruleForm.waybillType" icon-size="20">
                  <van-radio :name="index" v-for="(item, index) in WAYBILL_TYPE" :key="index">{{ item }}</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="车辆、人员信息"></baseTitle>
            <van-field
              :value="ruleForm.defaultVehiclePlateNumber"
              name="defaultVehiclePlateNumber"
              label="收运车辆"
              placeholder="请选择收运车辆"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultVehiclePlateNumber"
              required
              @click="showPlateNumberPicker = true"
            />
            <van-field
              :value="ruleForm.defaultDriverDossierName"
              name="defaultDriverDossierId"
              label="驾驶司机"
              placeholder="请选择驾驶司机"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultDriverDossierId"
              required
              @click="showDriverPicker = true"
            />
            <van-field
              :value="ruleForm.defaultDriverDossierPhone"
              name="defaultDriverDossierPhone"
              label="司机联系方式"
              placeholder="请输入司机联系方式"
              input-align="right"
              error-message-align="right"
              required
              maxlength="11"
              readonly
              :border="false"
              :rules="rules.defaultDriverDossierPhone"
            />
            <van-field
              :value="ruleForm.supercargoDossierOneName"
              label="押运工1"
              placeholder="请选择押运工1"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierOnePicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierOnePhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoName"
              label="押运工2"
              placeholder="请选择押运工2"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierTwoPicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoPhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="收运点位信息"></baseTitle>
            <div class="page-header">
              <div class="header-input">
                <van-field v-model="keyword" label="" placeholder="请输入点位名称" left-icon="search" clearable>
                  <template #right-icon>
                    <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
                  </template>
                </van-field>
              </div>
              <div class="filter-button">
                <van-button type="info" size="small" @click="showPopup = true">添加点位</van-button>
              </div>
            </div>
            <ul class="list" v-if="tableData.length > 0">
              <li class="list-item" v-for="(item, index) in tableData" :key="item.id">
                <div class="list-index">{{ index + 1 }}</div>
                <div class="list-content">
                  <div class="item-title">点位名称：{{ item.name }}</div>
                  <div class="item-text">点位编号：{{ item.code }}</div>
                  <div class="item-text">点位类型：{{ POINT_TYPE[item.type] }}</div>
                  <div class="item-text">点位收运方式：{{ POINT_RECEIVING_METHOD[item.baggingMethod] }}</div>
                  <div class="item-text">点位地址：{{ item.address }}</div>
                  <div class="item-text">联系人：{{ item.contact }}</div>
                  <div class="item-text">联系方式：{{ item.contactPhone }}</div>
                  <div class="item-text">收运周期：{{ COLLECTION_CYCLE[item.period] }}</div>
                  <div class="item-text">收运频次：{{ item.frequency }}</div>
                  <div class="item-text">经营状态：{{ REMOVE_STATUS[item.isUndock] }}</div>
                </div>
                <van-icon class="delete-icon" name="cross" size="20" color="#939c99" @click="deleteItem(item)" />
              </li>
            </ul>
            <van-empty description="暂无收运点位数据" v-else />
          </main>
        </div>
        <footer class="create-footer">
          <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="cancel"
            >取消</van-button
          >
          <van-button
            native-type="button"
            class="round-22 ml-11"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >保存</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showDistrictPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="districtOptions"
        :default-index="defaultDistrictIndex"
        @confirm="onDistrictConfirm"
        @cancel="showDistrictPicker = false"
      />
    </van-popup>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="ROUTE_PROPERTY"
        :default-index="defaultTypeIndex"
        @confirm="onTypefirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>
    <van-popup v-model="showPlateNumberPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultPlateNumberIndex"
        @confirm="onCarConfirm"
        @cancel="showPlateNumberPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDriverPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="driverOptions"
        :default-index="defaultDriverIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'defaultDriverDossier', 'showDriverPicker')"
        @cancel="showDriverPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDossierOnePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierOneIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierOne', 'showDossierOnePicker')"
        @cancel="showDossierOnePicker = false"
      />
    </van-popup>
    <van-popup v-model="showDossierTwoPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierTwoIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierTwo', 'showDossierTwoPicker')"
        @cancel="showDossierTwoPicker = false"
      />
    </van-popup>
    <van-popup v-model="showPopup" position="right">
      <template v-if="showPopup">
        <div class="popup-container">
          <point :originalTable="originalTable" @closePopup="showPopup = false" @selectItem="selectItem"></point>
        </div>
      </template>
    </van-popup>
    <van-dialog
      v-model="showDialog"
      title="备注"
      show-cancel-button
      confirmButtonColor="#4ca786"
      @confirm="confirmThrottling"
    >
      <van-field
        v-model="ruleForm.historyRemark"
        rows="8"
        maxlength="500"
        label=""
        type="textarea"
        placeholder="请输入备注"
        show-word-limit
      />
    </van-dialog>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun, getInfoApiFunByParams, updateApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import {
    ROUTE_STATUS,
    ROUTE_PROPERTY,
    WAYBILL_TYPE,
    POINT_TYPE,
    REMOVE_STATUS,
    COLLECTION_CYCLE,
    POINT_RECEIVING_METHOD,
  } from "@/enums";
  import point from "./components/point.vue";
  export default {
    components: {
      baseTitle,
      point,
    },
    data() {
      return {
        ROUTE_STATUS,
        ROUTE_PROPERTY,
        WAYBILL_TYPE,
        POINT_TYPE,
        REMOVE_STATUS,
        COLLECTION_CYCLE,
        POINT_RECEIVING_METHOD,
        submitFormThrottling: () => {},
        apis: {
          create: "/api/pickup/pickupPath/create",
          update: "/api/pickup/pickupPath/update",
          info: "/api/pickup/pickupPath/get/",
          regionList: "/api/region/regionList",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          compare: "/api/pickup/pickupPath/compareCurrentPathWaybill/",
        },
        ruleForm: {
          name: "", //路线名称
          code: "", //路线编号
          status: 0, // 路线状态
          districtId: "", //所属区域id
          districtName: "", //所属区域名称
          type: "", //路线属性
          waybillType: "", //收运方式
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //驾驶司机id
          defaultDriverDossierName: "", //驾驶司机名称
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1id
          supercargoDossierOneName: "", // 押运工1名称
          supercargoDossierOnePhone: "", //押运工1联系方式
          supercargoDossierTwoId: "", //押运工2id
          supercargoDossierTwoName: "", //押运工2名称
          supercargoDossierTwoPhone: "", //押运工2联系方式
          historyRemark: "",
        },
        rules: {
          name: [{ required: true, message: "请输入路线名称" }],
          code: [{ required: true, message: "请输入路线编号" }],
          status: [{ required: true, message: "请选择路线状态" }],
          districtId: [{ required: true, message: "请选择所属区域" }],
          type: [{ required: true, message: "请选择路线属性" }],
          waybillType: [{ required: true, message: "请选择收运方式" }],
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆" }],
          defaultDriverDossierId: [{ required: true, message: "请选择驾驶司机" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式" }],
        },
        id: "",
        districtOptions: [],
        showDistrictPicker: false,
        defaultDistrictIndex: 0,
        showTypePicker: false,
        defaultTypeIndex: 0,
        carOptions: [],
        showPlateNumberPicker: false,
        defaultPlateNumberIndex: 0,
        driverOptions: [],
        showDriverPicker: false,
        defaultDriverIndex: 0,
        shipWorkerOptions: [],
        showDossierOnePicker: false,
        defaultDossierOneIndex: 0,
        showDossierTwoPicker: false,
        defaultDossierTwoIndex: 0,
        showPopup: false,
        keyword: "",
        originalTable: [],
        showDialog: false,
        confirmThrottling: () => {},
        needReplace: false,
        isReplace: "",
      };
    },
    computed: {
      tableData() {
        let dataList = this.originalTable;
        if (this.keyword.trim()) {
          dataList = dataList.filter((list) => list.name.includes(this.keyword));
        }
        return dataList;
      },
    },
    created() {
      this.id = this.$route.query.id || "";
      document.title = `${this.id ? "编辑" : "新增"}收运路线档案`;
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
      this.confirmThrottling = this.$throttling(this.updateRoute, 500);
    },
    async mounted() {
      await this.getOptions();
      if (this.id) {
        this.getRecord();
      }
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.defaultDriverDossierPhone = this.ruleForm.defaultDriverDossierPhone
            ? this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone)
            : "";
          this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
            : "";
          this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
            : "";

          this.originalTable = res.data.pickupPointList.map((item) => {
            return {
              ...item,
              contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
            };
          });
          let defaultDistrictIndex = this.districtOptions.findIndex((o) => o.id == res.data.districtId);
          if (defaultDistrictIndex >= 0) {
            this.defaultDistrictIndex = defaultDistrictIndex;
          }
          let defaultTypeIndex = ROUTE_PROPERTY.findIndex((_, index) => index == res.data.type);
          if (defaultTypeIndex >= 0) {
            this.defaultTypeIndex = defaultTypeIndex;
          }
          let defaultPlateNumberIndex = this.carOptions.findIndex(
            (item) => item.name == res.data.defaultVehiclePlateNumber,
          );
          if (defaultPlateNumberIndex >= 0) {
            this.defaultPlateNumberIndex = defaultPlateNumberIndex;
          }
          let defaultDriverIndex = this.driverOptions.findIndex(
            (item) => item.lgUnionId == res.data.defaultDriverDossierId,
          );
          if (defaultDriverIndex >= 0) {
            this.defaultDriverIndex = defaultDriverIndex;
          }
          if (res.data.supercargoDossierOneId) {
            let defaultDossierOneIndex = this.shipWorkerOptions.findIndex(
              (item) => item.lgUnionId == res.data.supercargoDossierOneId,
            );
            if (defaultDossierOneIndex >= 0) {
              this.defaultDossierOneIndex = defaultDossierOneIndex;
            }
          }
          if (res.data.supercargoDossierTwoId) {
            let defaultDossierTwoIndex = this.shipWorkerOptions.findIndex(
              (item) => item.lgUnionId == res.data.supercargoDossierTwoId,
            );
            if (defaultDossierTwoIndex >= 0) {
              this.defaultDossierTwoIndex = defaultDossierTwoIndex;
            }
          }
        }
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFunByParams({ pid: "440100000000" }, this.apis.regionList),
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.districtOptions = res[0].data;
        this.carOptions = res[1].data;
        this.driverOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[3].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 选择所属区域
      onDistrictConfirm(item) {
        this.ruleForm.districtId = item.id;
        this.ruleForm.districtName = item.name;
        this.showDistrictPicker = false;
      },
      // 选择路线属性
      onTypefirm(_, index) {
        this.ruleForm.type = index;
        this.showTypePicker = false;
      },
      // 选择收运车辆
      async onCarConfirm(item) {
        if (this.ruleForm.defaultVehiclePlateNumber == item.name) {
          this.showPlateNumberPicker = false;
          return;
        }
        let promiseList = [
          createApiFun({ userIdentity: "3", plateNumber: item.name }, this.apis.driverAndWorkerInfo),
          createApiFun({ userIdentity: "4", plateNumber: item.name }, this.apis.driverAndWorkerInfo),
        ];
        let res = await Promise.all(promiseList);
        let driverInfo = res[0].data;
        let workerInfo = res[1].data;
        if (driverInfo) {
          this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
          this.ruleForm.defaultDriverDossierName = driverInfo.fullName;
          this.ruleForm.defaultDriverDossierPhone = driverInfo.phone ? this.$sm2Decrypt(driverInfo.phone) : "";
          let defaultDriverIndex = this.driverOptions.findIndex((item) => item.lgUnionId == driverInfo.lgUnionId);
          if (defaultDriverIndex >= 0) {
            this.defaultDriverIndex = defaultDriverIndex;
          }
        } else {
          this.ruleForm.defaultDriverDossierId = "";
          this.ruleForm.defaultDriverDossierName = "";
          this.ruleForm.defaultDriverDossierPhone = "";
        }
        if (workerInfo) {
          this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
          this.ruleForm.supercargoDossierOneName = workerInfo.fullName;
          this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          let defaultDossierOneIndex = this.shipWorkerOptions.findIndex(
            (item) => item.lgUnionId == workerInfo.lgUnionId,
          );
          if (defaultDossierOneIndex >= 0) {
            this.defaultDossierOneIndex = defaultDossierOneIndex;
          }
        } else {
          this.ruleForm.supercargoDossierOneId = "";
          this.ruleForm.supercargoDossierOneName = "";
          this.ruleForm.supercargoDossierOnePhone = "";
        }
        this.ruleForm.defaultVehiclePlateNumber = item.name;
        this.showPlateNumberPicker = false;
      },
      // 选择驾驶司机、押运工
      onDriverShipWorkerConfirm(item, field, pickerPopup) {
        this.ruleForm[`${field}Id`] = item.lgUnionId;
        this.ruleForm[`${field}Name`] = item.fullName;
        this.ruleForm[`${field}Phone`] = item.phone;
        this[pickerPopup] = false;
      },
      // 添加/删除点位
      selectItem(obj) {
        if (obj.flag) {
          let index = this.originalTable.findIndex((item) => item.id == obj.item.id);
          if (index >= 0) {
            this.originalTable.splice(index, 1);
          }
        } else {
          this.originalTable.push(obj.item);
        }
      },
      // 删除点位
      deleteItem(item) {
        this.$dialog
          .confirm({
            title: "提示",
            message: "是否要从该路线中删除该收运点位",
          })
          .then(() => {
            let index = this.originalTable.findIndex((list) => list.id == item.id);
            if (index >= 0) {
              this.originalTable.splice(index, 1);
            }
          })
          .catch(() => {});
      },
      // 取消
      cancel() {
        this.$commonBack();
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            if (this.id) {
              this.showDialog = true;
              return;
            }
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            this.ruleForm.pickupPointIds = this.originalTable.map((data) => data.id);
            try {
              let res = await createApiFun(this.$sm2Encrypt(JSON.stringify(this.ruleForm)), this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: `新增收运路线档案成功`,
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      async updateRoute() {
        if (!this.ruleForm.historyRemark || !this.ruleForm.historyRemark.trim()) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "请填写备注",
            duration: 1500,
          });
          return;
        }
        let rsp = await getInfoApiFun(this.id, this.apis.compare);
        if (rsp.data) {
          this.needReplace = true;
          this.$dialog
            .confirm({
              title: "提示",
              message:
                "当前路线实际收运人员与路线档案中收运人员不一致，是否要以路线档案中人员为准更新路线收运单。是（以路线档案人员为准更新最新收运单）否（保持当前路线收运单人员不变）",
              confirmButtonText: "是",
              cancelButtonText: "否",
              confirmButtonColor: "#4ca786",
            })
            .then(() => {
              this.isReplace = 1;
              this.updateSecond();
            })
            .catch(() => {
              this.isReplace = 0;
              this.updateSecond();
            });
        } else {
          this.needReplace = false;
          this.updateSecond();
        }
      },
      async updateSecond() {
        this.$toast({
          type: "loading",
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });
        this.ruleForm.pickupPointIds = this.originalTable.map((data) => data.id);
        if (this.needReplace) {
          this.ruleForm.isReplace = this.isReplace;
        }
        try {
          let res = await updateApiFun(this.$sm2Encrypt(JSON.stringify(this.ruleForm)), this.apis.update);
          this.$toast.clear();
          if (res.success) {
            this.$toast({
              type: "success",
              message: `编辑收运路线档案成功`,
              forbidClick: true,
              duration: 1500,
            });
            setTimeout(() => {
              this.$commonBack();
            }, 1500);
          }
        } catch (error) {
          this.$toast.clear();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .popup-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
  }
  .page-header {
    background-color: #fff;
    display: flex;
    align-items: center;
    padding-bottom: 12px;
    .header-input {
      flex: 1;
      overflow: hidden;
    }
    .filter-button {
      margin-left: 6px;
    }
  }
  .list {
    padding-bottom: 12px;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    .list-index {
      width: 18px;
      height: 18px;
      background-color: #4ca786;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      margin-right: 10px;
    }
    .list-content {
      flex: 1;
      overflow: hidden;
    }
    .delete-icon {
      position: absolute;
      top: 6px;
      right: 6px;
    }
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__left-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .van-radio {
    margin-bottom: 2px;
  }
</style>
