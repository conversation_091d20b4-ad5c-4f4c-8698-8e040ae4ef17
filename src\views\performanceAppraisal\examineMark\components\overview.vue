<template>
  <div>
    <div class="overview-box" v-if="dataDetailFields?.includes('drumOrBagDetail')">
      <div class="overview-header">
        <div class="overview-title">大型床位绩效汇总</div>
        <div class="overview-total">绩效总额：{{ performanceDetailJson.drumOrBagPerformance }}（元）</div>
      </div>
      <div class="overview-item">桶装点位绩效：{{ performanceDetailJson.drumOrBagBucketPerformance }}（元）</div>
      <div class="overview-item">
        <div class="overview-item-left">月收运桶装点位：{{ performanceDetailJson.bucketCount }}</div>
        <div class="overview-item-right">月桶装点位重量：{{ performanceDetailJson.bucketWeight }}（吨）</div>
      </div>
      <div class="overview-item">袋装点位绩效：{{ performanceDetailJson.drumOrBagBagPerformance }}（元）</div>
      <div class="overview-item">
        <div class="overview-item-left">月收运袋装点位：{{ performanceDetailJson.bagCount }}</div>
        <div class="overview-item-right">月袋装点位重量：{{ performanceDetailJson.bagWeight }}（吨）</div>
      </div>
    </div>
    <div class="overview-box" v-if="dataDetailFields?.includes('collectionPointDetail')">
      <div class="overview-header">
        <div class="overview-title">小型床位绩效汇总</div>
        <div class="overview-total">绩效总额：{{ performanceDetailJson.collectionPointPerformance }}（元）</div>
      </div>
      <div class="overview-item">
        <div class="overview-item-left"
          >桶装点位绩效：{{ performanceDetailJson.collectionPointBucketPerformance }}（元）</div
        >
        <div class="overview-item-right"
          >袋装点位绩效：{{ performanceDetailJson.collectionPointBagPerformance }}（元）</div
        >
      </div>
    </div>
    <div class="overview-box" v-if="detailItem?.hasClinicGroup">
      <div class="overview-header">
        <div class="overview-title">诊所绩效汇总</div>
      </div>
      <!-- 绩效计算公式展示区域 -->
      <div
        class="flex-center pt-10"
        v-if="(detailItem?.lessType || detailItem?.lessType === 0) && detailItem?.pathFactor"
      >
        <!-- lessType为0时的计算公式: (基础工资 × 系数 + 超额工资 + 单干 = 绩效工资) -->
        <span v-if="detailItem?.lessType === 0"
          >{{ detailItem?.clinicGroupParam?.clinicBaseSalary || detailItem?.clinicGroupParam?.nsClinicBaseSalary }} ×
          {{ detailItem?.pathFactor
          }}{{ detailItem?.clinicGroupParam?.excessSalary ? " + " + detailItem?.clinicGroupParam?.excessSalary : ""
          }}{{
            detailItem?.clinicGroupParam?.singletonSalary ? " + " + detailItem?.clinicGroupParam?.singletonSalary : ""
          }}
          = {{ detailItem?.clinicGroupParam?.displaySalary }}（元）</span
        >
        <!-- lessType为1时的计算公式: (基础工资1 × 系数 + 基础工资2 × 系数 - 扣款1 - 扣款2 + 单干 = 绩效工资) -->
        <span v-else-if="detailItem?.lessType === 1">
          {{
            detailItem?.clinicGroupParam?.clinicBaseSalary
              ? detailItem?.clinicGroupParam?.clinicBaseSalary + " × " + detailItem?.pathFactor
              : ""
          }}
          {{
            detailItem?.clinicGroupParam?.clinicBaseSalary && detailItem?.clinicGroupParam?.nsClinicBaseSalary
              ? " + "
              : ""
          }}
          {{
            detailItem?.clinicGroupParam?.nsClinicBaseSalary
              ? detailItem?.clinicGroupParam?.nsClinicBaseSalary + " × " + detailItem?.pathFactor
              : ""
          }}{{
            detailItem?.clinicGroupParam?.clinicDeduction ? " - " + detailItem?.clinicGroupParam?.clinicDeduction : ""
          }}{{
            detailItem?.clinicGroupParam?.nsClinicDeduction
              ? " - " + detailItem?.clinicGroupParam?.nsClinicDeduction
              : ""
          }}{{
            detailItem?.clinicGroupParam?.singletonSalary ? " + " + detailItem?.clinicGroupParam?.singletonSalary : ""
          }}
          = {{ detailItem?.clinicGroupParam?.displaySalary }}（元）
        </span>
        <!-- 其他情况的计算公式: (点数 × 4 × 线路系数 + 单干 = 绩效工资) -->
        <span v-else
          >{{ detailItem?.clinicGroupParam?.pointNum }} × 4 × {{ detailItem?.pathFactor
          }}{{
            detailItem?.clinicGroupParam?.singletonSalary ? " + " + detailItem?.clinicGroupParam?.singletonSalary : ""
          }}
          = {{ detailItem?.clinicGroupParam?.displaySalary }}（元）</span
        >
      </div>
      <div class="is-flex select-box">
        <div class="select-left">
          <van-icon name="warning" color="#4CA786" size="18" />
        </div>
        <div class="select-right">
          <van-field
            :value="factorObj[ruleForm.pathFactor]"
            label="线路系数"
            placeholder="请选择线路系数"
            readonly
            input-align="right"
            error-message-align="right"
            is-link
            :border="false"
            required
            @click="showPathFactorPicker = true"
            v-if="canScore && performanceDetailJson?.clinicGroupParam?.factorList?.length"
          />
          <van-field
            :value="detailItem?.pathFactor ? detailItem?.pathFactor : '待选择线路系数'"
            label="线路系数"
            readonly
            input-align="right"
            error-message-align="right"
            :border="false"
            required
            v-else
          />
          <template v-if="detailItem.lessType !== 0">
            <van-field
              :value="lessTypeList[ruleForm.lessType - 1]"
              label="不足基准点位计算"
              placeholder="请选择不足基准时的规则"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              required
              @click="showLessType = true"
              v-if="canScore"
            />
            <van-field
              :value="detailItem.lessType ? lessTypeList[detailItem.lessType - 1] : '待选择不足基准时的规则'"
              label="不足基准点位计算"
              readonly
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              v-else
            />
          </template>
        </div>
      </div>
      <div class="overview-item" v-if="dataDetailFields?.includes('clinicDetail')">
        <div class="overview-item-left"
          >月收运常规诊所点位：{{ performanceDetailJson?.clinicCollectNum }}/{{
            performanceDetailJson?.clinicTotal
          }}</div
        >
        <div class="overview-item-right">月单人收运常规诊所点位：{{ performanceDetailJson?.clinicSingletonNum }}</div>
      </div>
      <div class="overview-item" v-if="dataDetailFields?.includes('nsClinicDetail')">
        <div class="overview-item-left"
          >月收运南沙诊所点位：{{ performanceDetailJson?.nsClinicCollectNum }}/{{
            performanceDetailJson?.nsClinicTotal
          }}</div
        >
        <div class="overview-item-right">月单人收运南沙诊所点位：{{ performanceDetailJson?.nsClinicSingletonNum }}</div>
      </div>
    </div>
    <div
      class="subsidy"
      v-if="performanceDetailJson?.totalDistrictSubsidyTotal || performanceDetailJson?.totalDistrictSubsidyTotal === 0"
    >
      <div class="subsidy-left">区域补贴</div>
      <div class="subsidy-right">{{ performanceDetailJson?.totalDistrictSubsidyTotal }}（元）</div>
    </div>
    <div
      class="subsidy"
      v-if="performanceDetailJson?.totalOvertimePerformance || performanceDetailJson?.totalOvertimePerformance === 0"
    >
      <div class="subsidy-left">加班费</div>
      <div class="subsidy-right">{{ performanceDetailJson?.totalOvertimePerformance }}（元）</div>
    </div>
    <div class="subsidy mb-0" v-if="performanceDetailJson?.teamLeaderSubsidy">
      <div class="subsidy-left">班组长补贴</div>
      <div class="subsidy-right">{{ performanceDetailJson?.teamLeaderSubsidy }}（元）</div>
    </div>
    <van-popup v-model="showPathFactorPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fieldName"
        :columns="factorList"
        :default-index="pathFactorIndex"
        @confirm="onPathFactorConfirm"
        @cancel="showPathFactorPicker = false"
      />
    </van-popup>
    <van-popup v-model="showLessType" position="bottom">
      <van-picker
        show-toolbar
        :columns="lessTypeList"
        :default-index="lessTypeIndex"
        @confirm="onLessTypeConfirm"
        @cancel="showLessType = false"
      />
    </van-popup>
  </div>
</template>

<script>
  export default {
    props: {
      detailItem: {
        type: Object,
        default: () => {},
      },
      performanceDetailJson: {
        type: Object,
        default: () => {},
      },
      dataDetailFields: {
        type: Array,
        default: () => [],
      },
      form: {
        type: Object,
        default: () => {},
      },
      canScore: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      factorList() {
        let list = [];
        if (this.performanceDetailJson?.clinicGroupParam?.factorList?.length > 0) {
          list = this.performanceDetailJson?.clinicGroupParam?.factorList.map((item) => {
            return {
              ...item,
              fieldName: `${item.factor}（${item.area}）`,
            };
          });
        }
        return list;
      },
      factorObj() {
        let obj = {};
        if (this.performanceDetailJson?.clinicGroupParam?.factorList?.length > 0) {
          this.performanceDetailJson?.clinicGroupParam?.factorList.forEach((item) => {
            obj[item.factor] = `${item.factor}（${item.area}）`;
          });
        }
        return obj;
      },
      pathFactorIndex() {
        let index = 0;
        if (this.detailItem.pathFactor && this.performanceDetailJson?.clinicGroupParam?.factorList?.length > 0) {
          let i = this.performanceDetailJson?.clinicGroupParam?.factorList.findIndex(
            (item) => item.factor == this.detailItem.pathFactor,
          );
          if (i > 0) {
            index = i;
          }
        }
        return index;
      },
      lessTypeIndex() {
        let index = 0;
        if (this.detailItem.lessType) {
          let i = this.lessTypeList.findIndex((_, itemIndex) => itemIndex + 1 == this.detailItem.lessType);
          if (i > 0) {
            index = i;
          }
        }
        return index;
      },
    },
    watch: {
      ruleForm: {
        handler(newForm) {
          this.$emit("update:form", newForm);
        },
        deep: true,
      },
      detailItem: {
        handler(newItem) {
          this.ruleForm.pathFactor = newItem.pathFactor;
          this.ruleForm.lessType = newItem.lessType;
        },
        deep: true,
      },
    },
    data() {
      return {
        ruleForm: {
          pathFactor: "",
          lessType: "",
        },
        showPathFactorPicker: false,
        lessTypeList: ["按规则扣费", "单个点位计费（x4）"],
        showLessType: false,
      };
    },
    methods: {
      // 确认选择线路系数
      onPathFactorConfirm(item) {
        this.ruleForm.pathFactor = item.factor;
        this.showPathFactorPicker = false;
      },
      // 确认选择扣款规则
      onLessTypeConfirm(_, index) {
        this.ruleForm.lessType = index + 1;
        this.showLessType = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .overview-box {
    padding: 10px;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    margin-bottom: 10px;
    .overview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .overview-title {
        font-size: 14px;
      }
      .overview-total {
        font-size: 12px;
      }
    }
    .overview-item {
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
    }
    .overview-item-left,
    .overview-item-right {
      word-break: break-all;
    }
    .overview-item-right {
      margin-left: 2px;
    }
  }
  .subsidy {
    padding: 10px;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .subsidy-left {
      font-size: 14px;
    }
    .subsidy-right {
      font-size: 12px;
    }
  }
  .select-box {
    margin-top: 10px;
    justify-content: center;
    .select-left {
      margin-right: 10px;
      padding-top: 10px;
    }
  }
  .mb-0 {
    margin-bottom: 0;
  }
  .pt-10 {
    padding-top: 10px;
  }
</style>
