<template>
  <div class="notice-container">
    <div class="notice-title">{{ ruleForm?.title }}</div>
    <div class="notice-content" v-html="ruleForm?.content"></div>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  export default {
    data() {
      return {
        apis: {
          info: "/api/notice/get/",
        },
        ruleForm: "",
      };
    },
    created() {
      this.getRecord();
    },
    methods: {
      async getRecord() {
        let res = await getInfoApiFun(this.$route.query.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .notice-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    flex-direction: column;
  }
  .notice-title {
    font-weight: 600;
    font-size: 18px;
    text-align: center;
    padding: 12px 0;
  }
  .notice-content {
    flex: 1;
    overflow-y: auto;
  }
  ::v-deep .notice-content * {
    all: revert;
  }
  ::v-deep .notice-content img {
    max-width: 100%;
    height: auto;
  }
</style>
