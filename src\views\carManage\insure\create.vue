<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.plateNumber"
              name="plateNumber"
              label="车牌号"
              placeholder="请选择车牌号"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.plateNumber"
              required
              @click="showCarPicker = true"
            />
            <van-field
              v-model="ruleForm.year"
              name="year"
              label="所属年份"
              placeholder="请选择所属年份"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              label-width="120"
              :border="false"
              :rules="rules.year"
              required
              @click="handleYearClick"
            />
            <van-field
              :value="headerOptions.map((o) => o.name).join('、')"
              name="insuranceType"
              label="投保类型"
              placeholder="请选择投保类型"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.insuranceType"
              required
              @click="changeType"
              :disabled="!ruleForm.plateNumber || !ruleForm.year"
            />
          </main>
          <div v-for="(item, index) in headerOptions" :key="index">
            <div class="border-line"></div>
            <main class="create-main">
              <baseTitle :title="item.name"></baseTitle>
              <van-field
                v-model="ruleForm[typeField[item.id][0]]"
                :name="typeField[item.id][0]"
                label="经办人"
                placeholder="请输入经办人"
                input-align="right"
                error-message-align="right"
                :border="false"
                :rules="rules[typeField[item.id][0]]"
                required
                maxlength="50"
              />
              <van-field
                v-model="ruleForm[typeField[item.id][1]]"
                :name="typeField[item.id][1]"
                label="保单号"
                placeholder="请输入保单号"
                input-align="right"
                error-message-align="right"
                :border="false"
                :rules="rules[typeField[item.id][1]]"
                required
                maxlength="30"
              />
              <van-field
                v-model="ruleForm[typeField[item.id][2]]"
                :name="typeField[item.id][2]"
                label="保单金额(元)"
                placeholder="请输入保单金额"
                input-align="right"
                error-message-align="right"
                type="number"
                :border="false"
                :rules="rules[typeField[item.id][2]]"
                required
              />
              <van-field
                v-model="ruleForm[typeField[item.id][3]]"
                :name="typeField[item.id][3]"
                label="保险公司"
                placeholder="请输入保险公司"
                input-align="right"
                error-message-align="right"
                :border="false"
                :rules="rules[typeField[item.id][3]]"
                required
                maxlength="100"
              />
              <van-field
                v-model="ruleForm[typeField[item.id][4]]"
                :name="typeField[item.id][4]"
                label="保单开始日期"
                placeholder="请选择保单开始日期"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules[typeField[item.id][4]]"
                required
                @click="openDatePicker(typeField[item.id][4])"
              />
              <div class="tips">
                <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
                <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
              </div>
              <van-field
                v-model="ruleForm[typeField[item.id][5]]"
                :name="typeField[item.id][5]"
                label="保单结束日期"
                placeholder="请选择保单结束日期"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules[typeField[item.id][5]]"
                required
                @click="openDatePicker(typeField[item.id][5])"
              />
              <div class="tips">
                <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
                <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
              </div>
              <van-field :border="false" required label-width="100%">
                <template #label>
                  <div class="label-box">
                    <div class="label-title">凭证</div>
                    <div class="label-text">(请上传保单、投保凭证)</div>
                  </div>
                </template>
              </van-field>
              <imageUpload
                v-model="ruleForm[typeField[item.id][6]]"
                :isRequired="true"
                :formId="typeField[item.id][6]"
                formPlaceHolder="请上传保单、投保凭证"
              ></imageUpload>
            </main>
          </div>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="onCarConfirm"
        @cancel="showCarPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showYearPicker" position="bottom">
      <van-datetime-picker
        class="year-picker"
        type="year-month"
        v-model="currentYear"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onYearConfirm"
        @cancel="showYearPicker = false"
      />
    </van-popup>
    <van-popup v-model="showTypePicker" position="bottom" :style="{ height: '20%' }">
      <div class="checkbox-box">
        <van-checkbox-group v-model="ruleForm.insuranceType">
          <van-checkbox
            class="mb-12"
            :name="index"
            v-for="(item, index) in operationalNature == 1 ? INSURANCE_TYPE.slice(0, 2) : INSURANCE_TYPE"
            :key="index"
            :disabled="selectedType.includes(index)"
            >{{ item }}</van-checkbox
          >
        </van-checkbox-group>
      </div>
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFunByParams, updateApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import { INSURANCE_TYPE } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: {
      imageUpload,
      baseTitle,
    },
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/vehicleInsure/create",
          update: "/api/vehicleInsure/update",
          carList: "/api/vehicle/dossier/list",
          defaultCar: "/api/vehicle/dossier/findByPhone",
          findByPlateNumberAndYear: "/api/vehicleInsure/findByPlateNumberAndYear",
        },
        showCarPicker: false,
        carOptions: [],
        defaultCarIndex: 0,
        showDatePicker: false,
        currentDate: new Date(),
        currentYear: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        showTypePicker: false,
        showYearPicker: false,
        ruleForm: {
          plateNumber: "", //车牌号
          year: "", //所属年份
          insuranceType: [], //投保类型
          operatorName: "", //交强险经办人
          policyNo: "", //交强险保单号
          money: "", //交强险保单金额
          insuranceCompany: "", //交强险保险公司
          policyEffectiveDate: "", //交强险保单开始日期
          policyTerminationDate: "", //交强险保单结束日期
          vehicleFileList: [], ///交强险投保凭证

          syOperatorName: "", //商业险经办人
          syPolicyNo: "", //商业险保单号
          syMoney: "", //商业险保单金额
          syInsuranceCompany: "", //商业险保险公司
          syPolicyEffectiveDate: "", //商业险保单开始日期
          syPolicyTerminationDate: "", //商业险保单结束日期
          syVehicleFileList: [], ///商业险投保凭证

          cyOperatorName: "", //承运险经办人
          cyPolicyNo: "", //承运险保单号
          cyMoney: "", //承运险保单金额
          cyInsuranceCompany: "", //承运险保险公司
          cyPolicyEffectiveDate: "", //承运险保单开始日期
          cyPolicyTerminationDate: "", //承运险保单结束日期
          cyVehicleFileList: [], ///承运险投保凭证
        },
        originalRuleForm: {
          plateNumber: "", //车牌号
          year: "", //所属年份
          insuranceType: [], //投保类型
          operatorName: "", //交强险经办人
          policyNo: "", //交强险保单号
          money: "", //交强险保单金额
          insuranceCompany: "", //交强险保险公司
          policyEffectiveDate: "", //交强险保单开始日期
          policyTerminationDate: "", //交强险保单结束日期
          vehicleFileList: [], ///交强险投保凭证

          syOperatorName: "", //商业险经办人
          syPolicyNo: "", //商业险保单号
          syMoney: "", //商业险保单金额
          syInsuranceCompany: "", //商业险保险公司
          syPolicyEffectiveDate: "", //商业险保单开始日期
          syPolicyTerminationDate: "", //商业险保单结束日期
          syVehicleFileList: [], ///商业险投保凭证

          cyOperatorName: "", //承运险经办人
          cyPolicyNo: "", //承运险保单号
          cyMoney: "", //承运险保单金额
          cyInsuranceCompany: "", //承运险保险公司
          cyPolicyEffectiveDate: "", //承运险保单开始日期
          cyPolicyTerminationDate: "", //承运险保单结束日期
          cyVehicleFileList: [], ///承运险投保凭证
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号" }],
          year: [{ required: true, message: "请选择所属年份" }],
          insuranceType: [{ required: true, message: "请选择投保类型" }],
          operatorName: [{ required: true, message: "请输入经办人" }],
          syOperatorName: [{ required: true, message: "请输入经办人" }],
          cyOperatorName: [{ required: true, message: "请输入经办人" }],
          policyNo: [{ required: true, message: "请输入保单号" }],
          syPolicyNo: [{ required: true, message: "请输入保单号" }],
          cyPolicyNo: [{ required: true, message: "请输入保单号" }],
          money: [{ required: true, message: "请输入保单金额" }],
          syMoney: [{ required: true, message: "请输入保单金额" }],
          cyMoney: [{ required: true, message: "请输入保单金额" }],
          insuranceCompany: [{ required: true, message: "请输入保险公司" }],
          syInsuranceCompany: [{ required: true, message: "请输入保险公司" }],
          cyInsuranceCompany: [{ required: true, message: "请输入保险公司" }],
          policyEffectiveDate: [{ required: true, message: "请选择保单开始日期" }],
          syPolicyEffectiveDate: [{ required: true, message: "请选择保单开始日期" }],
          cyPolicyEffectiveDate: [{ required: true, message: "请选择保单开始日期" }],
          policyTerminationDate: [{ required: true, message: "请选择保单结束日期" }],
          syPolicyTerminationDate: [{ required: true, message: "请选择保单结束日期" }],
          cyPolicyTerminationDate: [{ required: true, message: "请选择保单结束日期" }],
        },
        INSURANCE_TYPE,
        typeField: [
          [
            "operatorName",
            "policyNo",
            "money",
            "insuranceCompany",
            "policyEffectiveDate",
            "policyTerminationDate",
            "vehicleFileList",
          ],
          [
            "syOperatorName",
            "syPolicyNo",
            "syMoney",
            "syInsuranceCompany",
            "syPolicyEffectiveDate",
            "syPolicyTerminationDate",
            "syVehicleFileList",
          ],
          [
            "cyOperatorName",
            "cyPolicyNo",
            "cyMoney",
            "cyInsuranceCompany",
            "cyPolicyEffectiveDate",
            "cyPolicyTerminationDate",
            "cyVehicleFileList",
          ],
        ],
        operationalNature: 0,
        selectedType: [],
      };
    },
    computed: {
      headerOptions() {
        let options = [];
        let typeArr = this.ruleForm.insuranceType;
        if (this.ruleForm.insuranceType.length > 0) {
          typeArr = typeArr.sort((a, b) => a - b);
          typeArr.forEach((item) => {
            options.push({ id: item, name: INSURANCE_TYPE[item] });
          });
        }
        return options;
      },
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.getUserDefaultCar();
      },
      // 获取当前用户默认车牌号
      async getUserDefaultCar() {
        let res = await getInfoApiFunByParams(
          { phone: this.userInfo.phone ? this.$sm2Encrypt(this.userInfo.phone) : "" },
          this.apis.defaultCar,
        );
        if (res.data) {
          this.ruleForm.plateNumber = res.data.plateNumber;
          this.operationalNature = res.data.operationalNature;
          let index = this.carOptions.findIndex((option) => option.id == res.data.id);
          if (index > 0) {
            this.defaultCarIndex = index;
          }
        }
      },
      // 打开投保类型弹窗
      changeType() {
        if (!this.ruleForm.plateNumber || !this.ruleForm.year) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "请先选择车牌号和所属年份",
            duration: 1500,
          });
          return;
        }
        this.showTypePicker = true;
      },
      // 确认选择车牌号
      onCarConfirm(item) {
        this.getHasTypeByNumberAndYear();
        if (this.ruleForm.plateNumber === item.name) {
          return;
        }
        this.ruleForm.plateNumber = item.name;
        this.operationalNature = item.operationalNature;
        this.ruleForm.type = [];
        this.showCarPicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 打开选择年份弹窗
      handleYearClick() {
        this.showYearPicker = true;
        this.$nextTick(() => {
          let columns = this.$el.querySelectorAll(".year-picker .van-picker__columns .van-picker-column");
          if (columns.length === 2) {
            columns[1].parentElement.removeChild(columns[1]);
          }
        });
      },
      // 确认选择年份
      onYearConfirm(value) {
        this.ruleForm.year = moment(value).format("YYYY");
        this.showYearPicker = false;
        this.getHasTypeByNumberAndYear();
      },
      // 根据车牌号、年份获取已存在的投保类型
      async getHasTypeByNumberAndYear() {
        if (!this.ruleForm.plateNumber || !this.ruleForm.year) {
          return;
        }
        let res = await createApiFun(
          { plateNumber: this.ruleForm.plateNumber, year: this.ruleForm.year },
          this.apis.findByPlateNumberAndYear,
        );
        if (res.data) {
          this.ruleForm = res.data;
          this.ruleForm.insuranceType = res.data.insuranceType.split(",").map((i) => Number(i));
          this.selectedType = this.ruleForm.insuranceType;
        } else {
          let plateNumber = this.ruleForm.plateNumber;
          let year = this.ruleForm.year;
          this.ruleForm = JSON.parse(JSON.stringify(this.originalRuleForm));
          this.ruleForm.plateNumber = plateNumber;
          this.ruleForm.year = year;
          this.selectedType = [];
        }
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = {
              plateNumber: this.ruleForm.plateNumber,
              insuranceType: this.ruleForm.insuranceType.join(","),
              year: this.ruleForm.year,
            };
            let typeFieldList = this.typeField.filter((_, index) => this.ruleForm.insuranceType.includes(index));
            typeFieldList.forEach((list) => {
              list.forEach((field) => {
                params[field] = this.ruleForm[field];
              });
            });
            if (this.ruleForm.id) {
              params.id = this.ruleForm.id;
            }
            try {
              let res = this.ruleForm.id
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: `${this.ruleForm.id ? "修改" : "新增"}车辆投保记录成功`,
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .checkbox-box {
    padding: 12px;
  }
  .mb-12 {
    margin-bottom: 12px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
