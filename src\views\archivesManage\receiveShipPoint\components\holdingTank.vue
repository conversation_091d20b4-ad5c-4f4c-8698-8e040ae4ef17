<template>
  <div class="page-container">
    <header class="page-header">
      <div class="header-input">
        <van-field
          v-model="keyword"
          label=""
          placeholder="请输入设备名称"
          right-icon="search"
          clearable
          @change="onRefresh"
          @clear="onRefresh"
        >
          <template #right-icon>
            <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
          </template>
        </van-field>
      </div>
    </header>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="item-content">
                <div class="item-left">
                  <div class="item-title">设备名称：{{ item.deviceName }}</div>
                  <div class="item-text">运行状态：{{ CURRENT_STATUS[item.runStatus] }}</div>
                  <div class="item-text">设备状态：{{ DEVICE_STATUS[item.deviceStatus] }}</div>
                  <div class="item-text">设备地址：{{ item.deviceAddress }}</div>
                </div>
                <div class="item-right">
                  <van-button
                    class="round-4"
                    native-type="button"
                    type="info"
                    color="#4CA786"
                    size="small"
                    @click="selectItem(item)"
                    >{{ id === item.id ? "取消选择" : "选择" }}</van-button
                  >
                </div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
    <div class="page-footer">
      <van-button native-type="button" class="round-22" block type="info" color="#4CA786" @click="$emit('closePopup')"
        >返回</van-button
      >
    </div>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { CURRENT_STATUS, DEVICE_STATUS } from "@/enums";
  export default {
    props: {
      id: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        listPageApi: "/api/holding/holdingTank/listPage",
        tableList: [],
        CURRENT_STATUS,
        DEVICE_STATUS,
        keyword: "",
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          keyword: this.keyword,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas;
            dataList.forEach((list) => {
              for (let key in list) {
                list[key] = list[key] || list[key] === 0 ? list[key] : "-";
              }
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 选择列表项
      selectItem(item) {
        this.$emit("selectItem", {
          id: item.id === this.id ? "" : item.id,
          name: item.id === this.id ? "" : item.deviceName,
          type: 0,
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
  }
  .header-input {
    flex: 1;
    overflow: hidden;
  }
  .filter-button {
    display: flex;
    align-items: center;
    margin-left: 16px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
      margin-right: 2px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
      margin-top: 2px;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  .round-4 {
    border-radius: 4px;
  }
  .page-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .round-22 {
    border-radius: 22px;
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
