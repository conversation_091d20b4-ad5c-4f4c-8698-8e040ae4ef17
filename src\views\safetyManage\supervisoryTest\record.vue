<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { SUPERVISORY_TEST_COMPLETED, IS_NORMAL_OPERATION } from "@/enums";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/safety/spotCheckRecord/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "inspectorName",
                value: "检查人",
              },
              {
                key: "checkedPersonName",
                value: "被检查人",
              },
              {
                key: "checkedVehiclePlateNumber",
                value: "检查车辆",
              },
              {
                key: "spotCheckDate",
                value: "抽查日期",
              },
              {
                key: "isError",
                value: "是否危运车辆",
                options: IS_NORMAL_OPERATION,
              },
            ],
          },
          {
            title: "抽查信息",
            recordFieldArr: [
              {
                key: "rectifiedStatus",
                value: "是否完成整改",
                options: SUPERVISORY_TEST_COMPLETED,
              },
              {
                key: "problemDescription",
                value: "问题",
              },
              {
                key: "alcoholTestCount",
                value: "酒精测试/人",
              },
            ],
          },
          {
            title: "附件信息",
            recordFieldArr: [
              {
                key: "beforeRectificationPhotoUrl",
                value: "整改前照片",
                isImage: true,
              },
              {
                key: "afterRectificationPhotoUrl",
                value: "整改后照片",
                isImage: true,
              },
            ],
          },
        ],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped>
  .record-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 10px 0;
  }
  .record-box {
    padding: 0 12px;
    background-color: #fff;
    border-radius: 8px;
    margin-top: 12px;
    .title-icon {
      width: 16px;
      height: 16px;
    }
  }
  .record-detail {
    padding: 0 24px;
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    &.active {
      color: #f53f3f;
    }
  }
</style>
