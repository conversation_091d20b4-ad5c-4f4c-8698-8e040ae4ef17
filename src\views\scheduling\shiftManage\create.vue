<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <van-field
              v-model="ruleForm.applyFullName"
              name="applyFullName"
              label="申请人名称"
              placeholder="请填写申请人名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.applyFullName"
              readonly
              required
              maxlength="32"
            />
            <van-field
              v-model="ruleForm.applyPhone"
              name="applyPhone"
              label="申请人联系电话"
              placeholder="请填写申请人联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.applyPhone"
              required
              label-width="120"
              maxlength="11"
            />
            <van-field
              v-model="ruleForm.changeStartDate"
              name="changeStartDate"
              label="换班开始日期"
              placeholder="请选择换班开始日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.changeStartDate"
              required
              @click="showStartDatePicker = true"
            />
            <van-field
              v-model="ruleForm.changeEndDate"
              name="changeEndDate"
              label="换班结束日期"
              placeholder="请选择换班结束日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.changeEndDate"
              required
              @click="showEndDatePicker = true"
            />
            <van-field name="remark" label="详情备注" label-width="100%" required :border="false" />
            <van-field
              v-model="ruleForm.remark"
              name="remark"
              label=""
              placeholder="请填写详情备注"
              type="textarea"
              :border="false"
              :autosize="{ maxHeight: 200, minHeight: 100 }"
              :maxlength="800"
              show-word-limit
              :rules="rules.remark"
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-22"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showStartDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentStartDate"
        :min-date="minStartDate"
        :max-date="maxStartDate"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showEndDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentEndDate"
        :min-date="minEndDate"
        :max-date="maxEndDate"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  export default {
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/waybill/change/applyChange",
        },
        ruleForm: {
          applyFullName: "", //申请人名称
          applyPhone: "", //申请人联系电话
          changeStartDate: moment().format("YYYY-MM-DD"), //换班开始日期
          changeEndDate: "", //换班结束日期
          remark: "", //详情备注
        },
        rules: {
          applyFullName: [{ required: true, message: "请填写申请人名称" }],
          applyPhone: [{ required: true, message: "请填写申请人联系电话" }],
          changeStartDate: [{ required: true, message: "请选择换班开始日期" }],
          changeEndDate: [{ required: true, message: "请选择换班结束日期" }],
          remark: [{ required: true, message: "请填写详情备注" }],
        },
        showStartDatePicker: false,
        currentStartDate: new Date(),
        minStartDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        showEndDatePicker: false,
        currentEndDate: new Date(),
        maxEndDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
      };
    },
    computed: {
      maxStartDate() {
        if (this.ruleForm.changeEndDate) {
          return new Date(moment(this.ruleForm.changeEndDate).format("YYYY/MM/DD"));
        }
        return new Date(moment().add(100, "years").format("YYYY/MM/DD"));
      },
      minEndDate() {
        if (this.ruleForm.changeStartDate) {
          return new Date(moment(this.ruleForm.changeStartDate).format("YYYY/MM/DD"));
        }
        return new Date(moment().add(-100, "years").format("YYYY/MM/DD"));
      },
    },
    created() {
      this.userInfo = getUserInfo();
      this.ruleForm.applyFullName = this.userInfo.fullName;
      this.ruleForm.applyPhone = this.userInfo.phone;
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {},
    methods: {
      // 确认换班开始日期
      onStartDateConfirm(value) {
        this.ruleForm.changeStartDate = moment(value).format("YYYY-MM-DD");
        this.showStartDatePicker = false;
      },
      // 确认换班结束日期
      onEndDateConfirm(value) {
        this.ruleForm.changeEndDate = moment(value).format("YYYY-MM-DD");
        this.showEndDatePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "申请换班成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
