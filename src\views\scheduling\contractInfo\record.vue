<template>
  <div class="create-container">
    <van-tabs v-model="step" animated>
      <van-tab title="">
        <div class="create-box">
          <header class="create-header">
            <div
              class="header-title"
              :class="{ active: index === 0 }"
              v-for="(item, index) in titleList"
              :key="index"
              @click="toggleTab(index)"
            >
              <div class="header-sort">{{ item.sort }}</div>
              <div>{{ item.name }}</div>
            </div>
          </header>
          <div class="create-content">
            <main class="create-main">
              <div class="record-item" v-for="(item, index) in contractList" :key="index">
                <div class="record-left">{{ item.name }}</div>
                <div class="record-right">{{ ruleForm[item.key] }}</div>
              </div>
            </main>
          </div>
        </div>
      </van-tab>
      <van-tab title="">
        <div class="create-box">
          <header class="create-header">
            <div
              class="header-title"
              :class="{ active: index === 1 }"
              v-for="(item, index) in titleList"
              :key="index"
              @click="toggleTab(index)"
            >
              <div class="header-sort">{{ item.sort }}</div>
              <div>{{ item.name }}</div>
            </div>
          </header>
          <div class="create-content">
            <main class="create-main">
              <div class="record-item" v-for="(item, index) in merchantList" :key="index">
                <div class="record-left">{{ item.name }}</div>
                <div class="record-right">{{ merchantInfo[item.key] }}</div>
              </div>
            </main>
          </div>
        </div>
      </van-tab>
      <van-tab title="">
        <div class="create-box">
          <header class="create-header">
            <div
              class="header-title"
              :class="{ active: index === 2 }"
              v-for="(item, index) in titleList"
              :key="index"
              @click="toggleTab(index)"
            >
              <div class="header-sort">{{ item.sort }}</div>
              <div>{{ item.name }}</div>
            </div>
          </header>
          <div class="create-content">
            <ul class="list" v-if="tableData.length > 0">
              <li class="list-item" v-for="item in tableData" :key="item.id">
                <div class="item-title">年度：{{ item.year }}&nbsp;&nbsp;月份：{{ item.month }}</div>
                <div class="item-text">收费类型：{{ item.chargeType }}</div>
                <div class="item-text">计数数量：{{ item.countQuantity }}</div>
                <div class="item-text">每月排放量（Kg）：{{ item.monthEmissions }}</div>
                <div class="item-text">每月金额（元）：{{ item.monthPrice }}</div>
                <div class="item-text">是否停运：{{ item.isStop == 1 ? "是" : "否" }}</div>
              </li>
            </ul>
            <van-empty description="暂无收费清单数据" v-else />
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
  import { getInfoApiFun, getListPageApiFun } from "@/api/base";
  export default {
    data() {
      return {
        id: "",
        step: 0, //新增步骤
        apis: {
          info: "/api/contract/contractinfo/get/",
          inventoryList: "/api/contract/contractinventory/list",
          merchantInfo: "/api/merchant/merchantFile/get/",
        },
        titleList: [
          { sort: "①", name: "合同信息" },
          { sort: "②", name: "客商信息" },
          { sort: "③", name: "收费清单" },
        ],
        contractList: [
          { key: "serialNumber", name: "合同流水号" },
          { key: "code", name: "合同编号" },
          { key: "name", name: "合同名称" },
          { key: "typeName", name: "合同类型" },
          { key: "effectiveDate", name: "生效日期" },
          { key: "expiryDate", name: "失效日期" },
          { key: "billingPeriod", name: "计费期数" },
          { key: "chargingCycle", name: "计费周期" },
          { key: "chargeMode", name: "计费方式" },
          { key: "pricingScheme", name: "价格方案" },
          { key: "dailyEmissions", name: "每日排放量（Kg）" },
          { key: "price", name: "单价" },
          { key: "countQuantity", name: "计数数量（Kg）" },
          { key: "monthEmissions", name: "每月排放量（Kg）" },
          { key: "monthPrice", name: "每月金额" },
          { key: "remark", name: "备注" },
        ],
        merchantList: [
          { key: "name", name: "客商名称" },
          { key: "address", name: "客商地址" },
          { key: "corporateName", name: "法人代表" },
          { key: "contact", name: "联系人" },
          { key: "contactPhone", name: "联系方式" },
        ],
        ruleForm: {},
        merchantInfo: {},
        tableData: [],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取合同详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          for (let key in this.ruleForm) {
            this.ruleForm[key] = this.ruleForm[key] || this.ruleForm[key] === 0 ? this.ruleForm[key] : "-";
          }
          this.getMerchantRecord(res.data.merchantFileId);
          this.getInventoryList(this.id);
        }
      },
      // 获取客商档案详情
      async getMerchantRecord(id) {
        let res = await getInfoApiFun(id, this.apis.merchantInfo);
        if (res.success) {
          this.merchantInfo = res.data;
          this.merchantInfo.contactPhone = this.merchantInfo.contactPhone
            ? this.$sm2Decrypt(this.merchantInfo.contactPhone)
            : "";
          for (let key in this.merchantInfo) {
            this.merchantInfo[key] =
              this.merchantInfo[key] || this.merchantInfo[key] === 0 ? this.merchantInfo[key] : "-";
          }
        }
      },
      // 获取收费清单列表
      async getInventoryList(id) {
        let params = {
          contractInfoId: id,
        };
        let res = await getListPageApiFun(params, this.apis.inventoryList);
        if (res.success) {
          this.tableData = res.data;
        }
      },
      // 切换tab栏
      toggleTab(index) {
        this.step = index;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
    padding: 0 12px;
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    word-break: break-all;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
