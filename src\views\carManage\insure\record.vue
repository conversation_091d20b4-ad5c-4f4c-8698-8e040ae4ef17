<template>
  <div>
    <pageRecord :params="params" :recordApi="apis.findByPlateNumberAndYear" :recordList="recordList" requestMode="post">
      <template #default="{ form }">
        <template v-if="form.insuranceType?.includes(0)">
          <div class="type-box">
            <div class="border-line"></div>
            <baseTitle class="record-title" title="交强险记录"></baseTitle>
            <div class="record-content" v-for="(item, index) in typeField[0]" :key="index">
              <div class="img-box" v-if="item.isImage">
                <div class="record-left">{{ item.value }}</div>
                <div class="img-list">
                  <img
                    class="img-item"
                    :src="imgItem.url"
                    alt=""
                    v-for="(imgItem, imgIndex) in form[item.key]"
                    :key="imgIndex"
                    @click="imagePreview(form[item.key], imgIndex)"
                  />
                </div>
              </div>
              <div class="record-item" v-else>
                <div class="record-left"
                  >{{ item.value }}<span v-if="item.tip">({{ item.tip }})</span></div
                >
                <div class="record-right">{{ form[item.key] }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="form.insuranceType?.includes(1)">
          <div class="type-box">
            <div class="border-line"></div>
            <baseTitle class="record-title" title="商业险记录"></baseTitle>
            <div class="record-content" v-for="(item, index) in typeField[1]" :key="index">
              <div class="img-box" v-if="item.isImage">
                <div class="record-left">{{ item.value }}</div>
                <div class="img-list">
                  <img
                    class="img-item"
                    :src="imgItem.url"
                    alt=""
                    v-for="(imgItem, imgIndex) in form[item.key]"
                    :key="imgIndex"
                    @click="imagePreview(form[item.key], imgIndex)"
                  />
                </div>
              </div>
              <div class="record-item" v-else>
                <div class="record-left"
                  >{{ item.value }}<span v-if="item.tip">({{ item.tip }})</span></div
                >
                <div class="record-right">{{ form[item.key] }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="form.insuranceType?.includes(2)">
          <div class="type-box">
            <div class="border-line"></div>
            <baseTitle class="record-title" title="承运险记录"></baseTitle>
            <div class="record-content" v-for="(item, index) in typeField[2]" :key="index">
              <div class="img-box" v-if="item.isImage">
                <div class="record-left">{{ item.value }}</div>
                <div class="img-list">
                  <img
                    class="img-item"
                    :src="imgItem.url"
                    alt=""
                    v-for="(imgItem, imgIndex) in form[item.key]"
                    :key="imgIndex"
                    @click="imagePreview(form[item.key], imgIndex)"
                  />
                </div>
              </div>
              <div class="record-item" v-else>
                <div class="record-left"
                  >{{ item.value }}<span v-if="item.tip">({{ item.tip }})</span></div
                >
                <div class="record-right">{{ form[item.key] }}</div>
              </div>
            </div>
          </div>
        </template>
      </template>
    </pageRecord>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex"></van-image-preview>
  </div>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { INSURANCE_TYPE } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: { pageRecord, baseTitle },
    data() {
      return {
        params: {},
        apis: {
          findByPlateNumberAndYear: "/api/vehicleInsure/findByPlateNumberAndYear",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "year",
                value: "所属年份",
              },
              {
                key: "insuranceType",
                value: "投保类型",
                options: INSURANCE_TYPE,
                isMultiple: true,
              },
            ],
          },
        ],
        typeField: [
          [
            { key: "operatorName", value: "经办人" },
            { key: "policyNo", value: "保单号" },
            { key: "money", value: "保单金额", tip: "元" },
            { key: "insuranceCompany", value: "保单公司" },
            { key: "policyEffectiveDate", value: "保单开始日期" },
            { key: "policyTerminationDate", value: "保单结束日期" },
            { key: "vehicleFileList", value: "凭证", isImage: true },
          ],
          [
            { key: "syOperatorName", value: "经办人" },
            { key: "syPolicyNo", value: "保单号" },
            { key: "syMoney", value: "保单金额", tip: "元" },
            { key: "syInsuranceCompany", value: "保单公司" },
            { key: "syPolicyEffectiveDate", value: "保单开始日期" },
            { key: "syPolicyTerminationDate", value: "保单结束日期" },
            { key: "syVehicleFileList", value: "凭证", isImage: true },
          ],
          [
            { key: "cyOperatorName", value: "经办人" },
            { key: "cyPolicyNo", value: "保单号" },
            { key: "cyMoney", value: "保单金额", tip: "元" },
            { key: "cyInsuranceCompany", value: "保单公司" },
            { key: "cyPolicyEffectiveDate", value: "保单开始日期" },
            { key: "cyPolicyTerminationDate", value: "保单结束日期" },
            { key: "cyVehicleFileList", value: "凭证", isImage: true },
          ],
        ],
        previewImages: [],
        previewIndex: 0,
        showPreview: false,
      };
    },
    created() {
      this.params = this.$route.query;
    },
    methods: {
      // 图片预览
      imagePreview(fileList, index) {
        this.previewImages = fileList.map((list) => list.url);
        this.previewIndex = index;
        this.showPreview = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .type-box {
    border-radius: 8px;
  }
  .type-title {
    font-weight: 500;
    font-size: 16px;
    color: #4ca786;
    line-height: 22px;
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
  }
  .img-box {
    width: 100%;
    padding-top: 10px;
  }
  .img-list {
    margin-top: 8px;
    display: grid;
    grid-gap: 8px;
    grid-template-columns: repeat(5, 1fr);
  }
  .img-item {
    width: 60px;
    height: 60px;
    overflow: hidden;
  }
</style>
