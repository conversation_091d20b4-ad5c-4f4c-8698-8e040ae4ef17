<template>
  <div class="page-container">
    <header class="page-header">
      <div class="header-input">
        <van-field
          :value="waybillTime"
          label=""
          placeholder="请选择收运日期"
          is-link
          arrow-direction="down"
          label-width="0"
          readonly
          :border="false"
          @click="showPicker = true"
        />
      </div>
    </header>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="item-content">
                <div class="item-left">
                  <div class="item-title">收运单编号：{{ item.waybillCode }}</div>
                  <div class="item-text">产废单位名称：{{ item.productionUnit }}</div>
                  <div class="item-text">第一承运人（司机）：{{ item.firstCarrier }}</div>
                  <div class="item-text">废物总量（kg）：{{ item.waybillStatus === 0 ? "-" : item.rubbishTotal }}</div>
                  <div class="item-text">收运时间：{{ item.waybillStatus === 0 ? "-" : item.waybillTime }}</div>
                </div>
                <div class="item-right" v-if="item.waybillStatus === 0">待收运</div>
                <template v-else>
                  <div class="item-right" @click="itemClick(item)">{{
                    item.verifyStatus ? (item.verifyStatus === 1 ? "已确认" : "需核实") : "待确认"
                  }}</div>
                </template>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
    <van-popup v-model="showPicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        cancel-button-text="重置"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
    </van-popup>
  </div>
</template>

<script>
  import components from "@/components/pageList/componentImport";
  import { getListPageApiFun } from "@/api/base";
  import moment from "moment";
  export default {
    components: components,
    data() {
      return {
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        tableList: [],
        waybillTime: "",
        apis: {
          listPage: "/api/waybill/waybillDetail/merchantFileListPage",
        },
        showPicker: false,
        currentDate: new Date(),
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          waybillTime: this.waybillTime,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            let dataList = res.data.datas;
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 列表项点击事件
      itemClick(item) {
        this.$commonSkip("merchantWaybillRecord", { id: item.id });
      },
      // 日期确认事件回调
      onConfirm(value) {
        this.waybillTime = moment(value).format("YYYY-MM-DD");
        this.onRefresh();
        this.showPicker = false;
      },
      // 重置
      onCancel() {
        this.waybillTime = "";
        this.onRefresh();
        this.showPicker = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
  }
  .header-input {
    flex: 1;
    overflow: hidden;
  }
  .filter-button {
    display: flex;
    align-items: center;
    margin-left: 16px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
      margin-right: 2px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
      margin-top: 2px;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    font-weight: 400;
    font-size: 14px;
    color: #fff;
    line-height: 20px;
    padding: 6px 16px;
    border-radius: 16px;
    background-color: rgba(76, 167, 134, 1);
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .filter-box {
    background-color: #fff;
  }
  .filter-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    margin-bottom: 16px;
  }
  .reset-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #dce0df;
    border-radius: 20px;
  }
  .search-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #4ca786;
    border-radius: 20px;
    margin-left: 8px;
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  .item-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    .footer-box {
      padding: 6px 8px;
      background: #f0f2f1;
      border-radius: 3px;
      opacity: 0.5;
      display: flex;
      align-items: center;
      .footer-text {
        font-weight: 400;
        font-size: 12px;
        color: #5c6663;
        line-height: 14px;
        margin-right: 4px;
      }
      .footer-icon {
        font-size: 14px;
        color: #5c6663;
      }
    }
  }
  ::v-deep .page-header .van-cell {
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
