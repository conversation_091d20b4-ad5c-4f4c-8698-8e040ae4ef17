<template>
  <div class="safe-wrapper">
    <div class="card">
      <div class="title">本月车辆日常安检合格率</div>
      <div class="flex-wrapper">
        <div class="echarts-wrapper">
          <div class="echarts-wrapper" id="main"> </div>
          <div class="num">
            <div class="num-text">{{ num }}<span class="beside">%</span></div>
            <div class="num-sub">合格率</div>
          </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="title">本月安全活动次数</div>
      <div class="flex-wrapper">
        <div class="img-wrapper">
          <img class="img-num" src="./images/num.png" alt="" />
          <div class="num">{{ summaryData.activeNum }}<span class="beside">次</span></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";

  import * as echarts from "echarts";
  export default {
    components: {},
    props: {},
    data() {
      return {
        num: 40,
        api: {
          getData: "/api/generalization/operate/safety",
        },
        summaryData: {
          activeNum: 0,
          inspectionPrecentage: 0,
        },
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
      this.dataCb();

      this.init();
    },
    methods: {
      dataCb() {
        let value = (this.summaryData.inspectionPrecentage * 100).toFixed(2);
        value = parseFloat(value);
        this.num = value;
      },
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          this.summaryData = data.data;
        } catch (error) {
          console.log(error);
        }
      },
      init() {
        var myChart = echarts.init(document.getElementById("main"));
        // 绘制图表
        myChart.setOption({
          color: ["#25C68C", "#EEFCF7"],
          series: [
            {
              clockwise: false,
              type: "pie",
              silent: true, // 禁止点击事件
              radius: ["55%", "85%"],

              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              data: [
                { value: this.num },
                {
                  value: 100 - this.num,
                  itemStyle: {
                    color: "transparent",
                  },
                },
              ],
            },
            {
              type: "pie",
              silent: true, // 禁止点击事件
              clockwise: false,
              radius: ["60%", "80%"],

              label: {
                show: false,
                position: "center",
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: "bold",
                },
              },
              labelLine: {
                show: false,
              },
              data: [
                {
                  value: this.num,
                  itemStyle: {
                    color: "transparent",
                  },
                },
                { value: 100 - this.num },
              ],
            },
          ],
        });
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .safe-wrapper {
    padding: 12px 12px 1px 12px;
    background-color: #f7faf9;
    flex: 1;
    overflow: auto;
  }
  .title {
    font-weight: bold;
    font-size: 16px;
    line-height: 22px;
  }
  .card {
    background-color: #fff;
    padding: 10px 12px;
    margin-bottom: 12px;
  }
  .img-wrapper {
    position: relative;
    margin-top: 20px;
    margin-bottom: 17px;
    width: 136px;
  }
  .echarts-wrapper {
    position: relative;
    width: 136px;
    height: 136px;
  }
  .img-num {
    width: 136px;
  }
  .flex-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .beside {
    font-size: 14px;
    color: #5c6663;
    line-height: 19px;
  }
  .num {
    top: 50px;
    left: 50px;
    position: absolute;
    font-weight: bold;
    font-size: 20px;
    color: #2e3432;
    line-height: 26px;
  }
  .num-sub {
    font-size: 12px;
    line-height: 16px;
    color: #5c6663;
  }
</style>
