[{"D:\\WorkSpace\\logan-oa-website\\src\\api\\design.js": "1", "D:\\WorkSpace\\logan-oa-website\\src\\api\\org.js": "2", "D:\\WorkSpace\\logan-oa-website\\src\\api\\process.js": "3", "D:\\WorkSpace\\logan-oa-website\\src\\api\\request.js": "4", "D:\\WorkSpace\\logan-oa-website\\src\\App.vue": "5", "D:\\WorkSpace\\logan-oa-website\\src\\assets\\iconfont\\iconfont.js": "6", "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\Ellipsis.vue": "7", "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\OrgPicker.vue": "8", "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\Tip.vue": "9", "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\WDialog.vue": "10", "D:\\WorkSpace\\logan-oa-website\\src\\enums\\cache-enum.js": "11", "D:\\WorkSpace\\logan-oa-website\\src\\main.js": "12", "D:\\WorkSpace\\logan-oa-website\\src\\public-path.js": "13", "D:\\WorkSpace\\logan-oa-website\\src\\router\\index.js": "14", "D:\\WorkSpace\\logan-oa-website\\src\\store\\index.js": "15", "D:\\WorkSpace\\logan-oa-website\\src\\utils\\CustomUtil.js": "16", "D:\\WorkSpace\\logan-oa-website\\src\\utils\\index.js": "17", "D:\\WorkSpace\\logan-oa-website\\src\\utils\\request.js": "18", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\FormProcessDesign.vue": "19", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\FormsPanel.vue": "20", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\form\\FormDesignRender.vue": "21", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\FormBaseSetting.vue": "22", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\FormDesign.vue": "23", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\FormProSetting.vue": "24", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\process\\DefaultNodeProps.js": "25", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\process\\ProcessTree.vue": "26", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\process\\ProcessTreeViewer.vue": "27", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\ProcessDesign.vue": "28", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\layout\\ProcessDiagramViewer.vue": "29", "D:\\WorkSpace\\logan-oa-website\\src\\views\\admin\\LayoutHeader.vue": "30", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\ComponentExport.js": "31", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\ComponentMinxins.js": "32", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\AmountInput.vue": "33", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\DateTime.vue": "34", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\DateTimeRange.vue": "35", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\DeptPicker.vue": "36", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\Description.vue": "37", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\FileUpload.vue": "38", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\ImageUpload.vue": "39", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\Location.vue": "40", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\MoneyInput.vue": "41", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\MultipleSelect.vue": "42", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\NumberInput.vue": "43", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\SelectInput.vue": "44", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\SignPannel.vue": "45", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\SpanLayout.vue": "46", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\TableList.vue": "47", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\TextareaInput.vue": "48", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\TextInput.vue": "49", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\components\\UserPicker.vue": "50", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\ComponentsConfigExport.js": "51", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\AmountInputConfig.vue": "52", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\DateTimeConfig.vue": "53", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\DateTimeRangeConfig.vue": "54", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\DescriptionConfig.vue": "55", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\FileUploadConfig.vue": "56", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\ImageUploadConfig.vue": "57", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\LocationConfig.vue": "58", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\MoneyInputConfig.vue": "59", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\NumberInputConfig.vue": "60", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\OrgPickerConfig.vue": "61", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\SelectInputConfig.vue": "62", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\TableListConfig.vue": "63", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\TextareaInputConfig.vue": "64", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\config\\TextInputConfig.vue": "65", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\FormComponentConfig.vue": "66", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\form\\FormRender.vue": "67", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\InsertButton.vue": "68", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\ApprovalNodeConfig.vue": "69", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\CcNodeConfig.vue": "70", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\ConditionGroupItemConfig.vue": "71", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\ConditionNodeConfig.vue": "72", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\DelayNodeConfig.vue": "73", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\FormAuthorityConfig.vue": "74", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\NodeConfig.vue": "75", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\RootNodeConfig.vue": "76", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\config\\TriggerNodeConfig.vue": "77", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\ApprovalNode.vue": "78", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\CcNode.vue": "79", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\ConcurrentNode.vue": "80", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\ConditionNode.vue": "81", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\DelayNode.vue": "82", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\EmptyNode.vue": "83", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\Node.vue": "84", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\RootNode.vue": "85", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\nodes\\TriggerNode.vue": "86", "D:\\WorkSpace\\logan-oa-website\\src\\views\\common\\process\\OrgItems.vue": "87", "D:\\WorkSpace\\logan-oa-website\\src\\views\\Index.vue": "88", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\form.js": "89", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\InitiateProcess.vue": "90", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\process\\AgreenForm.vue": "91", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\process\\ProcessForm.vue": "92", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\process\\ProcessInstanceTabs.vue": "93", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\task\\ApplyTask.vue": "94", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\task\\DoneTask.vue": "95", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\task\\TodoTask.vue": "96", "D:\\WorkSpace\\logan-oa-website\\src\\views\\workspace\\WorkSpace.vue": "97"}, {"size": 4642, "mtime": 1676878530503, "results": "98", "hashOfConfig": "99"}, {"size": 565, "mtime": 1676878417149, "results": "100", "hashOfConfig": "99"}, {"size": 0, "mtime": 1676875804048, "results": "101", "hashOfConfig": "99"}, {"size": 1577, "mtime": 1676877849809, "results": "102", "hashOfConfig": "99"}, {"size": 929, "mtime": 1676878890231, "results": "103", "hashOfConfig": "99"}, {"size": 70871, "mtime": 1676879664963, "results": "104", "hashOfConfig": "99"}, {"size": 1033, "mtime": 1676879664963, "results": "105", "hashOfConfig": "99"}, {"size": 13526, "mtime": 1676879664963, "results": "106", "hashOfConfig": "99"}, {"size": 647, "mtime": 1676879664963, "results": "107", "hashOfConfig": "99"}, {"size": 2275, "mtime": 1676879664963, "results": "108", "hashOfConfig": "99"}, {"size": 141, "mtime": 1676874940527, "results": "109", "hashOfConfig": "99"}, {"size": 2231, "mtime": 1676882790340, "results": "110", "hashOfConfig": "99"}, {"size": 154, "mtime": 1676874940529, "results": "111", "hashOfConfig": "99"}, {"size": 2290, "mtime": 1676879540992, "results": "112", "hashOfConfig": "99"}, {"size": 596, "mtime": 1676878665306, "results": "113", "hashOfConfig": "99"}, {"size": 541, "mtime": 1676879664963, "results": "114", "hashOfConfig": "99"}, {"size": 889, "mtime": 1676879664963, "results": "115", "hashOfConfig": "99"}, {"size": 4160, "mtime": 1676877999639, "results": "116", "hashOfConfig": "99"}, {"size": 9352, "mtime": 1676875804056}, {"size": 11463, "mtime": 1676875804057}, {"size": 821, "mtime": 1676875804060}, {"size": 8159, "mtime": 1676875804057}, {"size": 13961, "mtime": 1676875804058}, {"size": 1040, "mtime": 1676875804058}, {"size": 2628, "mtime": 1676875804060}, {"size": 16507, "mtime": 1676875804060}, {"size": 14851, "mtime": 1676875804061}, {"size": 2626, "mtime": 1676875804058}, {"size": 1955, "mtime": 1676875804059}, {"size": 4042, "mtime": 1676875804057}, {"size": 1431, "mtime": 1676875804061}, {"size": 383, "mtime": 1676875804062}, {"size": 4259, "mtime": 1676875804064}, {"size": 1267, "mtime": 1676875804064}, {"size": 3505, "mtime": 1676875804064}, {"size": 1906, "mtime": 1676876209882}, {"size": 591, "mtime": 1676875804064}, {"size": 3462, "mtime": 1676875804065}, {"size": 4266, "mtime": 1676875804065}, {"size": 189, "mtime": 1676875804066}, {"size": 191, "mtime": 1676875804066}, {"size": 1810, "mtime": 1676875804066}, {"size": 850, "mtime": 1676875804066}, {"size": 1623, "mtime": 1676875804066}, {"size": 191, "mtime": 1676875804067}, {"size": 5655, "mtime": 1676875804067}, {"size": 9357, "mtime": 1676876244720}, {"size": 1089, "mtime": 1676875804068}, {"size": 826, "mtime": 1676875804067}, {"size": 2254, "mtime": 1676876193896}, {"size": 5362, "mtime": 1676875804062}, {"size": 825, "mtime": 1676875804068}, {"size": 894, "mtime": 1676875804068}, {"size": 1121, "mtime": 1676875804069}, {"size": 616, "mtime": 1676875804069}, {"size": 1707, "mtime": 1676875804069}, {"size": 1327, "mtime": 1676875804070}, {"size": 189, "mtime": 1676875804070}, {"size": 191, "mtime": 1676875804070}, {"size": 472, "mtime": 1676875804071}, {"size": 590, "mtime": 1676875804071}, {"size": 2253, "mtime": 1676875804071}, {"size": 1692, "mtime": 1676875804071}, {"size": 474, "mtime": 1676875804072}, {"size": 453, "mtime": 1676875804071}, {"size": 2050, "mtime": 1676875804062}, {"size": 2606, "mtime": 1676875804062}, {"size": 2475, "mtime": 1676875804061}, {"size": 12145, "mtime": 1676876325333}, {"size": 1394, "mtime": 1676875804073}, {"size": 10918, "mtime": 1676875804073}, {"size": 3772, "mtime": 1676875804073}, {"size": 1453, "mtime": 1676875804074}, {"size": 3828, "mtime": 1676875804074}, {"size": 1621, "mtime": 1676875804075}, {"size": 1265, "mtime": 1676875804075}, {"size": 8528, "mtime": 1676875804075}, {"size": 4231, "mtime": 1676875804075}, {"size": 1549, "mtime": 1676875804076}, {"size": 4384, "mtime": 1676875804076}, {"size": 9597, "mtime": 1676875804076}, {"size": 1963, "mtime": 1676875804076}, {"size": 294, "mtime": 1676875804077}, {"size": 4767, "mtime": 1676875804077}, {"size": 1063, "mtime": 1676875804078}, {"size": 1656, "mtime": 1676875804078}, {"size": 840, "mtime": 1676875804072}, {"size": 3613, "mtime": 1676875804056, "results": "117", "hashOfConfig": "99"}, {"size": 1121, "mtime": 1676875804079}, {"size": 2200, "mtime": 1676875804078}, {"size": 1238, "mtime": 1676875804079}, {"size": 4385, "mtime": 1676875804079}, {"size": 3552, "mtime": 1676875804079}, {"size": 2083, "mtime": 1676875804080}, {"size": 1989, "mtime": 1676875804080}, {"size": 2108, "mtime": 1676875804080}, {"size": 6594, "mtime": 1676875804078}, {"filePath": "118", "messages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ux75u2", {"filePath": "120", "messages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WorkSpace\\logan-oa-website\\src\\api\\design.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\api\\org.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\api\\process.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\api\\request.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\App.vue", [], "D:\\WorkSpace\\logan-oa-website\\src\\assets\\iconfont\\iconfont.js", ["156", "157", "158", "159", "160", "161", "162", "163"], "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\Ellipsis.vue", [], "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\OrgPicker.vue", ["164"], "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\Tip.vue", [], "D:\\WorkSpace\\logan-oa-website\\src\\components\\common\\WDialog.vue", [], "D:\\WorkSpace\\logan-oa-website\\src\\enums\\cache-enum.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\main.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\public-path.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\router\\index.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\store\\index.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\utils\\CustomUtil.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\utils\\index.js", [], "D:\\WorkSpace\\logan-oa-website\\src\\utils\\request.js", ["165"], "D:\\WorkSpace\\logan-oa-website\\src\\views\\Index.vue", [], {"ruleId": "166", "severity": 2, "message": "167", "line": 1, "column": 1, "nodeType": "168", "messageId": "169", "endLine": 63, "endColumn": 12}, {"ruleId": "166", "severity": 2, "message": "167", "line": 20, "column": 7, "nodeType": "168", "messageId": "169", "endLine": 20, "endColumn": 33}, {"ruleId": "166", "severity": 2, "message": "167", "line": 24, "column": 5, "nodeType": "168", "messageId": "169", "endLine": 24, "endColumn": 26}, {"ruleId": "166", "severity": 2, "message": "167", "line": 34, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 62, "endColumn": 13}, {"ruleId": "166", "severity": 2, "message": "167", "line": 37, "column": 5, "nodeType": "168", "messageId": "169", "endLine": 46, "endColumn": 81}, {"ruleId": "170", "severity": 2, "message": "171", "line": 45, "column": 14, "nodeType": "172", "messageId": "173", "endLine": 45, "endColumn": 15}, {"ruleId": "166", "severity": 2, "message": "167", "line": 52, "column": 13, "nodeType": "168", "messageId": "169", "endLine": 52, "endColumn": 74}, {"ruleId": "166", "severity": 2, "message": "167", "line": 61, "column": 11, "nodeType": "168", "messageId": "169", "endLine": 61, "endColumn": 78}, {"ruleId": "174", "severity": 1, "message": "175", "line": 178, "column": 19, "nodeType": "172", "messageId": "176", "endLine": 178, "endColumn": 22}, {"ruleId": "174", "severity": 1, "message": "177", "line": 159, "column": 38, "nodeType": "172", "messageId": "176", "endLine": 159, "endColumn": 44}, "no-unused-expressions", "Expected an assignment or function call and instead saw an expression.", "ExpressionStatement", "unusedExpression", "no-self-assign", "'a' is assigned to itself.", "Identifier", "selfAssignment", "no-unused-vars", "'err' is defined but never used.", "unusedVar", "'config' is assigned a value but never used."]