<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="plateNumber"
    searchPlaceholder="请输入车牌号"
    :hasRightIcon="false"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listPageApi: "/api/vehicleWeigh/listPage",
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "weighTime",
            value: "过磅时间",
          },
          {
            key: "grossWeight",
            value: "毛重量（kg）",
          },
          {
            key: "tareWeight",
            value: "皮重量（kg）",
          },
          {
            key: "netWeight",
            value: "净重量（kg）",
          },
        ],
        filterList: [
          {
            type: "Date",
            key: "weighTime",
            value: "过磅日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
  };
</script>

<style lang="scss" scoped></style>
