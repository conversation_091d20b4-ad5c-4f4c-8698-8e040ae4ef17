<template>
  <div class="container">
    <header class="header-tab bg-white">
      <ul class="header-tab-list">
        <li
          class="header-tab-item"
          :class="{ active: activeTab === index }"
          v-for="(item, index) in tabList"
          :key="index"
          @click="toggleHeader(index)"
        >
          <div class="header-tab-item-title">{{ item }}</div>
          <div class="header-tab-item-line"></div>
        </li>
      </ul>
    </header>
    <div class="search-box">
      <van-search
        v-model.trim="productionUnit"
        placeholder="请输入点位名称"
        clearable
        @search="onRefresh"
        @clear="onRefresh"
      />
    </div>
    <div class="commission-tabs" v-if="commissionList.length > 0">
      <van-tabs
        v-model="activeWaybill"
        color="#4ca786"
        title-active-color="#4ca786"
        line-width="60px"
        :ellipsis="false"
        @change="onRefresh"
      >
        <van-tab :title="item.name" v-for="(item, index) in commissionList" :key="index"></van-tab>
      </van-tabs>
    </div>
    <div class="waybill-space"></div>
    <div class="info-box" v-if="commissionList.length > 0">
      <ul class="info-list">
        <li class="info-item" v-for="(item, index) in infoArr" :key="index">
          <div class="info-item-left">
            <span class="el-icon-user-solid info-icon" v-if="index <= 2"></span>
            <span class="el-icon-s-promotion info-icon" v-else></span>
          </div>
          <div class="info-item-right">
            <div class="info-item-label">{{ item.label }}</div>
            <div class="info-item-value">{{ commissionList[activeWaybill][item.key] || "-" }}</div>
          </div>
        </li>
      </ul>
      <div class="popover-box">
        <van-popover
          v-model="showPopover"
          trigger="click"
          :actions="popupActions"
          @select="handlePopupSelect"
          placement="bottom-end"
          :get-container="getContainer"
        >
          <template #reference>
            <van-icon class="popover-icon" name="ellipsis" size="20" />
          </template>
        </van-popover>
      </div>
    </div>
    <main class="main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="item" v-for="(item, index) in tableList" :key="item.id" @click="viewRecord(item)">
              <div class="item-left">{{ index + 1 }}</div>
              <div class="item-right">
                <div class="item-top">
                  <div class="is-flex top-box">
                    <div class="item-title pi-one-line-clamp">{{ item.productionUnit }}</div>
                    <img
                      class="item-img"
                      src="@/assets/images/ic_luxian.png"
                      @click.stop="openMap(item)"
                      v-if="
                        activeTab === 0 && item.longitude && item.longitude != 0 && item.latitude && item.latitude != 0
                      "
                    />
                  </div>
                </div>
                <div class="is-flex flex-align-end">
                  <div class="item-text">
                    <span class="text-value">所属路线名称：{{ item.name }}</span>
                    <span class="text-value">地址：{{ item.address }}</span>
                    <span class="text-value"
                      >联系人：{{ item.productionUnitOperator }}&nbsp;&nbsp;{{ item.productionUnitOperatorPhone }}</span
                    >
                    <span
                      class="text-value"
                      v-if="moment().startOf('day').valueOf() < moment(item.effectiveDate).startOf('day').valueOf()"
                      >收运生效日期：{{ item.effectiveDate }}<span class="tip-text">（未到生效日期）</span></span
                    >
                    <span class="text-value">收运截止日期：{{ item.endDate }} </span>
                  </div>
                  <div
                    class="item-prompt"
                    v-if="
                      activeTab === 0 &&
                      moment().startOf('day').valueOf() >= moment(item.effectiveDate).startOf('day').valueOf() &&
                      userInfo.lgUnionId === item.defaultDriverDossierId
                    "
                    @click.stop="writeTask(item)"
                    >去确认</div
                  >
                </div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
    <div class="bottom-line"></div>
    <footer class="footer" v-if="activeTab === 0 && commissionList.length > 0">
      <div class="footer-button" @click="exceptReport">异常上报</div>
    </footer>
  </div>
</template>

<script>
  import { getUserInfo } from "@/utils/storage";
  import { getListPageApiFun, getInfoApiFun, createApiFun } from "@/api/base";
  import { wxAdvanceNavigateTo } from "@/utils/wechatRouter";
  import moment from "moment";
  export default {
    data() {
      return {
        userInfo: "",
        tabList: ["待收运", "已收运"],
        activeTab: 0,
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        tableList: [],
        apis: {
          listPage: "/api/waybill/commissionDetailListPage",
          roleList: "/api/userrole/currentRoleList",
          commissionList: "/api/waybill/commissionList",
        },
        routeTaskList: [],
        waybillType: "", //收运方式
        moment,
        levelList: [],
        productionUnit: "",
        activeWaybill: 0,
        commissionList: [],
        infoArr: [
          { key: "default_driver_dossier_name", label: "司机" },
          { key: "supercargo_dossier_one_name", label: "押运工1" },
          { key: "supercargo_dossier_two_name", label: "押运工2" },
          { key: "default_vehicle_plate_number", label: "车辆" },
        ],
        showPopover: false,
      };
    },
    computed: {
      popupActions() {
        let actions = [];
        if (this.levelList.includes(190)) {
          actions = actions.concat([{ text: "收运信息调整", id: 0 }]);
          if (this.commissionList[this.activeWaybill].is_operate_temp === 0) {
            actions = actions.concat([{ text: "增加点位收运任务", id: 1 }]);
          }
        }
        return actions;
      },
    },
    created() {
      this.userInfo = getUserInfo();
      this.getRoleList();
      this.getCommissionList();
    },
    mounted() {},
    methods: {
      getContainer() {
        return document.querySelector(".info-box");
      },
      // 获取权限列表
      async getRoleList() {
        let res = await getInfoApiFun("", this.apis.roleList);
        this.levelList = res.data.map((item) => item.level);
      },
      // 获取路线收运单列表
      async getCommissionList() {
        let res = await createApiFun({ waybillStatus: this.activeTab }, this.apis.commissionList);
        if (res.success) {
          this.commissionList = res.data;
          this.onRefresh();
        }
      },
      toggleHeader(index) {
        if (index === this.activeTab) {
          return;
        }
        this.commissionList = [];
        this.activeTab = index;
        this.activeWaybill = 0;
        this.getCommissionList();
      },
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          waybillStatus: this.activeTab,
          waybillId: this.commissionList[this.activeWaybill].id,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          productionUnit: this.productionUnit,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            let dataList = res.data.datas.map((item) => {
              return {
                ...item,
                productionUnitOperatorPhone: item.productionUnitOperatorPhone
                  ? this.$sm2Decrypt(item.productionUnitOperatorPhone)
                  : "",
              };
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        if (this.commissionList.length === 0) {
          this.tableList = [];
          this.finished = true;
          this.refreshing = false;
          return;
        }
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 打开地图
      openMap(item) {
        let { longitude, latitude } = item;
        wxAdvanceNavigateTo(`/pages/pointMap/index?longitude=${longitude}&latitude=${latitude}`);
      },
      // 填写电子收运单
      writeTask(item) {
        let params = {
          id: item ? item.id : "",
          productionUnit: item ? item.productionUnit : "",
          productionUnitOperator: item ? item.productionUnitOperator : "",
          waybillType: item ? item.waybillType : "",
          address: item ? item.address : "",
          waybillId: this.commissionList[this.activeWaybill].id,
        };
        this.$commonSkip("electronicWaybillCreate", params);
      },
      // 异常上报
      exceptReport() {
        this.$commonSkip("exceptReportCreate", { waybillId: this.commissionList[this.activeWaybill].id });
      },
      // 查看详情
      viewRecord(item) {
        if (this.activeTab === 0) {
          return;
        }
        this.$commonSkip("electronicWaybillRecord", { id: item.id });
      },
      // 调整车辆
      adjustCar() {
        this.$commonSkip("electronicWaybillAdjust");
      },
      // 处理弹窗选项
      handlePopupSelect(action) {
        switch (action.id) {
          case 0:
            this.$commonSkip("dispatchLedgerTemporary", { id: this.commissionList[this.activeWaybill].id });
            break;
          case 1:
            this.writeTask("");
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    display: flex;
    flex-direction: column;
  }
  .header-tab {
    padding: 12px;
    .header-tab-list {
      display: flex;
      align-items: center;
      .header-tab-item {
        padding-bottom: 4px;
        margin-right: 12px;
        &:last-child {
          margin-right: 0;
        }
        .header-tab-item-title {
          font-size: 14px;
          color: #162e25;
          line-height: 22px;
        }
        .header-tab-item-line {
          width: 100%;
          height: 2px;
          background-color: #4ca786;
          margin-top: 2px;
          border-radius: 2px;
          opacity: 0;
        }
        &.active {
          .header-tab-item-title {
            color: #4ca786;
            font-weight: 500;
          }
          .header-tab-item-line {
            opacity: 1;
          }
        }
      }
    }
  }
  .commission-tabs {
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e5e5;
    background-color: #fff;
  }
  .info-box {
    background-color: #fff;
    padding: 0 12px;
    position: relative;
    .popover-box {
      position: absolute;
      right: 12px;
      top: 12px;
      .popover-icon {
        transform: rotate(90deg);
      }
    }
  }
  .info-list {
    padding: 12px;
    background-color: #f9fafb;
    display: grid;
    grid-gap: 16px;
    grid-template-columns: repeat(2, 1fr);
  }
  .info-item {
    display: flex;
    align-items: center;
    .info-item-left {
      margin-right: 8px;
      .info-icon {
        font-size: 16px;
        color: #9ca3af;
      }
    }
    .info-item-label {
      font-size: 14px;
      color: #9ca3af;
    }
    .info-item-value {
      font-size: 14px;
      margin-top: 4px;
    }
  }
  .title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
    text-align: center;
  }
  .footer {
    padding: 12px 12px 0 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .footer-button {
    width: 100%;
    padding: 11px 0;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    background-color: #4ca786;
    border-radius: 32px;
    text-align: center;
  }
  .create-button {
    padding: 10px;
    text-align: right;
    font-weight: 400;
    font-size: 14px;
    color: #fff;
    line-height: 20px;
    background-color: #4ca786;
    border-radius: 20px;
  }
  .tab-list {
    display: flex;
    align-items: center;
    padding: 12px;
    .tab-item {
      padding: 10px 15px;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      background-color: #dce0df;
      margin-right: 8px;
      border-radius: 20px;
      &.active {
        background-color: #4ca786;
      }
    }
  }
  .main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-top: 0;
    background-color: #fff;
  }
  .item {
    padding: 12px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: flex-start;
    .item-left {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      border-radius: 50%;
      background-color: #4ca786;
      color: #fff;
      font-size: 12px;
      transform: translateY(4px);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .item-right {
      flex: 1;
      overflow: hidden;
    }
  }

  .item-icon {
    font-size: 20px;
    margin-right: 4px;
  }

  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    flex: 1;
  }

  .item-img {
    width: 32px;
    height: 32px;
    overflow: hidden;
    margin-left: 17px;
  }

  .item-text {
    display: block;
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    flex: 1;
  }

  .text-value {
    display: block;
    margin-top: 8px;
  }

  .item-prompt {
    font-weight: 400;
    font-size: 14px;
    color: #fff;
    line-height: 20px;
    padding: 6px 16px;
    border-radius: 16px;
    background-color: rgba(76, 167, 134, 1);
  }
  .button-box {
    justify-content: space-between;
    padding: 12px;
  }
  .tip-text {
    font-size: 10px;
    color: #f53f3f;
  }
  .top-box {
    .item-title {
      line-height: 30px;
    }
  }
  .bg-white {
    background-color: #fff;
  }
  ::v-deep .van-search {
    padding-top: 0;
  }
  ::v-deep .van-search .van-search__content {
    background-color: #f3f4f6;
    border-radius: 6px;
  }
  ::v-deep .van-tabs--line .van-tabs__wrap {
    height: 32px;
  }
  ::v-deep .van-popover__action {
    font-size: 12px;
    width: 160px;
  }
</style>
