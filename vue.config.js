/*
 * @Author: luowd <EMAIL>
 * @Date: 2023-02-15 15:30:49
 * @LastEditors: luowd <EMAIL>
 * @LastEditTime: 2023-02-17 14:59:49
 * @FilePath: \mirco-appdemo\vue.config.js
 * @Description:更改webpack配置
 */
const { defineConfig } = require("@vue/cli-service");
const path = require("path");
const FileManagerPlugin = require("filemanager-webpack-plugin"); //打包后文件压缩为zip插件
const name = require("./config/index").appName;
const port = require("./config/index").port;
var plugins = [];
if (process.env.NODE_ENV == "production") {
  //生产环境插件
  plugins = [
    new FileManagerPlugin({
      events: {
        onEnd: {
          delete: ["./web.zip"],
          archive: [{ source: "./dist", destination: "./web.zip" }],
        },
      },
    }),
  ];
}
module.exports = defineConfig({
  transpileDependencies: true,
  outputDir: "web",
  devServer: {
    port,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
  },
  publicPath: "",
  productionSourceMap: false,
  chainWebpack: (config) => {
    config.plugin("html").tap((args) => {
      args[0].title = "医疗废物智慧收运管理平台";
      return args;
    });
    config.plugin("module-federation-plugin").use(require("webpack").container.ModuleFederationPlugin, [
      {
        name: `logan_${name}`, //唯一ID，作为输出的模块名，使用的时通过 ${name}/${expose} 的方式使用
        library: { type: "umd", name: `logan_${name}` }, //库打包方式
        filename: `logan_${name}.js`, // 构建出来的文件名
        remotes: {
          //引入需要远程访问的文件
          "logan-webcom": "logan_webcom",
        },
        exposes: {
          //导出公共组件
        },
      },
    ]);
  },
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "@/styles/variables.scss";`, //引入全局变量
      },
    },
  },
  configureWebpack: {
    plugins,
    output: {
      library: `${name}-[name]`,
      libraryTarget: "umd", // 把微应用打包成 umd 库格式
      chunkLoadingGlobal: `webpackJsonp_${name}`,
      filename: "[name].[hash].js",
      chunkFilename: "[name].[hash].js",
    },
    externals: {
      //打包优化-减少打包组件
      vue: "Vue",
      "vue-router": "VueRouter",
      vuex: "Vuex",
      // "element-ui": "ElementUI",
    },
  },
  pluginOptions: {
    "style-resources-loader": {
      preProcessor: "less",
      patterns: [
        // 全局变量路径，不能使用路径别名
        // path.resolve(__dirname, "./src/assets/theme.less"),
      ],
    },
  },
});
