<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicle/annualReview/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "operatorName",
                value: "经办人",
              },
            ],
          },
          {
            title: "年审信息填报",
            recordFieldArr: [
              {
                key: "costs",
                value: "年审金额",
                unit: "元",
              },
              {
                key: "organizationName",
                value: "年审单位",
              },
              {
                key: "odd",
                value: "年审单号",
              },
              {
                key: "recentAnnualReviewTime",
                value: "最近年审日期",
              },
              {
                key: "nextAnnualReviewTime",
                value: "下次年审日期",
              },
            ],
          },
          {
            title: "年审凭证",
            recordFieldArr: [
              {
                key: "fileList",
                value: "凭证",
                isImage: true,
              },
            ],
          },
        ],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped></style>
