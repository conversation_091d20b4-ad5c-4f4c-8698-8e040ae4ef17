<template>
  <div class="detail-container" v-if="ruleForm">
    <van-field
      :value="PERFORMANCE_RULE[ruleIndex]"
      label="绩效方案名称："
      input-align="right"
      :border="false"
      required
      label-width="120px"
      readonly
    />
    <van-field value="" label="绩效计算公式：" input-align="right" :border="false" label-width="100%" readonly />
    <div class="formula">绩效工资标准 × 线路系数 × 考核系数 - 扣款金额</div>
    <van-field
      :value="ruleForm.collectTotal"
      label="诊所点位总收运数（个）："
      input-align="right"
      :border="false"
      label-width="230"
      readonly
    />
    <van-field
      :value="ruleForm.clinicRouteRatio"
      label="诊所线路系数："
      input-align="right"
      :border="false"
      label-width="120px"
      readonly
    />
    <van-field
      :value="ruleForm.attendDays"
      label="出勤天数："
      input-align="right"
      :border="false"
      label-width="120px"
      readonly
    />
    <van-field
      :value="ruleForm.ratio"
      label="考核系数："
      input-align="right"
      :border="false"
      label-width="120"
      readonly
    />
    <van-field
      :value="deductSalary"
      label="扣款金额（元）："
      input-align="right"
      :border="false"
      label-width="140"
      readonly
    />
    <van-field value="" label="绩效工资（元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`${ruleForm.standSlavery} x ${ruleForm.clinicRouteRatio} x ${ruleForm.ratio} - ${deductSalary} = ${ruleForm.payIncentives}`"
      label=""
      label-width="0"
      readonly
    />
    <van-field value="" label="点位收运绩效明细：" input-align="right" :border="false" label-width="100%" readonly />
    <ul class="detail-list">
      <li class="detail-item" v-for="(item, index) in ruleForm.detailInfo" :key="index">
        <div class="detail-left">
          <div class="detail-title">日期</div>
          <div class="detail-value">{{ item.date }}</div>
        </div>
        <div class="detail-middle">
          <div class="middle-item">
            <div class="middle-item-title">当日收运点位数量</div>
            <div class="middle-item-value">{{ item.dayCollectNum }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">当月累计收运点位数量</div>
            <div class="middle-item-value">{{ item.monthCollectNum }}</div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { PERFORMANCE_RULE } from "@/enums";
  export default {
    props: {
      detailId: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 2,
      },
      deductSalary: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        PERFORMANCE_RULE,
        apis: {
          info: "/api/access/record/detail/",
        },
        ruleForm: "",
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import "./detail.scss";
  .detail-middle {
    border-right: none;
  }
  .detail-list {
    max-height: 366px;
    overflow-y: auto;
  }
</style>
