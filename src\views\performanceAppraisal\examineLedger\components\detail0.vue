<template>
  <div class="detail-container" v-if="ruleForm">
    <van-field
      value="桶装/袋装收运月度绩效"
      label="绩效方案名称："
      input-align="right"
      :border="false"
      required
      label-width="120px"
      readonly
    />
    <van-field value="" label="绩效计算公式：" input-align="right" :border="false" label-width="100%" readonly />
    <van-field value="" label="桶装" input-align="right" :border="false" label-width="100%" readonly />
    <div class="formula">当月每日收运单价 × 当月内每日的总收运量 × 当月考核系数</div>
    <van-field value="" label="袋装" input-align="right" :border="false" label-width="100%" readonly />
    <div class="formula">收运单价 × 当月总收运量 × 当月总里程系数 × 当月考核系数</div>
    <van-field
      :value="ruleForm.bucketTotal"
      label="桶装垃圾收运绩效总计（元）："
      input-align="right"
      :border="false"
      label-width="240"
      readonly
    />
    <van-field
      :value="ruleForm?.bagTotal?.performance"
      label="袋装垃圾收运绩效总计（元）："
      input-align="right"
      :border="false"
      label-width="240"
      readonly
    />
    <van-field
      :value="ruleForm.ratio"
      label="考核系数："
      input-align="right"
      :border="false"
      label-width="120"
      readonly
    />
    <van-field
      :value="deductSalary"
      label="扣款金额（元）："
      input-align="right"
      :border="false"
      label-width="140"
      readonly
    />
    <van-field value="" label="绩效工资（元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`（${ruleForm.bucketTotal} + ${ruleForm.bagTotal[0]?.performance || 0}）x ${
        ruleForm.ratio
      } - ${deductSalary} = ${ruleForm.payIncentives}`"
      label=""
      label-width="0"
      readonly
    />
    <van-field value="" label="点位收运绩效明细：" input-align="right" :border="false" label-width="100%" readonly />
    <ul class="detail-list">
      <li class="detail-item" v-for="(item, index) in ruleForm.detailInfo" :key="index">
        <div class="detail-left">
          <div class="detail-title">日期</div>
          <div class="detail-value">{{ item.date }}</div>
        </div>
        <div class="detail-middle">
          <div class="middle-item">
            <div class="middle-item-title">桶装点位收运数量</div>
            <div class="middle-item-value">{{ item.bucketCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">当日桶装垃圾收运重量（kg）</div>
            <div class="middle-item-value">{{ item.bucketRubbishTotal }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">袋装点位收运数量</div>
            <div class="middle-item-value">{{ item.bagCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">当日袋装垃圾收运重量（kg）</div>
            <div class="middle-item-value">{{ item.bagRubbishTotal }}</div>
          </div>
        </div>
        <div class="detail-right">
          <div class="detail-title">当日绩效（元）</div>
          <div class="detail-value">{{ item.performance }}</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  export default {
    props: {
      detailId: {
        type: String,
        default: "",
      },
      deductSalary: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        apis: {
          info: "/api/access/record/detail/",
        },
        ruleForm: {},
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import "./detail.scss";
  .detail-list {
    max-height: 800px;
    overflow-y: auto;
  }
</style>
