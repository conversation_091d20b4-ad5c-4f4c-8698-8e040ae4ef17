<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordFieldArr="recordFieldArr"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { CERT_TYPES } from "@/enums";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/certificate/get/",
        },
        recordFieldArr: [
          {
            key: "certType",
            value: "证件类型",
            options: CERT_TYPES,
          },
          {
            key: "fullName",
            value: "所属人员",
          },
          {
            key: "certNo",
            value: "证件号",
          },
          {
            key: "photoFront",
            value: "证件照片-正面",
            isImage: true,
            isObject: true,
          },
          {
            key: "photoBack",
            value: "证件照片-反面",
            isImage: true,
            isObject: true,
          },
          {
            key: "validityBeginDate",
            value: "有效期开始时间",
          },
          {
            key: "validityEndDate",
            value: "有效期截止时间",
          },
          {
            key: "plateNumber",
            value: "所属车辆车牌号",
          },
          {
            key: "registrationDate",
            value: "登记日期",
          },
        ],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped>
  .record-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 10px 0;
  }
  .record-box {
    padding: 0 12px;
    background-color: #fff;
    border-radius: 8px;
    margin-top: 12px;
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    &.active {
      color: #f53f3f;
    }
  }
</style>
