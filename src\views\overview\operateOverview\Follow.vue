<template>
  <div class="safe-wrapper">
    <div class="card">
      <div class="title">本年各月回访率/满意率/投诉率分析</div>
      <div class="flex-wrapper">
        <div class="echarts-wrapper">
          <div class="echarts-wrapper" id="followUpModule"> </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";
  import * as echarts from "echarts";
  export default {
    components: {},
    props: {},
    data() {
      return {
        api: {
          getData: "/api/generalization/operate/custservice",
        },
        summaryData: {},
        xAxisData: [],
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
      this.dataCb();

      this.init();
    },
    methods: {
      getXAxisData() {
        // 获取当前日期
        const now = new Date();

        // 获取当前年份的当前月份（注意：月份是从0开始的，所以8月是7）
        const currentMonth = now.getMonth() + 1;

        // 使用Array.from()方法生成从1到当前月份的数组
        // 第二个参数是一个函数，它接受两个参数：index（当前索引）和array（当前数组，但在这个场景下我们不需要它）
        // 我们通过index+1来确保数组从1开始
        const monthsArray = Array.from({ length: currentMonth }, (_, index) => index + 1);
        this.xAxisData = monthsArray.map((item) => {
          return `${item}月`;
        });
      },
      dataCb() {
        this.getXAxisData();
        if (this.summaryData.followUpSituation) {
          this.getSeriesData1();
        }
        if (this.summaryData.satisfactionSituation) {
          this.getSeriesData2();
        }
        if (this.summaryData.complaintSituation) {
          this.getSeriesData3();
        }
      },
      getSeriesData1() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.followUpSituation.forEach((item) => {
          let value = parseFloat(item.followUpPrecentage.replace("%", ""));
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData1 = mappedUndefinedArray;
        console.log(this.seriesData1, "this.seriesData1");
      },
      getSeriesData2() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.satisfactionSituation.forEach((item) => {
          let value = parseFloat(item.satisfctionLevel.replace("%", ""));
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData2 = mappedUndefinedArray;
      },
      getSeriesData3() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.complaintSituation.forEach((item) => {
          let value = parseFloat(item.complaintPrecentage.replace("%", ""));
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData3 = mappedUndefinedArray;
      },
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          this.summaryData = data.data;
        } catch (error) {
          console.log(error);
        }
      },
      init() {
        var myChart = echarts.init(document.getElementById("followUpModule"));
        myChart.setOption({
          color: ["#006BF2", "#25C68C", "#E72A2A"],

          tooltip: {},
          legend: {
            top: "0",
            data: ["回访率", "满意率", "投诉率"],
          },
          xAxis: {
            type: "category",
            data: this.xAxisData,
          },
          grid: {
            top: "15%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          yAxis: [
            {
              type: "value",
              axisLabel: {
                formatter: "{value} %",
              },
            },
          ],
          series: [
            {
              silent: true, // 禁止点击事件

              name: "回访率",
              type: "line",
              data: this.seriesData1,
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}%", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
            {
              silent: true, // 禁止点击事件

              name: "满意率",
              type: "line",
              data: this.seriesData2,
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}%", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
            {
              silent: true, // 禁止点击事件

              name: "投诉率",
              type: "line",
              data: this.seriesData3,
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}%", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
          ],
        });
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .safe-wrapper {
    padding: 12px 12px 1px 12px;
    background-color: #f7faf9;
    flex: 1;
    overflow: auto;
  }
  .title {
    font-weight: bold;
    font-size: 16px;
    line-height: 22px;
  }
  .card {
    background-color: #fff;
    padding: 10px 12px;
    margin-bottom: 12px;
  }
  .echarts-wrapper {
    position: relative;
    width: 100%;
    height: 236px;
  }
  .flex-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
</style>
