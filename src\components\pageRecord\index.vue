<template>
  <div class="record-container" v-if="recordList.length > 0">
    <ul class="record-box">
      <div v-for="(list, listIndex) in recordList" :key="listIndex">
        <div class="border-line" v-if="listIndex !== 0"></div>
        <baseTitle class="record-title" :title="list.title"></baseTitle>
        <div class="record-content" v-for="(item, index) in list.recordFieldArr" :key="index">
          <template v-if="!(item.disabledKey && ruleForm[item.disabledKey] == item.disabledValue)">
            <div class="image-box" v-if="item.isImage">
              <div class="record-left">{{ item.value }}</div>
              <template v-if="ruleForm[item.key] && (isJSON(ruleForm[item.key]) || Array.isArray(ruleForm[item.key]))">
                <template v-if="item.isObject">
                  <ul class="image-list">
                    <img
                      class="image-item"
                      :src="
                        typeof ruleForm[item.key] == 'string'
                          ? JSON.parse(ruleForm[item.key]).url
                          : ruleForm[item.key]?.url
                      "
                      alt=""
                      @click="
                        imagePreview(
                          typeof ruleForm[item.key] == 'string'
                            ? [JSON.parse(ruleForm[item.key])]
                            : [ruleForm[item.key]],
                          0,
                        )
                      "
                    />
                  </ul>
                </template>
                <template v-else>
                  <ul
                    class="image-list"
                    v-if="
                      isJSON(ruleForm[item.key])
                        ? JSON.parse(ruleForm[item.key]).length > 0
                        : ruleForm[item.key]?.length > 0
                    "
                  >
                    <img
                      class="image-item"
                      :src="imgItem.url"
                      alt=""
                      v-for="(imgItem, imgIndex) in isJSON(ruleForm[item.key])
                        ? JSON.parse(ruleForm[item.key])
                        : ruleForm[item.key]"
                      :key="imgIndex"
                      @click="
                        imagePreview(
                          isJSON(ruleForm[item.key]) ? JSON.parse(ruleForm[item.key]) : ruleForm[item.key],
                          imgIndex,
                        )
                      "
                    />
                  </ul>
                </template>
              </template>
              <div class="record-right" v-else>-</div>
            </div>
            <div class="file-box" v-else-if="item.isFile">
              <div class="record-left"
                >{{ item.value }}<el-link type="info" :underline="false">（文件请前往管理端下载）</el-link></div
              >
              <div
                class="file-right"
                v-if="ruleForm[item.key] && (isJSON(ruleForm[item.key]) || Array.isArray(ruleForm[item.key]))"
              >
                <template v-if="item.isObject">
                  <el-link type="success">{{
                    typeof ruleForm[item.key] == "string"
                      ? JSON.parse(ruleForm[item.key]).name
                      : ruleForm[item.key]?.name
                  }}</el-link>
                </template>
                <template v-else>
                  <div v-if="Array.isArray(ruleForm[item.key]) && ruleForm[item.key].length > 0">
                    <el-link
                      class="link-block"
                      type="primary"
                      v-for="(fileItem, fileIndex) in ruleForm[item.key]"
                      :key="fileIndex"
                      >{{ fileItem.name }}</el-link
                    >
                  </div>
                </template>
              </div>
              <div class="file-right" v-else>-</div>
            </div>
            <div
              class="record-item"
              v-else-if="item.connectField ? item.connectValue.includes(ruleForm[item.connectField]) : true"
            >
              <div class="record-left"
                >{{ item.value }}<span v-if="item.unit">({{ item.unit }})</span></div
              >
              <div class="record-right">
                <span
                  :style="{
                    color: item.activeColor
                      ? ruleForm[item.key] == item.activeValue
                        ? item.activeColor
                        : '#5c6663'
                      : '#5c6663',
                  }"
                >
                  <template v-if="item.options">
                    <template v-if="item.isMultiple">
                      <span
                        v-for="(child, childIndex) in Array.isArray(ruleForm[item.key])
                          ? ruleForm[item.key]
                          : ruleForm[item.key]?.split(',')"
                        :key="childIndex"
                      >
                        <span>{{ item.options[child] || "-" }}</span>
                        <span
                          v-if="
                            childIndex <
                            (Array.isArray(ruleForm[item.key])
                              ? ruleForm[item.key].length - 1
                              : ruleForm[item.key]?.split(',').length - 1)
                          "
                          >、</span
                        >
                      </span>
                    </template>
                    <template v-else>{{ item.options[ruleForm[item.key]] || "-" }}</template>
                  </template>
                  <template v-else>{{ ruleForm[item.key] }}</template>
                </span>
                <span v-if="item.unit">({{ item.unit }})</span>
              </div>
            </div>
          </template>
        </div>
      </div>
      <slot name="other" :form="ruleForm"></slot>
    </ul>
    <slot :form="ruleForm"></slot>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex"></van-image-preview>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  export default {
    props: {
      id: {
        type: String,
        default: "",
      },
      recordApi: {
        type: String,
        default: "",
      },
      // 详情列表数据
      recordList: {
        type: Array,
        default: () => [],
      },
      // 详情字段数组
      recordFieldArr: {
        type: Array,
        default: () => [],
      },
      requestMode: {
        type: String,
        default: "get",
      },
      // post请求方式传参
      params: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        ruleForm: {},
        previewImages: [],
        previewIndex: 0,
        showPreview: false,
        smKey: [
          "phone",
          "idCard",
          "contactPhone",
          "productionUnitOperatorPhone",
          "phoneNumber",
          "defaultDriverDossierPhone",
          "supercargoDossierOnePhone",
          "supercargoDossierTwoPhone",
        ],
      };
    },
    async mounted() {
      this.getRecord();
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res =
          this.requestMode == "get"
            ? await getInfoApiFun(this.id, this.recordApi)
            : await createApiFun(this.params, this.recordApi);
        if (res.success) {
          this.ruleForm = res.data;
          for (let key in this.ruleForm) {
            if (this.smKey.includes(key)) {
              this.ruleForm[key] = this.ruleForm[key] ? this.$sm2Decrypt(this.ruleForm[key]) : "";
            }
            this.ruleForm[key] =
              this.ruleForm[key] || this.ruleForm[key] === 0 || this.ruleForm[key] === false ? this.ruleForm[key] : "-";
          }
        }
      },
      // 图片预览
      imagePreview(fileList, index) {
        this.previewImages = fileList.map((list) => list.url);
        this.previewIndex = index;
        this.showPreview = true;
      },
      isJSON(str) {
        if (typeof str == "string") {
          try {
            let obj = JSON.parse(str);
            if (typeof obj == "object" && obj) {
              return true;
            }
            return false;
          } catch (e) {
            return false;
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background: #fff;
    overflow: auto;
  }
  .record-title {
    padding: 12px 24px;
  }
  .record-box {
    background-color: #fff;
    border-radius: 8px;
  }
  .record-content {
    padding: 0 24px;
  }
  .record-item {
    padding: 12px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 16px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 16px;
    text-align: right;
    word-break: break-all;
  }
  .image-box {
    padding: 10px 0;
  }
  .image-list {
    margin-top: 8px;
    display: grid;
    grid-gap: 8px;
    grid-template-columns: repeat(5, 1fr);
  }
  .image-item {
    width: 60px;
    height: 60px;
    overflow: hidden;
  }
  .file-box {
    padding: 10px 0;
  }
  .file-right {
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    word-break: break-all;
    margin-top: 4px;
  }
  .link-block {
    display: block;
    padding-bottom: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
</style>
