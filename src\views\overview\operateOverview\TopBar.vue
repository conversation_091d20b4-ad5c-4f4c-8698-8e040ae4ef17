<template>
  <div class="topBar-wrapper">
    <div
      v-for="(item, index) in list"
      :key="index"
      @click="updateActive(index + 1)"
      :class="active == index + 1 ? 'topBar-item-active' : ''"
      class="topBar-item"
      >{{ item }}</div
    >
  </div>
</template>
<script>
  export default {
    components: {},
    props: ["list"],
    data() {
      return {
        active: 1,
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      updateActive(active) {
        this.active = active;
        this.$emit("updateActive", active);
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .topBar-wrapper {
    height: 56px;
    display: flex;
    .topBar-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      line-height: 20px;
      color: #939c99;
      font-weight: 400;
    }
    .topBar-item-active {
      font-size: 16px;
      color: #4ca786;
      font-weight: bold;
    }
  }
</style>
