<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="车辆、人员信息"></baseTitle>
            <van-field
              :value="ruleForm.defaultVehiclePlateNumber"
              name="defaultVehiclePlateNumber"
              label="收运车辆"
              placeholder="请选择收运车辆"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultVehiclePlateNumber"
              required
              @click="showPlateNumberPicker = true"
            />
            <van-field
              :value="ruleForm.defaultDriverDossierName"
              name="defaultDriverDossierId"
              label="司机"
              placeholder="请选择司机"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultDriverDossierId"
              required
              @click="showDriverPicker = true"
            />
            <van-field
              :value="ruleForm.defaultDriverDossierPhone"
              name="defaultDriverDossierPhone"
              label="司机联系方式"
              placeholder="请输入司机联系方式"
              input-align="right"
              error-message-align="right"
              required
              maxlength="11"
              readonly
              :border="false"
              :rules="rules.defaultDriverDossierPhone"
            />
            <van-field
              :value="ruleForm.supercargoDossierOneName"
              label="押运工1"
              placeholder="请选择押运工1"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierOnePicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierOnePhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoName"
              label="押运工2"
              placeholder="请选择押运工2"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierTwoPicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoPhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="收运任务"></baseTitle>
            <ul class="list" v-if="tableData.length > 0">
              <li class="list-item" v-for="(item, index) in tableData" :key="item.id">
                <div class="list-index">{{ index + 1 }}</div>
                <div class="list-content">
                  <div class="item-title">点位编号：{{ item.code }}</div>
                  <div class="item-text">产废单位名称：{{ item.productionUnit }}</div>
                  <div class="item-text">产废单位经办人：{{ item.productionUnitOperator }}</div>
                  <div class="item-text">收运状态：{{ RECEIVING_CONDITION[item.waybillStatus] }}</div>
                </div>
              </li>
            </ul>
            <van-empty description="暂无收运点位数据" v-else />
          </main>
        </div>
        <footer class="create-footer">
          <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="cancel"
            >取消</van-button
          >
          <van-button
            native-type="button"
            class="round-22 ml-11"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >保存</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showPlateNumberPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultPlateNumberIndex"
        @confirm="onCarConfirm"
        @cancel="showPlateNumberPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDriverPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="driverOptions"
        :default-index="defaultDriverIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'defaultDriverDossier', 'showDriverPicker')"
        @cancel="showDriverPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDossierOnePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierOneIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierOne', 'showDossierOnePicker')"
        @cancel="onDriverShipWorkerCancel($event, 'supercargoDossierOne', 'showDossierOnePicker')"
      />
    </van-popup>
    <van-popup v-model="showDossierTwoPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierTwoIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierTwo', 'showDossierTwoPicker')"
        @cancel="onDriverShipWorkerCancel($event, 'supercargoDossierTwo', 'showDossierTwoPicker')"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { RECEIVING_CONDITION } from "@/enums";
  export default {
    components: {
      baseTitle,
    },
    data() {
      return {
        RECEIVING_CONDITION,
        submitFormThrottling: () => {},
        apis: {
          update: "/api/waybill/temporaryUpdate",
          info: "/api/waybill/get/",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          detailList: "/api/waybill/waybillDetail/list",
        },
        ruleForm: {
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //驾驶司机id
          defaultDriverDossierName: "", //驾驶司机名称
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1id
          supercargoDossierOneName: "", // 押运工1名称
          supercargoDossierOnePhone: "", //押运工1联系方式
          supercargoDossierTwoId: "", //押运工2id
          supercargoDossierTwoName: "", //押运工2名称
          supercargoDossierTwoPhone: "", //押运工2联系方式
        },
        rules: {
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式" }],
        },
        id: "",
        carOptions: [],
        showPlateNumberPicker: false,
        defaultPlateNumberIndex: 0,
        driverOptions: [],
        showDriverPicker: false,
        defaultDriverIndex: 0,
        shipWorkerOptions: [],
        showDossierOnePicker: false,
        defaultDossierOneIndex: 0,
        showDossierTwoPicker: false,
        defaultDossierTwoIndex: 0,
        keyword: "",
        tableData: [],
      };
    },
    created() {
      this.id = this.$route.query.id || "";
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    async mounted() {
      await this.getOptions();
      if (this.id) {
        this.getRecord();
      }
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.defaultDriverDossierPhone = this.ruleForm.defaultDriverDossierPhone
            ? this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone)
            : "";
          this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
            : "";
          this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
            : "";

          let defaultPlateNumberIndex = this.carOptions.findIndex(
            (item) => item.name == res.data.defaultVehiclePlateNumber,
          );
          if (defaultPlateNumberIndex >= 0) {
            this.defaultPlateNumberIndex = defaultPlateNumberIndex;
          }
          let defaultDriverIndex = this.driverOptions.findIndex(
            (item) => item.lgUnionId == res.data.defaultDriverDossierId,
          );
          if (defaultDriverIndex >= 0) {
            this.defaultDriverIndex = defaultDriverIndex;
          }
          if (res.data.supercargoDossierOneId) {
            let defaultDossierOneIndex = this.shipWorkerOptions.findIndex(
              (item) => item.lgUnionId == res.data.supercargoDossierOneId,
            );
            if (defaultDossierOneIndex >= 0) {
              this.defaultDossierOneIndex = defaultDossierOneIndex;
            }
          }
          if (res.data.supercargoDossierTwoId) {
            let defaultDossierTwoIndex = this.shipWorkerOptions.findIndex(
              (item) => item.lgUnionId == res.data.supercargoDossierTwoId,
            );
            if (defaultDossierTwoIndex >= 0) {
              this.defaultDossierTwoIndex = defaultDossierTwoIndex;
            }
          }
        }
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
          createApiFun({ waybillStatus: 0, waybillId: this.id }, this.apis.detailList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.tableData = res[3].data;
      },
      // 选择收运车辆
      async onCarConfirm(item) {
        if (this.ruleForm.defaultVehiclePlateNumber == item.name) {
          this.showPlateNumberPicker = false;
          return;
        }
        let promiseList = [
          createApiFun({ userIdentity: "3", plateNumber: item.name }, this.apis.driverAndWorkerInfo),
          createApiFun({ userIdentity: "4", plateNumber: item.name }, this.apis.driverAndWorkerInfo),
        ];
        let res = await Promise.all(promiseList);
        let driverInfo = res[0].data;
        let workerInfo = res[1].data;
        if (driverInfo) {
          this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
          this.ruleForm.defaultDriverDossierName = driverInfo.fullName;
          this.ruleForm.defaultDriverDossierPhone = driverInfo.phone ? this.$sm2Decrypt(driverInfo.phone) : "";
          let defaultDriverIndex = this.driverOptions.findIndex((item) => item.lgUnionId == driverInfo.lgUnionId);
          if (defaultDriverIndex >= 0) {
            this.defaultDriverIndex = defaultDriverIndex;
          }
        } else {
          this.ruleForm.defaultDriverDossierId = "";
          this.ruleForm.defaultDriverDossierName = "";
          this.ruleForm.defaultDriverDossierPhone = "";
        }
        if (workerInfo) {
          this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
          this.ruleForm.supercargoDossierOneName = workerInfo.fullName;
          this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          let defaultDossierOneIndex = this.shipWorkerOptions.findIndex(
            (item) => item.lgUnionId == workerInfo.lgUnionId,
          );
          if (defaultDossierOneIndex >= 0) {
            this.defaultDossierOneIndex = defaultDossierOneIndex;
          }
        } else {
          this.ruleForm.supercargoDossierOneId = "";
          this.ruleForm.supercargoDossierOneName = "";
          this.ruleForm.supercargoDossierOnePhone = "";
        }
        this.ruleForm.defaultVehiclePlateNumber = item.name;
        this.showPlateNumberPicker = false;
      },
      // 选择驾驶司机、押运工
      onDriverShipWorkerConfirm(item, field, pickerPopup) {
        this.ruleForm[`${field}Id`] = item.lgUnionId;
        this.ruleForm[`${field}Name`] = item.fullName;
        this.ruleForm[`${field}Phone`] = item.phone;
        this[pickerPopup] = false;
      },
      // 取消选择
      onDriverShipWorkerCancel(_, field, pickerPopup) {
        this.ruleForm[`${field}Id`] = "";
        this.ruleForm[`${field}Name`] = "";
        this.ruleForm[`${field}Phone`] = "";
        this[pickerPopup] = false;
      },
      // 取消
      cancel() {
        this.$commonBack();
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.update);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: `临时调整成功`,
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .list {
    padding-bottom: 12px;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    .list-index {
      width: 18px;
      height: 18px;
      background-color: #4ca786;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      margin-right: 10px;
    }
    .list-content {
      flex: 1;
      overflow: hidden;
    }
    .delete-icon {
      position: absolute;
      top: 6px;
      right: 6px;
    }
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
