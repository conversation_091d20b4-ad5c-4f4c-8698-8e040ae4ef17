<template>
  <div class="card-box">
    <div class="card-title" :style="{ background: bgColor }">
      <span>{{ title }}</span>
      <div class="right" v-if="isRight" @click="handleRightClick">
        <span>{{ rightText }}</span>
        <van-icon name="arrow" size="16px" />
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: "标题",
      },
      rightText: {
        type: String,
        default: "更多",
      },
      isRight: {
        type: Boolean,
        default: true,
      },
      bgColor: {
        type: String,
        default: "",
      },
    },
    methods: {
      handleRightClick() {
        this.$emit("clickRight");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .card-box {
    width: 100%;
    min-height: 150px;
    background: #ffffff;
    border-radius: 8px;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
    margin-bottom: 12px;
  }

  .card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    font-weight: bold;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
  }

  .card-title .right {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: #939c99;
    line-height: 20px;
    text-align: right;
  }
</style>
