<template>
  <div>
    <van-field
      v-model="_value"
      :label="filterItem.value"
      :placeholder="`请输入${filterItem.value}`"
      input-align="right"
      type="tel"
      maxlength="11"
    />
  </div>
</template>

<script>
  import componentMinxins from "./mixins";
  export default {
    mixins: [componentMinxins],
  };
</script>

<style lang="scss" scoped>
  @import "./common.scss";
</style>
