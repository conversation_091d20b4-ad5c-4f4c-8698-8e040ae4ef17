<template>
  <div id="app" :class="className">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" />
  </div>
</template>
<script>
  import { appName } from "../config/index.js";
  export default {
    data() {
      return {
        className: `micro-app-${appName}`,
      };
    },
  };
</script>
<style lang="scss"></style>
