<template>
  <div class="page-container">
    <header class="page-header">
      <div class="header-input">
        <van-field
          v-model="keyword"
          label=""
          placeholder="请输入路线名称/车牌号/司机名称"
          right-icon="search"
          clearable
          @change="filterList"
          @clear="filterList"
        >
          <template #right-icon>
            <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
          </template>
        </van-field>
      </div>
    </header>
    <main class="page-main">
      <ul class="list" v-if="tableList.length > 0">
        <li class="list-item" v-for="(item, index) in tableList" :key="index">
          <div class="item-content">
            <div class="item-left">
              <div class="item-title">路线名称：{{ item.name }}</div>
              <div class="item-text">点位数量：{{ item.pointNumber }}</div>
              <div class="item-text">默认车辆：{{ item.defaultVehiclePlateNumber }}</div>
              <div class="item-text">默认司机：{{ item.defaultDriverDossierName }}</div>
              <div class="item-text">联系方式：{{ item.defaultDriverDossierPhone }}</div>
            </div>
            <div class="item-right">
              <van-button
                class="round-4"
                native-type="button"
                type="info"
                color="#4CA786"
                size="small"
                @click="selectItem(item)"
                >{{ id === item.id ? "取消选择" : "选择" }}</van-button
              >
            </div>
          </div>
        </li>
      </ul>
      <van-empty description="暂无数据" v-else />
    </main>
    <div class="page-footer">
      <van-button native-type="button" class="round-22" block type="info" color="#4CA786" @click="$emit('closePopup')"
        >返回</van-button
      >
    </div>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      id: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        apis: {
          pathList: "/api/pickup/pickupPath/list",
        },
        tableList: [],
        originalList: [],
        keyword: "",
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      // 初始化数据
      async initData() {
        try {
          let res = await createApiFun({ status: 0 }, this.apis.pathList);
          if (res.success) {
            this.originalList = res.data.map((item) => {
              return {
                ...item,
                defaultDriverDossierPhone: item.defaultDriverDossierPhone
                  ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                  : "",
              };
            });
            this.tableList = res.data.map((item) => {
              return {
                ...item,
                defaultDriverDossierPhone: item.defaultDriverDossierPhone
                  ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                  : "",
              };
            });
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 过滤
      filterList() {
        if (!this.keyword) {
          this.tableList = this.originalList;
        } else {
          this.tableList = this.originalList.filter(
            (list) =>
              list.name.includes(this.keyword) ||
              list.defaultVehiclePlateNumber.includes(this.keyword) ||
              list.defaultDriverDossierName.includes(this.keyword),
          );
        }
      },
      // 选择列表项
      selectItem(item) {
        this.$emit("selectItem", {
          id: item.id === this.id ? "" : item.id,
          name: item.id === this.id ? "" : item.name,
          type: 2,
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
  }
  .header-input {
    flex: 1;
    overflow: hidden;
  }
  .filter-button {
    display: flex;
    align-items: center;
    margin-left: 16px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
      margin-right: 2px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
      margin-top: 2px;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  .round-4 {
    border-radius: 4px;
  }
  .page-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .round-22 {
    border-radius: 22px;
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
