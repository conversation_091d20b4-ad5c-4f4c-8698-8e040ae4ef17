<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入姓名/联系电话/车牌号"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "fullName",
            value: "姓名",
          },
          {
            key: "phone",
            value: "联系电话",
          },
          {
            key: "plateNumber",
            value: "默认押运车辆",
          },
        ],
        apis: {
          listPage: "/api/supercargouser/listPage",
        },
        filterList: [
          {
            type: "Input",
            key: "fullName",
            value: "姓名",
          },
          {
            type: "Input",
            key: "phone",
            value: "联系电话",
          },
          {
            type: "Input",
            key: "plateNumber",
            value: "默认押运车辆",
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("shipWorkerRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
