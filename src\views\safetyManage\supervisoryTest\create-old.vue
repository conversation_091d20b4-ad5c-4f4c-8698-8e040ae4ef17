<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <van-tabs v-model="step" animated>
        <van-tab title="">
          <div class="create-box">
            <div class="create-content">
              <main class="create-main">
                <div class="create-title">基础信息</div>
                <van-field
                  v-model="ruleForm.plateNumber"
                  name="plateNumber"
                  label="车牌号"
                  placeholder="请选择车牌号"
                  readonly
                  input-align="right"
                  error-message-align="right"
                  is-link
                  :border="false"
                  :rules="rules.plateNumber"
                  required
                  @click="showCarPicker = true"
                />
                <van-field
                  v-model="ruleForm.operatorName"
                  name="operatorName"
                  label="经办人"
                  placeholder="请输入经办人"
                  input-align="right"
                  error-message-align="right"
                  :border="false"
                  :rules="rules.operatorName"
                  required
                  maxlength="20"
                />
              </main>
            </div>
            <footer class="create-footer">
              <van-button
                native-type="button"
                class="round-32"
                block
                type="info"
                color="#4CA786"
                @click="handleStepClick"
                >下一步</van-button
              >
            </footer>
          </div>
        </van-tab>
        <van-tab title="">
          <div class="create-box">
            <header class="create-header">
              <div class="header-title active"><div class="header-sort">①</div><div>监督抽查信息</div></div>
            </header>
            <div class="create-content">
              <main class="create-main">
                <van-field
                  v-model="ruleForm.conditionTime"
                  name="conditionTime"
                  label="抽查日期"
                  placeholder="请选择抽查日期"
                  readonly
                  input-align="right"
                  error-message-align="right"
                  is-link
                  :border="false"
                  :rules="rules.conditionTime"
                  required
                  @click="openDatePicker('conditionTime')"
                />
                <van-field label="车辆状况评价明细" label-width="100%" :border="false" required></van-field>
                <div v-for="(item, index) in ruleForm.detailList" :key="index">
                  <van-field
                    :label="item.configName"
                    input-align="right"
                    error-message-align="right"
                    :border="false"
                    required
                  >
                    <template #input>
                      <el-switch
                        v-model="ruleForm.detailList[index].status"
                        active-text="正常"
                        inactive-text="异常"
                        active-color="#4CA786"
                        :active-value="0"
                        :inactive-value="1"
                      >
                      </el-switch>
                    </template>
                  </van-field>
                </div>
              </main>
            </div>
            <footer class="create-footer">
              <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="step--"
                >返回</van-button
              >
              <van-button
                native-type="button"
                class="round-22 ml-11"
                block
                type="info"
                color="#4CA786"
                @click="submitFormThrottling"
                >提交</van-button
              >
            </footer>
          </div>
        </van-tab>
      </van-tabs>
    </van-form>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="onCarConfirm"
        @cancel="showCarPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFunByParams } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  export default {
    data() {
      return {
        step: 0, //新增步骤
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/vehicle/check/create",
          configList: "/api/vehicle/evaluationConfig/list",
          carList: "/api/vehicle/dossier/list",
          defaultCar: "/api/vehicle/dossier/findByPhone",
        },
        showCarPicker: false, //车牌号下拉弹框
        carOptions: [], //车牌号列表
        defaultCarIndex: 0, //默认车牌号下拉列表索引
        configList: [], //评价配置项列表
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(),
        ruleForm: {
          plateNumber: "", //车牌号
          operatorName: "", //经办人
          conditionTime: moment(new Date()).format("YYYY-MM-DD"), //抽查日期
          detailList: [], //车辆状况明细
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号" }],
          operatorName: [{ required: true, message: "请输入经办人" }],
          conditionTime: [{ required: true, message: "请选择抽查日期" }],
        },
        validateList: [["plateNumber", "operatorName"], ["conditionTime"]],
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          createApiFun({ type: 0 }, this.apis.configList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.ruleForm.detailList = res[1].data.map((list) => {
          return {
            configId: list.id,
            configName: list.name,
            status: 0,
          };
        });
        this.getUserDefaultCar();
      },
      // 获取当前用户默认车牌号
      async getUserDefaultCar() {
        let res = await getInfoApiFunByParams(
          { phone: this.userInfo.phone ? this.$sm2Encrypt(this.userInfo.phone) : "" },
          this.apis.defaultCar,
        );
        if (res.data) {
          this.ruleForm.plateNumber = res.data.plateNumber;
          let index = this.carOptions.findIndex((option) => option.id == res.data.id);
          if (index > 0) {
            this.defaultCarIndex = index;
          }
        }
      },
      // 确认选择车牌号
      onCarConfirm(item) {
        this.ruleForm.plateNumber = item.name;
        this.showCarPicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 步骤点击事件
      handleStepClick() {
        this.$refs.formCreate
          .validate(this.validateList[this.step])
          .then(() => {
            this.step++;
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增监督抽查记录成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
