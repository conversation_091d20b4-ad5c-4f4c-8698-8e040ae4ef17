<template>
  <div class="menus-grid">
    <div v-for="(item, index) in menusData" :key="item.id">
      <card-box
        :title="item.name"
        :isRight="false"
        :bgColor="index == 0 ? 'linear-gradient( 180deg, #EFFFFA 0%, #FFFFFF 100%);' : ''"
      >
        <div class="grid-list">
          <div class="grid-item" v-for="second in item.menus" :key="second.id" @click="handleRouteJump(second)">
            <van-image class="grid-icon" :src="second.icon" fit="fill" />
            <div class="grid-text">{{ second.name }}</div>
          </div>
        </div>
      </card-box>
    </div>
  </div>
</template>

<script>
  import cardBox from "@/views/tabbar/home/<USER>/cardBox.vue";
  export default {
    components: {
      cardBox,
    },
    props: {
      menusData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {};
    },
    methods: {
      handleRouteJump(item) {
        let { url, menus } = item;
        if (url) {
          this.$commonSkip(url);
        } else if (menus && menus.length) {
          localStorage.setItem("nextMenus", JSON.stringify(item));
          this.$commonSkip("nextMenus");
        } else {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "暂未开通该功能",
            duration: 1500,
          });
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .menus-grid {
    padding: 16px;
    background: #f7faf9;
    min-height: 100vh;
    min-height: 100dvh;
  }

  .grid-icon {
    width: 32px;
    height: 32px;
  }

  .content-class {
    background: transparent !important;
    text-align: center;
  }

  .grid-list {
    display: grid;
    grid-gap: 16px 8px;
    grid-template-columns: repeat(4, 1fr);
    padding: 16px 0;
  }

  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 6px;
  }

  .grid-text {
    font-size: 12px;
    margin-top: 8px;
    text-align: center;
    line-height: 14px;
  }
</style>
