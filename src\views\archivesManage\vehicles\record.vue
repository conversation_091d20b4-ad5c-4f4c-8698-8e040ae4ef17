<template>
  <div class="record-container">
    <div class="type-box" v-for="(item, index) in formList" :key="index">
      <div class="type-title">{{ item.title }}</div>
      <div v-for="(field, fieldIndex) in item.fieldList" :key="fieldIndex">
        <template v-if="!(field.disabledKey && ruleForm[field.disabledKey] == field.disabledValue)">
          <template v-if="field.isImage && ruleForm[field.key] && ruleForm[field.key].url">
            <div class="img-box">
              <div class="record-left">{{ field.value }}</div>
              <div class="img-list">
                <img
                  class="img-item"
                  :src="ruleForm[field.key].url"
                  alt=""
                  @click="imagePreview([ruleForm[field.key]], 0)"
                />
              </div>
            </div>
          </template>
          <div class="record-item" v-else>
            <div class="record-left"
              >{{ field.value }}<span v-if="field.unit">({{ field.unit }})</span></div
            >

            <div class="record-right">
              <template v-if="field.options">{{ field.options[ruleForm[field.key]] || "-" }}</template>
              <template v-else>
                {{ ruleForm[field.key] || "-" }}
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex"></van-image-preview>
  </div>
</template>

<script>
  import { VEHICLE_ENERGY_TYPE, OPERATIONAL_NATURE } from "@/enums";
  import { getInfoApiFun } from "@/api/base";
  export default {
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicle/dossier/get/",
        },
        ruleForm: {},
        formList: [
          {
            title: "基础信息",
            fieldList: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "vehicleModelName",
                value: "车辆型号",
              },
              {
                key: "brandModel",
                value: "品牌型号",
              },
              {
                key: "vehicleEnergyType",
                value: "能源类型",
                options: VEHICLE_ENERGY_TYPE,
              },
              {
                key: "grossWeight",
                value: "总质量",
                unit: "kg",
              },
              {
                key: "refuelTypeName",
                value: "加油/充电类型",
              },
              {
                key: "fuelTankCapacity",
                value: "油箱/电池容量",
                unit: "L/Kwh",
              },
              {
                key: "emissionStandardName",
                value: "排放标准",
              },
              {
                key: "vin",
                value: "车架号",
              },
              {
                key: "engineNumber",
                value: "发动机号",
              },
              {
                key: "loadVolume",
                value: "装载体积",
                unit: "m³",
              },
              {
                key: "loadCapacity",
                value: "装载重量",
                unit: "kg",
              },
              {
                key: "purchaseDate",
                value: "购买日期",
              },
              {
                key: "departmentName",
                value: "所属部门",
              },
              {
                key: "operationalNature",
                value: "营运性质",
                options: OPERATIONAL_NATURE,
              },
            ],
          },
          {
            title: "年审、维保、投保信息",
            fieldList: [
              {
                key: "recentAnnualReviewTime",
                value: "最近年审日期",
              },
              {
                key: "nextAnnualReviewTime",
                value: "下次年审日期",
              },
              {
                key: "lastSecMaintTime",
                value: "最近二级维护日期",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
              {
                key: "nextSecMaintTime",
                value: "下次二级维护日期",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
              {
                key: "recentMaintenanceTime",
                value: "最近保养日期",
              },
              {
                key: "nextMaintenanceTime",
                value: "下次保养日期",
              },
              {
                key: "lastMotcStartTime",
                value: "最近交强险开始日期",
              },
              {
                key: "lastMotcEndTime",
                value: "交强险终保日期",
              },
              {
                key: "lastCommercialStartTime",
                value: "最近商业险开始日期",
              },
              {
                key: "lastCommercialEndTime",
                value: "商业险终保日期",
              },
              {
                key: "lastTransInsuranceTime",
                value: "最近承运险开始日期",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
              {
                key: "finalTransInsuranceTime",
                value: "承运险终保日期",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
            ],
          },
          {
            title: "证件信息",
            fieldList: [
              {
                key: "drivingCertNumber",
                value: "车辆行驶证编号",
              },
              {
                key: "drivingCertBeginDate",
                value: "车辆行驶证有效期开始日期",
              },
              {
                key: "drivingCertEndDate",
                value: "车辆行驶证有效期结束日期",
              },
              {
                key: "drivingCertRegDate",
                value: "车辆行驶证登记日期",
              },
              {
                key: "drivingCertPhotoFront",
                value: "车辆行驶证件主页照片",
                isImage: true,
                isObject: true,
              },
              {
                key: "drivingCertPhotoBack",
                value: "车辆行驶证件副页照片",
                isImage: true,
                isObject: true,
              },
              {
                key: "operationCertNumber",
                value: "营运证编号",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
              {
                key: "operationCertBeginDate",
                value: "营运证有效期开始日期",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
              {
                key: "operationCertEndDate",
                value: "营运证有效期结束日期",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
              {
                key: "operationCertRegDate",
                value: "营运证登记日期",
                disabledKey: "operationalNature",
                disabledValue: 1,
              },
              {
                key: "operationCertPhotoFront",
                value: "营运证证件主页照片",
                disabledKey: "operationalNature",
                disabledValue: 1,
                isImage: true,
                isObject: true,
              },
              {
                key: "operationCertPhotoBack",
                value: "营运证证件副页照片",
                disabledKey: "operationalNature",
                disabledValue: 1,
                isImage: true,
                isObject: true,
              },
            ],
          },
        ],
        previewImages: [],
        previewIndex: 0,
        showPreview: false,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
      // 图片预览
      imagePreview(fileList, index) {
        this.previewImages = fileList.map((list) => list.url);
        this.previewIndex = index;
        this.showPreview = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-container {
    padding: 12px;
  }
  .type-box {
    background: linear-gradient(180deg, #eefcf7 0%, #ffffff 100%);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
  }
  .type-title {
    font-weight: 500;
    font-size: 16px;
    color: #4ca786;
    line-height: 22px;
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    word-break: break-all;
  }
  .img-box {
    width: 100%;
    padding-top: 10px;
  }
  .img-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 8px;
  }
  .img-item {
    width: 60px;
    height: 60px;
    overflow: hidden;
    margin-left: 6px;
  }
  .img-item:first-child {
    margin-left: 0;
  }
  .img-item:nth-child(5n + 6) {
    margin-left: 0;
  }
</style>
