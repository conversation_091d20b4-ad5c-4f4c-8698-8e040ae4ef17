<template>
  <div class="page-container">
    <header class="page-header">
      <van-field
        v-model="productionUnit"
        label=""
        placeholder="请输入产废单位名称"
        right-icon="search"
        clearable
        @change="onRefresh"
        @clear="onRefresh"
      >
        <template #right-icon>
          <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
        </template>
      </van-field>
    </header>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="item-content" @click="itemClick(item)">
                <div class="item-left">
                  <div class="item-title">产废单位名称：{{ item.productionUnit }}</div>
                  <div class="item-text">收运时间：{{ item.waybillTime }}</div>
                  <div class="item-text">收运截止日期：{{ item.endDate }}</div>
                  <div class="item-text">收运总量：{{ item.rubbishTotal }}</div>
                  <div class="item-text">任务类型：{{ POINT_TASK_TYPE[item.detailType] }}</div>
                  <div class="item-text"
                    >加班类型：{{ item.overType || item.overType === 0 ? OVERTIME_TYPE[item.overType] : "" }}</div
                  >
                  <div class="item-text">是否清空：{{ IS_NORMAL_OPERATION[item.isClear] }}</div>
                  <div class="item-text">桶装/袋装：{{ BARRELS_BAGS[item.baggingMethod] }}</div>
                  <div v-show="item.showMore">
                    <div class="item-text">点位编号：{{ item.code }}</div>
                    <div class="item-text">产废单位经办人：{{ item.productionUnitOperator }}</div>
                    <div class="item-text">正常经营：{{ IS_NORMAL_OPERATION[item.operation] }}</div>
                    <div class="item-text">收运状态：{{ RECEIVING_CONDITION[item.waybillStatus] }}</div>
                    <div class="item-text">当前流程：{{ VERIFY_STATUS[item.verifyStatus] }}</div>
                    <div class="item-text">确认人：{{ item.verifyUserName }}</div>
                    <div class="item-text">确认时间：{{ item.verifyTime }}</div>
                    <div class="item-text">感染性废物重量（kg）：{{ item.infectiousWaste }}</div>
                    <div class="item-text">损伤性废物重量（kg）：{{ item.damagingWaste }}</div>
                    <div class="item-text">药物性废物重量（kg）：{{ item.pharmaceuticalWaste }}</div>
                    <div class="item-text">病理性废物重量（kg）：{{ item.pathologicalWaste }}</div>
                    <div class="item-text">化学性废物重量（kg）：{{ item.chemicalWaste }}</div>
                    <div class="item-text">感染性废物污泥重量（kg）：{{ item.sludge }}</div>
                    <div class="item-text">剩余垃圾（kg）：{{ item.residueRubbish }}</div>
                  </div>
                </div>
              </div>
              <div class="item-footer">
                <div class="footer-box" @click="item.showMore = !item.showMore">
                  <div class="footer-text">{{ item.showMore ? "收起" : "展开" }}更多</div>
                  <span class="sctmp-iconfont icon-ic_zhankai footer-icon" v-show="!item.showMore"></span>
                  <span class="sctmp-iconfont icon-ic_shouqi footer-icon" v-show="item.showMore"></span>
                </div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import {
    IS_NORMAL_OPERATION,
    BARRELS_BAGS,
    RECEIVING_CONDITION,
    POINT_TASK_TYPE,
    VERIFY_STATUS,
    OVERTIME_TYPE,
  } from "@/enums";
  export default {
    data() {
      return {
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        listPageApi: "/api/waybill/waybillDetail/listPage",
        tableList: [],
        IS_NORMAL_OPERATION,
        BARRELS_BAGS,
        RECEIVING_CONDITION,
        POINT_TASK_TYPE,
        VERIFY_STATUS,
        OVERTIME_TYPE,
        productionUnit: "",
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          waybillId: this.$route.query.waybillId,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          productionUnit: this.productionUnit,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas;
            dataList.forEach((list) => (list.showMore = false));
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .item-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    .footer-box {
      padding: 6px 8px;
      background: #f0f2f1;
      border-radius: 3px;
      opacity: 0.5;
      display: flex;
      align-items: center;
      .footer-text {
        font-weight: 400;
        font-size: 12px;
        color: #5c6663;
        line-height: 14px;
        margin-right: 4px;
      }
      .footer-icon {
        font-size: 14px;
        color: #5c6663;
      }
    }
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
