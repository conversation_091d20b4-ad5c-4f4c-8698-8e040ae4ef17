<template>
  <div class="operateOverview-wrapper">
    <div class="info-wrapper">
      <ListItem
        title="今日各区收运率情况"
        :hasLabel="true"
        :label="['区域名', '区域收运率']"
        :list="summaryData.districtSituation"
      ></ListItem>
      <ListItem
        title="今日各路线收运率情况"
        :hasLabel="true"
        :label="['路线名', '路线收运率']"
        :list="summaryData.routeSituation"
      ></ListItem>
      <TodayBill></TodayBill>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";
  import ListItem from "@/views/overview/operateOverview/ListItem";
  import TodayBill from "./TodayBill";
  export default {
    components: { ListItem, TodayBill },
    props: {},
    data() {
      return {
        api: {
          getData: "/api/generalization/operate/dispatch",
        },
        summaryData: {
          routeSituation: [],
          districtSituation: [],
        },
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
    },
    methods: {
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          this.summaryData.districtSituation = data.data.districtSituation.map((item) => {
            let value = (item.districtPercentage * 100).toFixed(2);
            value = parseFloat(value);
            return {
              districtName: item.districtName,
              pointNum: value + "%",
            };
          });
          this.summaryData.routeSituation = data.data.routeSituation.map((item) => {
            let value = (item.routePercentage * 100).toFixed(2);
            value = parseFloat(value);
            return {
              districtName: item.name,
              pointNum: value + "%",
            };
          });
        } catch (error) {
          console.log(error);
        }
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .operateOverview-wrapper {
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
  }
  .info-wrapper {
    padding: 12px 12px 1px 12px;
    background-color: #f7faf9;
    flex: 1;
    overflow: auto;
  }
</style>
