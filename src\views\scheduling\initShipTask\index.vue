<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" label-width="180px" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="时效信息"></baseTitle>
            <van-field
              v-model="ruleForm.expectTime"
              name="expectTime"
              label="期望收运时间"
              placeholder="请选择期望收运时间"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.expectTime"
              required
              @click="openDatePicker('expectTime')"
            />
            <van-field
              :value="userInfo.fullName"
              name="driverDossierId"
              label="发起人"
              placeholder="请输入发起人"
              input-align="right"
              error-message-align="right"
              readonly
              :border="false"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="车辆信息"></baseTitle>
            <van-field
              :value="ruleForm.defaultVehiclePlateNumber"
              name="defaultVehiclePlateNumber"
              label="收运车辆"
              placeholder="请选择收运车辆"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultVehiclePlateNumber"
              required
              @click="showPlateNumberPicker = true"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="人员信息"></baseTitle>
            <van-field
              :value="ruleForm.defaultDriverDossierName"
              name="defaultDriverDossierId"
              label="司机"
              placeholder="请选择司机"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultDriverDossierId"
              required
              @click="showDriverPicker = true"
            />
            <van-field
              :value="ruleForm.defaultDriverDossierPhone"
              name="defaultDriverDossierPhone"
              label="司机联系方式"
              placeholder="请输入司机联系方式"
              input-align="right"
              error-message-align="right"
              required
              maxlength="11"
              readonly
              :border="false"
            />
            <van-field
              :value="ruleForm.supercargoDossierOneName"
              label="押运工1"
              placeholder="请选择押运工1"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierOnePicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierOnePhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoName"
              label="押运工2"
              placeholder="请选择押运工2"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierTwoPicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoPhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="收运任务"></baseTitle>
            <ul class="task-list" v-if="ruleForm.sponsorWaybillsDetails.length > 0">
              <li class="task-item" v-for="item in ruleForm.sponsorWaybillsDetails" :key="item.id">
                <van-field
                  :value="item.code"
                  label="点位编号"
                  placeholder="请输入点位编号"
                  input-align="right"
                  error-message-align="right"
                  readonly
                  :border="false"
                />
                <van-field
                  :value="item.name"
                  label="产废单位名称"
                  placeholder="请输入产废单位名称"
                  input-align="right"
                  error-message-align="right"
                  readonly
                  :border="false"
                />
                <van-field
                  :value="item.contact"
                  label="产废单位经办人"
                  placeholder="请输入产废单位经办人"
                  input-align="right"
                  error-message-align="right"
                  readonly
                  :border="false"
                />
                <van-field
                  :value="POINT_TASK_TYPE[item.detailType]"
                  label="任务类型"
                  placeholder="请选择任务类型"
                  readonly
                  input-align="right"
                  error-message-align="right"
                  is-link
                  :border="false"
                  required
                  @click="openDetailTypePicker(item)"
                />
                <div>
                  <van-button type="warning" icon="delete-o" size="small" block @click="deleteItem(item)"
                    >删除</van-button
                  >
                </div>
              </li>
            </ul>
            <van-empty v-else description="暂无数据" />
            <div class="add-task-button">
              <van-button type="info" size="small" block icon="plus" native-type="button" @click="showPopup = true"
                >增加收运点位</van-button
              >
            </div>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="备注信息"></baseTitle>
            <van-field
              v-model="ruleForm.memo"
              name="memo"
              label=""
              placeholder="请输入备注"
              type="textarea"
              rows="6"
              maxlength="500"
              border
              show-word-limit
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="datetime"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showPlateNumberPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultPlateNumberIndex"
        @confirm="onCarConfirm"
        @cancel="showPlateNumberPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDriverPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="driverOptions"
        :default-index="defaultDriverIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'defaultDriverDossier', 'showDriverPicker')"
        @cancel="showDriverPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDossierOnePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierOneIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierOne', 'showDossierOnePicker')"
        @cancel="onDriverShipWorkerCancel($event, 'supercargoDossierOne', 'showDossierOnePicker')"
      />
    </van-popup>
    <van-popup v-model="showDossierTwoPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierTwoIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierTwo', 'showDossierTwoPicker')"
        @cancel="onDriverShipWorkerCancel($event, 'supercargoDossierTwo', 'showDossierTwoPicker')"
      />
    </van-popup>
    <van-popup v-model="showDetailTypePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="POINT_TASK_TYPE"
        @confirm="onTypeConfirm"
        @cancel="showDetailTypePicker = false"
      />
    </van-popup>
    <van-popup v-model="showPopup" position="right">
      <template v-if="showPopup">
        <div class="popup-container">
          <point
            :originalTable="ruleForm.sponsorWaybillsDetails"
            @closePopup="showPopup = false"
            @selectItem="selectItem"
          ></point>
        </div>
      </template>
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import baseTitle from "@/components/baseTitle";
  import { POINT_TASK_TYPE } from "@/enums";
  import point from "@/views/archivesManage/receiveRoute/components/point.vue";
  import { deepCopy } from "@/utils";
  export default {
    components: {
      baseTitle,
      point,
    },
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/waybill/sponsorWaybills",
          userList: "/api/baseuser/list",
          carList: "/api/vehicle/dossier/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
        },
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        ruleForm: {
          expectTime: null, //期望收运时间
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //驾驶司机id
          defaultDriverDossierName: "", //驾驶司机名称
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1id
          supercargoDossierOneName: "", // 押运工1名称
          supercargoDossierOnePhone: "", //押运工1联系方式
          supercargoDossierTwoId: "", //押运工2id
          supercargoDossierTwoName: "", //押运工2名称
          supercargoDossierTwoPhone: "", //押运工2联系方式
          memo: "", //备注信息
          sponsorWaybillsDetails: [], //收运任务
        },
        rules: {
          expectTime: [{ required: true, message: "请选择期望收运时间" }],
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机" }],
        },
        carOptions: [],
        showPlateNumberPicker: false,
        defaultPlateNumberIndex: 0,
        driverOptions: [],
        showDriverPicker: false,
        defaultDriverIndex: 0,
        shipWorkerOptions: [],
        showDossierOnePicker: false,
        defaultDossierOneIndex: 0,
        showDossierTwoPicker: false,
        defaultDossierTwoIndex: 0,
        showDetailTypePicker: false,
        POINT_TASK_TYPE,
        showPopup: false,
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 选择收运车辆
      async onCarConfirm(item) {
        if (this.ruleForm.defaultVehiclePlateNumber == item.name) {
          this.showPlateNumberPicker = false;
          return;
        }
        let promiseList = [
          createApiFun({ userIdentity: "3", plateNumber: item.name }, this.apis.driverAndWorkerInfo),
          createApiFun({ userIdentity: "4", plateNumber: item.name }, this.apis.driverAndWorkerInfo),
        ];
        let res = await Promise.all(promiseList);
        let driverInfo = res[0].data;
        let workerInfo = res[1].data;
        if (driverInfo) {
          this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
          this.ruleForm.defaultDriverDossierName = driverInfo.fullName;
          this.ruleForm.defaultDriverDossierPhone = driverInfo.phone ? this.$sm2Decrypt(driverInfo.phone) : "";
          let defaultDriverIndex = this.driverOptions.findIndex((item) => item.lgUnionId == driverInfo.lgUnionId);
          if (defaultDriverIndex >= 0) {
            this.defaultDriverIndex = defaultDriverIndex;
          }
        } else {
          this.ruleForm.defaultDriverDossierId = "";
          this.ruleForm.defaultDriverDossierName = "";
          this.ruleForm.defaultDriverDossierPhone = "";
        }
        if (workerInfo) {
          this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
          this.ruleForm.supercargoDossierOneName = workerInfo.fullName;
          this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          let defaultDossierOneIndex = this.shipWorkerOptions.findIndex(
            (item) => item.lgUnionId == workerInfo.lgUnionId,
          );
          if (defaultDossierOneIndex >= 0) {
            this.defaultDossierOneIndex = defaultDossierOneIndex;
          }
        } else {
          this.ruleForm.supercargoDossierOneId = "";
          this.ruleForm.supercargoDossierOneName = "";
          this.ruleForm.supercargoDossierOnePhone = "";
        }
        this.ruleForm.defaultVehiclePlateNumber = item.name;
        this.showPlateNumberPicker = false;
      },
      // 选择驾驶司机、押运工
      onDriverShipWorkerConfirm(item, field, pickerPopup) {
        this.ruleForm[`${field}Id`] = item.lgUnionId;
        this.ruleForm[`${field}Name`] = item.fullName;
        this.ruleForm[`${field}Phone`] = item.phone;
        this[pickerPopup] = false;
      },
      // 取消选择
      onDriverShipWorkerCancel(_, field, pickerPopup) {
        this.ruleForm[`${field}Id`] = "";
        this.ruleForm[`${field}Name`] = "";
        this.ruleForm[`${field}Phone`] = "";
        this[pickerPopup] = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD HH:mm");
        this.showDatePicker = false;
      },
      // 打开选择任务类型弹窗
      openDetailTypePicker(item) {
        this.currentDetailTypeItem = item;
        this.showDetailTypePicker = true;
      },
      // 确认选择任务类型
      onTypeConfirm(_, i) {
        let index = this.ruleForm.sponsorWaybillsDetails.findIndex((item) => item.id == this.currentDetailTypeItem.id);
        if (index >= 0) {
          this.ruleForm.sponsorWaybillsDetails[index].detailType = i;
        }
        this.showDetailTypePicker = false;
      },
      // 添加/删除点位
      selectItem(obj) {
        if (obj.flag) {
          let index = this.ruleForm.sponsorWaybillsDetails.findIndex((item) => item.id == obj.item.id);
          if (index >= 0) {
            this.ruleForm.sponsorWaybillsDetails.splice(index, 1);
          }
        } else {
          this.ruleForm.sponsorWaybillsDetails.push({ ...obj.item, detailType: "" });
        }
      },
      // 删除点位
      deleteItem(item) {
        let index = this.ruleForm.sponsorWaybillsDetails.findIndex((list) => list.id == item.id);
        if (index >= 0) {
          this.ruleForm.sponsorWaybillsDetails.splice(index, 1);
        }
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            if (this.ruleForm.sponsorWaybillsDetails.length === 0) {
              this.$toast({
                type: "fail",
                message: "请至少选择一个收运点位",
                duration: 1500,
              });
              return;
            }
            let emptyDetailType = this.ruleForm.sponsorWaybillsDetails.filter((item) => item.detailType === "");
            if (emptyDetailType.length > 0) {
              this.$toast({
                type: "fail",
                message: "请选择任务类型",
                duration: 1500,
              });
              return;
            }
            let params = deepCopy(this.ruleForm);
            params.sponsorWaybillsDetails = params.sponsorWaybillsDetails.map((list) => {
              return {
                pickupPointId: list.id,
                detailType: list.detailType,
              };
            });
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "发起收运任务成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            console.log("err ==> ", err);

            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .copy-button {
    padding: 12px 24px;
  }
  .add-task-button {
    padding: 12px 0;
  }
  .popup-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
  }
  .task-item {
    padding: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
