<template>
  <div>
    <van-field
      :value="valueName"
      :label="filterItem.value"
      :placeholder="`请选择${filterItem.value}`"
      input-align="right"
      is-link
      arrow-direction="down"
      label-width="120"
      readonly
      :border="false"
      @click="handleClick"
    />
    <van-popup v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        :value-key="filterItem.valueKey || 'text'"
        :columns="filterItem.enums"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import componentMinxins from "./mixins";
  export default {
    mixins: [componentMinxins],
    data() {
      return {
        showPicker: false,
      };
    },
    computed: {
      valueName() {
        let name = "";
        if (this.filterItem.keyword && this._value) {
          let filterList = this.filterItem.enums.filter((item) => item[this.filterItem.keyword] == this._value);
          name = filterList[0][this.filterItem.valueKey];
        } else {
          name = this.filterItem.enums[this._value];
        }
        return name;
      },
    },
    methods: {
      // 点击
      handleClick() {
        this.showPicker = true;
      },
      // 确认
      onConfirm(obj, index) {
        if (this.filterItem.keyword) {
          this._value = obj[this.filterItem.keyword];
        } else {
          this._value = index;
        }
        this.showPicker = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import "./common.scss";
</style>
