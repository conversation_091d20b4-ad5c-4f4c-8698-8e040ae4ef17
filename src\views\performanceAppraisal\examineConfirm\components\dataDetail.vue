<template>
  <div>
    <van-field :value="`${tableData.length}（天）`" label="出勤天数：" input-align="right" :border="false" readonly />
    <ul class="detail-list" v-if="tableData.length > 0">
      <li class="detail-item" v-for="(item, index) in tableData" :key="index">
        <div class="item-header">
          <div class="item-title">{{ item.date }}</div>
          <div class="item-total" v-if="showTotal">
            <div v-if="item.dayPerformance > 0">当日绩效：{{ item.dayPerformance }}（元）</div>
            <div class="item-total-point" v-if="item.clinicDetail || item.nsClinicDetail"
              >收运诊所点位：{{ item.clinicTotal }}</div
            >
          </div>
        </div>
        <div class="detail-overall" v-if="item?.drumOrBagDetail">
          <div class="detail-overall-title">大型床位点位收运情况</div>
          <div class="detail-overall-item">
            <div class="detai-overall-item-left">收运点位总数：{{ item.drumOrBagDetail.pointTotal }}</div>
            <div class="detai-overall-item-right">总重量：{{ item.drumOrBagDetail.rubbishTotal }}（kg）</div>
          </div>
          <div v-for="(child, childIndex) in item.drumOrBagDetail.list" :key="childIndex">
            <div class="detail-overall-item">
              <div class="detai-overall-item-left"
                >{{ child?.type?.includes("t") ? "桶" : "袋" }}装点位：{{ child.count }}</div
              >
              <div class="detai-overall-item-right"
                >{{ child?.type?.includes("t") ? "桶" : "袋" }}装点位重量：{{ child.rubbish }}（吨）</div
              >
            </div>
            <div class="detail-overall-item">
              <div class="detai-overall-item-left">单价：{{ child.price }}/吨</div>
            </div>
          </div>
        </div>
        <div class="detail-overall" v-if="item?.collectionPointDetail">
          <div class="detail-overall-title">小型床位点位收运情况</div>
          <div class="detail-overall-item">
            <div class="detai-overall-item-left">收运点位总数：{{ item.collectionPointDetail.pointTotal }}</div>
          </div>
          <div v-for="(child, childIndex) in item.collectionPointDetail.list" :key="childIndex">
            <div class="detail-overall-item">
              <div class="detai-overall-item-left"
                >{{ child?.type?.includes("t") ? "桶" : "袋" }}装点位：{{ child.count }}</div
              >
              <div class="detai-overall-item-right">单价：{{ child.price }}/点</div>
            </div>
          </div>
        </div>
        <div class="detail-overall" v-if="item?.clinicDetail">
          <div class="detail-overall-title">常规小诊所点位收运情况</div>
          <div class="detail-overall-item">
            <div class="detai-overall-item-left">收运点位总数：{{ item.clinicDetail.collect }}</div>
            <div class="detai-overall-item-right"
              >当月累计：{{ item.clinicDetail.accumulate }}/{{ item.clinicDetail.baseNumber }}点</div
            >
          </div>
        </div>
        <div class="detail-overall" v-if="item?.nsClinicDetail">
          <div class="detail-overall-title">南沙小诊所点位收运情况</div>
          <div class="detail-overall-item">
            <div class="detai-overall-item-left">收运点位总数：{{ item.nsClinicDetail.collect }}</div>
            <div class="detai-overall-item-right"
              >当月累计：{{ item.nsClinicDetail.accumulate }}/{{ item.nsClinicDetail.baseNumber }}点</div
            >
          </div>
        </div>
        <div class="detail-overall">
          <div class="detail-overall-title">加班情况</div>
          <div class="detail-overall-item" v-if="item?.overtimePay">
            <div class="detai-overall-item-left"
              >{{ item?.overtimePay?.type }}加班：{{ item?.overtimePay?.amount }}（元）</div
            >
          </div>
          <template v-if="item?.overtime?.length > 0">
            <div class="detail-overall-item" v-for="(child, childIndex) in item?.overtime" :key="childIndex">
              <div class="detai-overall-item-left"
                >{{ child?.overtimeType ? "申请" : "下发" }}加班任务<span v-if="item?.overtime?.length > 1">{{
                  childIndex + 1
                }}</span
                >：{{ child?.overtimePrice }}（元）</div
              >
            </div>
          </template>
        </div>
        <div
          class="detail-overall"
          v-if="
            item?.districtSubsidy ||
            item.waybillChanges?.length > 0 ||
            item.driverWaybills?.length > 0 ||
            item.supercargoWaybills?.length > 0
          "
        >
          <div class="detail-overall-title">备注</div>
          <div class="detail-overall-item" v-if="item?.districtSubsidy">
            <div class="detai-overall-item-left"
              >{{ item?.districtSubsidy?.districtName }}区域补贴：{{ item?.districtSubsidy?.amount }}（元）</div
            >
          </div>
          <div class="detail-overall-item" v-if="item.waybillChanges?.length > 0">
            <div class="detai-overall-item-left">顶班</div>
          </div>
          <div class="detail-overall-item" v-if="item.driverWaybills?.length > 0">
            <div class="detai-overall-item-left"
              >司机单人收运点位数量&nbsp;&nbsp;{{ item?.driverWaybills?.length }}</div
            >
          </div>
          <div class="detail-overall-item" v-if="item.supercargoWaybills?.length > 0">
            <div class="detai-overall-item-left"
              >单押运收运点位数量&nbsp;&nbsp;{{ item?.supercargoWaybills.length }}</div
            >
          </div>
        </div>
      </li>
    </ul>
    <van-empty v-else description="暂无数据" />
  </div>
</template>

<script>
  export default {
    props: {
      tableData: {
        type: Array,
        default: () => [],
      },
      dataDetailFields: {
        type: Array,
        default: () => [],
      },
      showTotal: {
        type: Boolean,
        default: false,
      },
    },
  };
</script>

<style lang="scss" scoped>
  .detail-item {
    padding: 10px;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
    .item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item-title {
        font-size: 16px;
      }
      .item-total {
        font-size: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .item-total-point {
          margin-top: 2px;
        }
      }
    }
    .detail-overall {
      margin-top: 10px;
      padding: 10px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      .detail-overall-title {
        font-size: 14px;
      }
      .detail-overall-item {
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10px;
        .detai-overall-item-left,
        .detai-overall-item-right {
          word-break: break-all;
        }
        .detai-overall-item-right {
          margin-left: 2px;
        }
      }
    }
  }
</style>
