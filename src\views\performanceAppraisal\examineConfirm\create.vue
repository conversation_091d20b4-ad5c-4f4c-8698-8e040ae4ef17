<template>
  <div class="create-container">
    <div class="create-box">
      <div class="create-content">
        <main class="create-main">
          <baseTitle title="考核方案信息">
            <template #right>
              <van-icon
                :name="moreList[0] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(0)"
              />
            </template>
          </baseTitle>
          <div v-show="moreList[0]">
            <van-field
              :value="detailItem.fullName"
              label="被考核人员名称："
              input-align="right"
              :border="false"
              required
              label-width="130px"
              readonly
            />
            <van-field
              :value="`${detailItem.year}-${detailItem.month >= 10 ? '' : '0'}${detailItem.month}`"
              label="考核月度："
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              readonly
            />
            <van-field label="考核维度：" input-align="right" :border="false" required readonly>
              <template #input>
                <el-table :data="ruleForm.assessDimensions" :header-cell-style="{ background: '#F5F7F9' }" border>
                  <el-table-column prop="name" label="维度名称" align="center"></el-table-column>
                  <el-table-column prop="weight" label="维度满分" align="center"></el-table-column>
                </el-table>
              </template>
            </van-field>
          </div>
        </main>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="绩效总览">
            <template #right>
              <van-icon
                :name="moreList[1] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(1)"
              />
            </template>
          </baseTitle>
          <div class="pb-10" v-show="moreList[1]">
            <div class="overall" v-if="dataDetailFields?.includes('drumOrBagDetail')">
              <div class="overall-title">大型床位收运总情况</div>
              <div class="overall-item">
                <div class="overall-item-left">月收运桶装点位：{{ performanceDetailJson.bucketCount }}</div>
                <div class="overall-item-right">月桶装点位重量：{{ performanceDetailJson.bucketWeight }}（吨）</div>
              </div>
              <div class="overall-item">
                <div class="overall-item-left">月收运袋装点位：{{ performanceDetailJson.bagCount }}</div>
                <div class="overall-item-right">月袋装点位重量：{{ performanceDetailJson.bagWeight }}（吨）</div>
              </div>
            </div>
            <div class="overall" v-if="dataDetailFields?.includes('collectionPointDetail')">
              <div class="overall-title">小型床位收运总情况</div>
              <div class="overall-item">
                <div class="overall-item-left"
                  >月收运桶装点位：{{ performanceDetailJson?.collectionPointBucketPerformance }}（元）</div
                >
                <div class="overall-item-right"
                  >月收运袋装点位：{{ performanceDetailJson?.collectionPointBagPerformance }}（元）</div
                >
              </div>
            </div>
            <div class="overall" v-if="detailItem?.hasClinicGroup">
              <div class="overall-title">诊所收运总情况</div>
              <div class="overall-item" v-if="dataDetailFields?.includes('clinicDetail')">
                <div class="overall-item-left"
                  >月收运常规诊所点位：{{ performanceDetailJson?.clinicCollectNum }}/{{
                    performanceDetailJson?.clinicTotal
                  }}</div
                >
                <div class="overall-item-right"
                  >月单人收运常规诊所点位：{{ performanceDetailJson?.clinicSingletonNum }}</div
                >
              </div>
              <div class="overall-item" v-if="dataDetailFields?.includes('nsClinicDetail')">
                <div class="overall-item-left"
                  >月收运南沙诊所点位：{{ performanceDetailJson?.nsClinicCollectNum }}/{{
                    performanceDetailJson?.nsClinicTotal
                  }}</div
                >
                <div class="overall-item-right"
                  >月收运南沙诊所点位：{{ performanceDetailJson?.nsClinicSingletonNum }}</div
                >
              </div>
            </div>
            <div
              class="subsidy"
              v-if="
                performanceDetailJson?.totalDistrictSubsidyTotal ||
                performanceDetailJson?.totalDistrictSubsidyTotal === 0
              "
            >
              <div class="subsidy-left">区域补贴</div>
              <div class="subsidy-right">{{ performanceDetailJson?.totalDistrictSubsidyTotal }}（元）</div>
            </div>
            <div
              class="subsidy"
              v-if="
                performanceDetailJson?.totalOvertimePerformance || performanceDetailJson?.totalOvertimePerformance === 0
              "
            >
              <div class="subsidy-left">加班费</div>
              <div class="subsidy-right">{{ performanceDetailJson?.totalOvertimePerformance }}（元）</div>
            </div>
            <div class="subsidy mb-0" v-if="performanceDetailJson?.teamLeaderSubsidy">
              <div class="subsidy-left">班组长补贴</div>
              <div class="subsidy-right">{{ performanceDetailJson?.teamLeaderSubsidy }}（元）</div>
            </div>
          </div>
        </main>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="绩效明细">
            <template #right>
              <van-icon
                :name="moreList[2] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(2)"
              />
            </template>
          </baseTitle>
          <div class="pb-10" v-show="moreList[2]">
            <dataDetail
              :tableData="performanceDetailJson?.dataDetail"
              :dataDetailFields="dataDetailFields"
            ></dataDetail>
          </div>
        </main>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="确认信息">
            <template #right>
              <van-icon
                :name="moreList[3] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(3)"
              />
            </template>
          </baseTitle>
          <div v-show="moreList[3]">
            <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate" label-width="120px">
              <van-field label="被考核人签字确认：" :border="false" required label-width="100%" />
              <van-field
                v-model="signFile"
                name="signFile"
                label=""
                :border="false"
                :rules="rules.signFile"
                label-width="0"
              >
                <template #input>
                  <img :src="signFile" alt="" class="sign-img" @click="showPop = true" v-if="signFile" />
                  <van-button type="primary" color="#4CA786" block @click="showPop = true" v-else
                    >手写签字确认</van-button
                  >
                </template>
              </van-field>
            </van-form>
          </div>
        </main>
      </div>
      <footer class="create-footer">
        <van-button
          native-type="button"
          class="round-22"
          block
          type="info"
          color="#4CA786"
          @click="submitFormThrottling"
          >完成</van-button
        >
      </footer>
    </div>
    <signature :showPop="showPop" :baseUrl="signFile" @closePop="showPop = false" @setSign="setSign"></signature>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import Signature from "@/components/Signature";
  import { OSSUpload } from "@/utils/upload";
  import { base64ToFile } from "@/utils";
  import baseTitle from "@/components/baseTitle";
  import dataDetail from "./components/dataDetail.vue";
  export default {
    components: {
      Signature,
      baseTitle,
      dataDetail,
    },
    data() {
      return {
        assessSchemeId: "",
        apis: {
          record: "/api/assess/scheme/get/",
          create: "/api/access/record/verify",
          info: "/api/access/record/detail/",
          get: "/api/assess/form/get/",
        },
        ruleForm: {},
        rules: {
          signFile: [{ required: true, message: "请被考核人签字确认" }],
        },
        submitFormThrottling: () => {},
        signFile: "", //签名文件
        showPop: false, //签字弹窗
        moreList: [true, true, true, true],
        detailItem: {},
        performanceDetailJson: {},
        dataDetailFields: [],
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 展开/收缩
      toggleMore(index) {
        this.moreList.splice(index, 1, !this.moreList[index]);
      },
      async getRecord() {
        let res = await getInfoApiFun(this.$route.query.id, this.apis.get);
        if (res.success) {
          try {
            res.data.performanceDetailJson = JSON.parse(res.data.performanceDetailJson);
            res.data.clinicGroupParam = JSON.parse(res.data.clinicGroupParam);
          } catch (error) {
            res.data.performanceDetailJson = {};
            res.data.clinicGroupParam = {};
          }
          this.detailItem = res.data;
          this.performanceDetailJson = res.data.performanceDetailJson;
          this.performanceDetailJson.dataDetail.forEach((list) => {
            for (let key in list) {
              this.dataDetailFields.push(key);
            }
          });
          this.dataDetailFields = [...new Set(this.dataDetailFields)];
          let rsp = await getInfoApiFun(this.detailItem.assessSchemeId, this.apis.record);
          if (rsp.success) {
            this.ruleForm = rsp.data;
          }
        }
      },
      // 签名
      async setSign(url) {
        this.signFile = url;
        this.showPop = false;
      },
      async httpRequest(currFile) {
        await OSSUpload.getOssBase();
        return new Promise((resolve) => {
          let ossClient = new OSSUpload();
          ossClient.init();
          ossClient
            .upload(currFile, () => {})
            .then((data) => {
              console.log("上传成功", data);
              resolve(data.url);
            })
            .catch((err) => {
              console.log("上传失败", err);
              resolve(false);
            });
        });
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let signFile = await this.httpRequest(base64ToFile(this.signFile, "signature"));
              let res = await createApiFun({ signFile, id: this.detailItem.id }, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "考核确认成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .sign-img {
    width: 100%;
    height: 200px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .overall {
    padding: 10px;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    margin-bottom: 10px;
    .overall-title {
      font-size: 14px;
    }
    .overall-item {
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      .overall-item-left,
      .overall-item-right {
        word-break: break-all;
      }
      .overall-item-right {
        margin-left: 2px;
      }
    }
  }
  .subsidy {
    padding: 10px;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .subsidy-left {
      font-size: 14px;
    }
    .subsidy-right {
      font-size: 12px;
    }
  }
  .pb-10 {
    padding-bottom: 10px;
  }
  .mb-0 {
    margin-bottom: 0;
  }
</style>
