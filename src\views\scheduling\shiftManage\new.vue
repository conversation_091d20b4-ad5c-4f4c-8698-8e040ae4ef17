<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <van-field
              v-model="ruleForm.applyFullName"
              name="applyUserId"
              label="申请人名称"
              placeholder="请选择申请人"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.applyUserId"
              required
              @click="showUserPicker = true"
            />
            <van-field
              v-model="ruleForm.applyPhone"
              name="applyPhone"
              label="申请人联系电话"
              placeholder="请填写申请人联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.applyPhone"
              required
              label-width="120"
              maxlength="11"
              readonly
            />
            <van-field
              v-model="ruleForm.changeStartDate"
              name="changeStartDate"
              label="换班开始日期"
              placeholder="请选择换班开始日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.changeStartDate"
              required
              @click="showStartDatePicker = true"
            />
            <van-field
              v-model="ruleForm.changeEndDate"
              name="changeEndDate"
              label="换班结束日期"
              placeholder="请选择换班结束日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.changeEndDate"
              required
              @click="showEndDatePicker = true"
            />
            <van-field name="remark" label="详情备注" label-width="100%" required :border="false" />
            <van-field
              v-model="ruleForm.remark"
              name="remark"
              label=""
              placeholder="请填写详情备注"
              type="textarea"
              :border="false"
              :autosize="{ maxHeight: 200, minHeight: 100 }"
              :maxlength="800"
              show-word-limit
              :rules="rules.remark"
            />
            <van-field
              v-model="ruleForm.changeUserName"
              name="changeUserId"
              label="顶班人员"
              placeholder="请选择顶班人员"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.changeUserId"
              required
              @click="showChangePicker = true"
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-22"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showUserPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="userOptions"
        @confirm="onUserConfirm"
        @cancel="showUserPicker = false"
      />
    </van-popup>
    <van-popup v-model="showStartDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentStartDate"
        :min-date="minStartDate"
        :max-date="maxStartDate"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showEndDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentEndDate"
        :min-date="minEndDate"
        :max-date="maxEndDate"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showChangePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="changeUserOptions"
        @confirm="onChangeConfirm"
        @cancel="showChangePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import moment from "moment";
  export default {
    data() {
      return {
        submitFormThrottling: () => {},
        apis: {
          create: "/api/waybill/change/create",
          userList: "/api/baseuser/list",
        },
        ruleForm: {
          applyUserId: "", //申请人id
          applyFullName: "", //申请人名称
          applyPhone: "", //申请人联系电话
          changeStartDate: "", //换班开始日期
          changeEndDate: "", //换班结束日期
          remark: "", //详情备注
          changeUserId: "", //顶班人员id
          changeUserName: "", //顶班人员名称
        },
        rules: {
          applyUserId: [{ required: true, message: "请选择申请人" }],
          applyPhone: [{ required: true, message: "请填写申请人联系电话" }],
          changeStartDate: [{ required: true, message: "请选择换班开始日期" }],
          changeEndDate: [{ required: true, message: "请选择换班结束日期" }],
          remark: [{ required: true, message: "请填写详情备注" }],
          changeUserId: [{ required: true, message: "请选择顶班人员" }],
        },
        showStartDatePicker: false,
        currentStartDate: new Date(),
        minStartDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        showEndDatePicker: false,
        currentEndDate: new Date(),
        maxEndDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        userOptions: [], //用户列表
        driverOptions: [], //司机列表
        shipOptions: [], //押运工列表
        changeUserOptions: [], //顶班人员列表
        showUserPicker: false,
        showChangePicker: false,
      };
    },
    computed: {
      maxStartDate() {
        if (this.ruleForm.changeEndDate) {
          return new Date(moment(this.ruleForm.changeEndDate).format("YYYY/MM/DD"));
        }
        return new Date(moment().add(100, "years").format("YYYY/MM/DD"));
      },
      minEndDate() {
        if (this.ruleForm.changeStartDate) {
          return new Date(moment(this.ruleForm.changeStartDate).format("YYYY/MM/DD"));
        }
        return new Date(moment().add(-100, "years").format("YYYY/MM/DD"));
      },
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取基础数据
      async getOptions() {
        let promiseList = [
          getListApiFun({ userIdentitys: [3, 4] }, this.apis.userList),
          getListApiFun({ userIdentitys: [3] }, this.apis.userList),
          getListApiFun({ userIdentitys: [4] }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.userOptions = res[0].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.userOptions.forEach((item) => {
          item.identityArr = item.userIdentity.split(",").map(Number);
        });
      },
      // 选择用户确认事件
      onUserConfirm(item) {
        if (item.lgUnionId == this.ruleForm.applyUserId) return;
        this.ruleForm.applyUserId = item.lgUnionId;
        this.ruleForm.applyFullName = item.fullName;
        this.ruleForm.applyPhone = item.phone;
        this.ruleForm.changeUserId = "";
        this.ruleForm.changeUserName = "";
        if (item.identityArr.includes(3) && item.identityArr.includes(4)) {
          this.changeUserOptions = this.userOptions;
        } else if (item.identityArr.includes(3)) {
          this.changeUserOptions = this.driverOptions;
        } else if (item.identityArr.includes(4)) {
          this.changeUserOptions = this.shipOptions;
        }
        this.showUserPicker = false;
      },
      // 确认换班开始日期
      onStartDateConfirm(value) {
        this.ruleForm.changeStartDate = moment(value).format("YYYY-MM-DD");
        this.showStartDatePicker = false;
      },
      // 确认换班结束日期
      onEndDateConfirm(value) {
        this.ruleForm.changeEndDate = moment(value).format("YYYY-MM-DD");
        this.showEndDatePicker = false;
      },
      // 选择顶班人员确认事件
      onChangeConfirm(item) {
        this.ruleForm.changeUserId = item.lgUnionId;
        this.ruleForm.changeUserName = item.fullName;
        this.showChangePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增换班成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
