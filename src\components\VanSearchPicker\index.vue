<template>
  <van-popup v-model="showPopup" position="bottom">
    <van-search v-model.trim="searchValue" placeholder="请输入搜索关键词" clearable v-if="showSearch" />
    <van-picker
      show-toolbar
      :default-index="defaultIndex"
      :value-key="valueKey"
      :columns="columns"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </van-popup>
</template>

<script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      // 下拉选择器所有数据集
      options: {
        type: Array,
        default: () => [],
      },
      // vant组件选项对象中，选项文字对应的键名
      valueKey: {
        type: String,
        default: "text",
      },
      // 默认选中项的值
      indexValue: {
        type: [String, Number],
        default: "",
      },
      // 默认选中项比对的字段
      indexKey: {
        type: String,
        default: "",
      },
      // 是否根据index索引对比选中项的值
      isIndexValue: {
        type: <PERSON>olean,
        default: false,
      },
      // 是否展示搜索框
      showSearch: {
        type: <PERSON>olean,
        default: true,
      },
    },
    computed: {
      showPopup: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      columns() {
        let options = this.options;
        if (this.searchValue) {
          options = options.filter((item) => {
            return this.valueKey === "text"
              ? item.includes(this.searchValue)
              : item[this.valueKey].includes(this.searchValue);
          });
        }
        return options;
      },
      defaultIndex() {
        let index = 0;
        if (this.indexValue || this.indexValue === 0) {
          index = this.columns.findIndex((item, i) => {
            if (this.isIndexValue) {
              return i == this.indexValue;
            }
            return (this.indexKey ? item[this.indexKey] : item) == this.indexValue;
          });
        }
        return index >= 0 ? index : 0;
      },
    },
    data() {
      return {
        searchValue: "",
      };
    },
    methods: {
      handleConfirm(item) {
        let index = this.options.findIndex((o) => {
          if (this.indexKey) {
            return o[this.indexKey] == item[this.indexKey];
          }
          return o == item;
        });
        this.$emit("confirm", { item, index: index >= 0 ? index : 0 });
        this.handleCancel();
      },
      handleCancel() {
        this.searchValue = "";
        this.showPopup = false;
      },
    },
  };
</script>

<style lang="scss" scoped></style>
