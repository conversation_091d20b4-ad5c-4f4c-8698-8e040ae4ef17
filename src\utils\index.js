import { wxNavigateTo, wxNavigateBack, wxPostMessage } from "./wechatRouter";
// 导出的流Blob,filename 导出的文件名
export function downloadFileBlob(data, filename) {
  if (!data) {
    return;
  }
  let url = window.URL.createObjectURL(new Blob([data]));
  let link = document.createElement("a");
  link.style.display = "none";
  link.href = url;
  link.setAttribute("download", filename);

  document.body.appendChild(link);
  link.click();
}
// 用a链接导出处理方案GET方式
export function downloadALink(url, filename) {
  var a = document.createElement("a");
  a.setAttribute("href", url);
  a.setAttribute("target", "_blank");
  a.setAttribute("download", filename);
  a.setAttribute("id", "startTelMedicine");
  // 防止反复添加
  if (document.getElementById("startTelMedicine")) {
    document.body.removeChild(document.getElementById("startTelMedicine"));
  }
  document.body.appendChild(a);
  a.click();
}

//节流
export function throttling(fn, delay) {
  let last = 0;
  return function () {
    let now = Date.now();
    if (last && now < last + delay) {
      last = now;
    } else {
      fn.apply(this, arguments);
      last = now;
    }
  };
}

// 防抖
export function debounce(func, wait) {
  let timeout;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

// OSS文件命名
export function timestamp() {
  let add = function (m) {
    return m < 10 ? "0" + m : m;
  };
  let time = new Date();
  // let y = time.getFullYear()
  // let m = time.getMonth() + 1
  // let d = time.getDate()
  let h = time.getHours();
  let mm = time.getMinutes();
  let s = time.getSeconds();
  let chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");
  // let charsIndex = Math.floor(Math.random() * 26);
  let idvalue = "";
  let n = 23; //这个值可以改变的，对应的生成多少个字母，根据自己需求所改
  for (let i = 0; i < n; i++) {
    idvalue += chars[getRandomIntInclusive(0, 51)];
  }
  // time.getTime().toString().split('').splice(-4).join('')
  // return "" + add(h) + add(mm) + add(s) + chars[charsIndex] + time.getTime();
  return "" + add(h) + add(mm) + add(s) + idvalue + time.getTime();
}

function getRandomIntInclusive(min, max) {
  const randomBuffer = new Uint32Array(1);
  window.crypto.getRandomValues(randomBuffer);
  let randomNumber = randomBuffer[0] / (0xffffffff + 1);
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(randomNumber * (max - min + 1)) + min;
}

export function getFileSize(fileByte) {
  var fileSizeByte = fileByte;
  var fileSizeMsg = "";
  if (fileSizeByte < 1048576) fileSizeMsg = (fileSizeByte / 1024).toFixed(2) + "KB";
  else if (fileSizeByte == 1048576) fileSizeMsg = "1MB";
  else if (fileSizeByte > 1048576 && fileSizeByte < 1073741824)
    fileSizeMsg = (fileSizeByte / (1024 * 1024)).toFixed(2) + "MB";
  else if (fileSizeByte > 1048576 && fileSizeByte == 1073741824) fileSizeMsg = "1GB";
  else if (fileSizeByte > 1073741824 && fileSizeByte < 1099511627776)
    fileSizeMsg = (fileSizeByte / (1024 * 1024 * 1024)).toFixed(2) + "GB";
  else fileSizeMsg = "文件超过1TB";
  return fileSizeMsg;
}

//深拷贝
export function deepCopy(obj) {
  if (obj == null) {
    return null;
  }
  var result = Array.isArray(obj) ? [] : {};
  for (let key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (typeof obj[key] === "object") {
        result[key] = deepCopy(obj[key]); // 如果是对象，再次调用该方法自身
      } else {
        result[key] = obj[key];
      }
    }
  }
  return result;
}

// 根据数组对象的某个键值去重方法
export function uniqueArr(arr, key) {
  const res = new Map();
  return arr.filter((item) => !res.has(item[key]) && res.set(item[key], 1));
}

export function parseUA() {
  var u = navigator.userAgent;
  return {
    //移动终端浏览器版本信息
    mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
    android: u.indexOf("Android") > -1 || u.indexOf("Linux") > -1, //android终端或uc浏览器
    iPhone: u.indexOf("iPhone") > -1, //是否为iPhone或者QQHD浏览器
    weixin: u.toLocaleLowerCase().match(/MicroMessenger/i) == "micromessenger",
    wechatdevtool: u.match(/wechatdevtools/i) == "wechatdevtools", //微信开发工具
    miniProgram: u.toLocaleLowerCase().indexOf("miniprogram") !== -1, //微信小程序
  };
}

// 小数乘法
export function floatMul(arg1, arg2) {
  let m = 0,
    s1 = arg1.toString(),
    s2 = arg2.toString();
  try {
    m += s1.split(".")[1].length;
  } catch (e) {
    let u;
  }
  try {
    m += s2.split(".")[1].length;
  } catch (e) {
    let u;
  }
  return (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) / Math.pow(10, m);
}

//小数除法
export function floatDiv(arg1, arg2) {
  let t1 = 0,
    t2 = 0,
    r1,
    r2;
  try {
    t1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    let u;
  }
  try {
    t2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    let u;
  }
  r1 = Number(arg1.toString().replace(".", ""));
  r2 = Number(arg2.toString().replace(".", ""));
  return (r1 / r2) * Math.pow(10, t2 - t1);
}

//小数加法
export function floatAdd(arg1, arg2) {
  let r1, r2, m;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
}

//小数减法
export function floatSub(arg1, arg2) {
  let r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
}

/**
 * 四舍五入保留n位小数
 * @param {number} number
 * @param {number} digit
 * @returns {number}
 */
export function roundReserveDecimals(number = 0, digit = 2) {
  return floatDiv(Math.round(floatMul(number, Math.pow(10, digit))), Math.pow(10, digit));
}

export function commonSkip(path, params, routeMethod = "push", isRefresh = false) {
  if (this.$isMiniProgram) {
    let query = "";
    for (let key in params) {
      query += `${key}=${params[key]}&`;
    }
    query = query.slice(0, query.length - 1);
    let url = `${path}?${query}${isRefresh ? "&isRefresh=true" : ""}`;
    wxNavigateTo(url, "", "");
  } else {
    this.$router[routeMethod]({ path, query: params });
  }
}

export function commonBack(needRefresh = false) {
  if (this.$isMiniProgram) {
    if (needRefresh) {
      wxPostMessage({ needRefresh: true });
    }
    wxNavigateBack();
  } else {
    this.$router.go(-1);
  }
}

// 字段过滤，传入对象以及需要返回的字段数组，过滤出一个只包含字段数组所含字段的对象
export function filterObjectByFieldArr(obj, arr) {
  return Object.fromEntries(Object.entries(obj).filter(([key]) => arr.includes(key)));
}

export function base64ToFile(base64, fileName) {
  // 将base64按照 , 进行分割 将前缀  与后续内容分隔开
  let data = base64.split(",");
  // 利用正则表达式 从前缀中获取图片的类型信息（image/png、image/jpeg、image/webp等）
  let type = data[0].match(/:(.*?);/)[1];
  // 从图片的类型信息中 获取具体的文件格式后缀（png、jpeg、webp）
  let suffix = type.split("/")[1];
  // 使用atob()对base64数据进行解码  结果是一个文件数据流 以字符串的格式输出
  const bstr = window.atob(data[1]);
  // 获取解码结果字符串的长度
  let n = bstr.length;
  // 根据解码结果字符串的长度创建一个等长的整形数字数组
  // 但在创建时 所有元素初始值都为 0
  const u8arr = new Uint8Array(n);
  // 将整形数组的每个元素填充为解码结果字符串对应位置字符的UTF-16 编码单元
  while (n--) {
    // charCodeAt()：获取给定索引处字符对应的 UTF-16 代码单元
    u8arr[n] = bstr.charCodeAt(n);
  }
  // 利用构造函数创建File文件对象
  // new File(bits, name, options)
  const file = new File([u8arr], `${fileName}.${suffix}`, {
    type: type,
  });
  // 将File文件对象返回给方法的调用者
  return file;
}

//判断字符串是否为JSON数据
export function isJSON(str) {
  if (typeof str == "string") {
    try {
      let obj = JSON.parse(str);
      if (typeof obj == "object" && obj) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}

// 在utils.js中
export const isEmpty = (value) => {
  if (value === 0) return false;
  return (
    [undefined, null, "", NaN].includes(value) ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === "object" && Object.keys(value).length === 0)
  );
};

const sm3 = require("sm-crypto").sm3;
/**
 * sm3加密
 * @param {*} str
 */
export function sm3Encrypt(str) {
  return sm3(str);
}

const sm2 = require("sm-crypto").sm2;
const publicKey = process.env.VUE_APP_PUBLICKEY;
const privateKey = process.env.VUE_APP_PRIVATEKEY;

/**
 * sm2加密
 * @param {string} plainText - 要加密的明文
 * @param {string} publicKey - SM2公钥
 * @param {number} mode - 加密模式，1表示C1C3C2模式（默认），0表示C1C2C3模式
 * @returns {string} - 加密后的密文
 */
export function sm2Encrypt(plainText, mode = 1) {
  return "04" + sm2.doEncrypt(plainText, publicKey, mode);
}

/**
 * sm2解密（通常在服务器端使用，前端一般不会有私钥）
 * @param {string} cipherText - 要解密的密文
 * @param {string} privateKey - SM2私钥
 * @param {number} mode - 解密模式，1表示C1C3C2模式（默认），0表示C1C2C3模式
 * @returns {string} - 解密后的明文
 */
export function sm2Decrypt(cipherText, mode = 1) {
  if (cipherText.startsWith("04")) {
    cipherText = cipherText.substring(2); // 去 04
  }
  return sm2.doDecrypt(cipherText, privateKey, mode);
}
