<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.plateNumber"
              name="plateNumber"
              label="车牌号"
              placeholder="请选择车牌号"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.plateNumber"
              required
              @click="showCarPicker = true"
            />
            <van-field
              v-model="ruleForm.driverName"
              name="driverName"
              label="驾驶司机"
              placeholder="请选择驾驶司机"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.driverName"
              required
              @click="showUserPicker = true"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="违章事故信息"></baseTitle>
            <van-field
              v-model="ruleForm.occurrenceTime"
              name="occurrenceTime"
              label="发生时间"
              placeholder="请选择发生时间"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.occurrenceTime"
              required
              @click="openDatePicker('occurrenceTime')"
            />
            <van-field
              v-model="ruleForm.location"
              name="location"
              label="发生地点"
              placeholder="请输入发生地点"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.location"
              required
              maxlength="50"
            />
            <van-field
              v-model.number="ruleForm.penaltyAmount"
              name="penaltyAmount"
              label="处罚/赔偿金额(元)"
              placeholder="请输入处罚/赔偿金额"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rules.penaltyAmount"
              required
              label-width="130"
            />
            <van-field
              v-model.number="ruleForm.deductionPoint"
              name="deductionPoint"
              label="扣分"
              placeholder="请输入被扣分数"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rules.deductionPoint"
              required
            />
            <van-field
              v-model="ruleForm.degreeName"
              name="degree"
              label="车辆损坏程度"
              placeholder="请选择车辆损坏程度"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.degree"
              required
              @click="showDegreePicker = true"
            />
            <van-field label="事故描述" :border="false" label-width="100%" required />
            <van-field
              v-model="ruleForm.description"
              name="description"
              label=""
              rows="4"
              type="textarea"
              maxlength="200"
              placeholder="简述事故，字数控制在200字内。"
              show-word-limit
              :border="true"
              :rules="rules.description"
            />
            <van-field
              v-model="ruleForm.statusName"
              name="status"
              label="处理状态"
              placeholder="请选择处理状态"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.status"
              required
              @click="showStatusPicker = true"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="凭证信息"></baseTitle>
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">凭证</div>
                </div>
              </template>
            </van-field>
            <van-field label="违章事故现场照片" :border="false" label-width="100%" />
            <imageUpload
              v-model="ruleForm.violationPhoto"
              formId="violationPhoto"
              formPlaceHolder="请上传违章事故现场照片"
              :isRequired="true"
              :maxNumber="1"
            ></imageUpload>
            <van-field label="违章事故处罚单凭证" :border="false" label-width="100%" />
            <imageUpload
              v-model="ruleForm.violationPenaltyCertificate"
              formId="violationPenaltyCertificate"
              formPlaceHolder="请上传违章事故处罚单凭证"
              :isRequired="true"
              :maxNumber="1"
            ></imageUpload>
            <van-field label="违章事故处罚缴费凭证" :border="false" label-width="100%" />
            <imageUpload
              v-model="ruleForm.violationPayCertificate"
              formId="violationPayCertificate"
              formPlaceHolder="请上传违章事故处罚缴费凭证"
              :isRequired="true"
              :maxNumber="1"
            ></imageUpload>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="onCarConfirm"
        @cancel="showCarPicker = false"
      />
    </van-popup>
    <van-popup v-model="showUserPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="userOptions"
        @confirm="onUserConfirm"
        @cancel="showUserPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDegreePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="DEGREE_LIST"
        @confirm="onDegreeConfirm"
        @cancel="showDegreePicker = false"
      />
    </van-popup>
    <van-popup v-model="showStatusPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="STATUS_LIST"
        @confirm="onStatusConfirm"
        @cancel="showStatusPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="datetime"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFunByParams, getListApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import { DEGREE_LIST, STATUS_LIST } from "@/enums";
  import { deepCopy } from "@/utils";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: {
      imageUpload,
      baseTitle,
    },
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          userList: "/api/baseuser/list",
          create: "/api/vehicle/illegal/create",
          carList: "/api/vehicle/dossier/list",
          defaultCar: "/api/vehicle/dossier/findByPhone",
        },
        showCarPicker: false,
        carOptions: [],
        userOptions: [],
        showUserPicker: false,
        defaultCarIndex: 0,
        showDatePicker: false,
        showDegreePicker: false,
        DEGREE_LIST,
        showStatusPicker: false,
        STATUS_LIST,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(),
        ruleForm: {
          plateNumber: null,
          driverName: null,
          driverId: "",
          occurrenceTime: null,
          location: null,
          penaltyAmount: null,
          deductionPoint: null,
          degree: null,
          degreeName: "",
          status: null,
          statusName: "",
          description: null,
          violationPhoto: [],
          violationPenaltyCertificate: [],
          violationPayCertificate: [],
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号" }],
          driverName: [{ required: true, message: "请选择驾驶司机" }],
          occurrenceTime: [{ required: true, message: "请选择发生时间" }],
          location: [{ required: true, message: "请输入发生地点" }],
          penaltyAmount: [{ required: true, message: "请输入处罚/赔偿金额" }],
          deductionPoint: [{ required: true, message: "请输入被扣分数" }],
          description: [{ required: true, message: "请输入事故描述" }],
          degree: [{ required: true, message: "请选择车辆损坏程度" }],
          status: [{ required: true, message: "请选择处理状态" }],
        },
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.userOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.getUserDefaultCar();
      },
      // 获取当前用户默认车牌号
      async getUserDefaultCar() {
        let res = await getInfoApiFunByParams(
          { phone: this.userInfo.phone ? this.$sm2Encrypt(this.userInfo.phone) : "" },
          this.apis.defaultCar,
        );
        if (res.data) {
          this.ruleForm.plateNumber = res.data.plateNumber;
          let index = this.carOptions.findIndex((option) => option.id == res.data.id);
          if (index > 0) {
            this.defaultCarIndex = index;
          }
        }
      },
      // 确认选择车牌号
      onCarConfirm(item) {
        this.ruleForm.plateNumber = item.name;
        this.showCarPicker = false;
      },
      // 确认选择驾驶司机
      onUserConfirm(item) {
        this.ruleForm.driverName = item.fullName;
        this.ruleForm.driverId = item.lgUnionId;
        this.showUserPicker = false;
      },
      // 确认选择车辆损坏程度
      onDegreeConfirm(item) {
        this.ruleForm.degree = item.id;
        this.ruleForm.degreeName = item.name;
        this.showDegreePicker = false;
      },
      // 确认选择处理状态
      onStatusConfirm(item) {
        this.ruleForm.status = item.id;
        this.ruleForm.statusName = item.name;
        this.showStatusPicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD HH:mm");
        this.showDatePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = deepCopy(this.ruleForm);
            params.violationPhoto = params.violationPhoto[0];
            params.violationPenaltyCertificate = params.violationPenaltyCertificate[0];
            params.violationPayCertificate = params.violationPayCertificate[0];
            try {
              let res = await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增车辆违章事故记录成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
