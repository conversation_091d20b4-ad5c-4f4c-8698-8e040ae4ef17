<template>
  <div class="page-container">
    <header class="page-header">
      <van-field
        v-model="filterForm.plateNumber"
        label=""
        placeholder="请输入车牌号"
        right-icon="search"
        clearable
        @change="searchList"
        @clear="resetList"
      >
        <template #right-icon>
          <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
        </template>
      </van-field>
    </header>
    <main class="page-main">
      <ul class="list" v-if="carOptions.length > 0">
        <li class="list-item" v-for="(item, index) in carOptions" :key="index" @click="selectPlateNumber(item)">
          <div class="item-content">
            <div class="item-left">
              <div class="item-title">车牌号：{{ item.name }}</div>
            </div>
            <div class="item-right">
              <span class="sctmp-iconfont icon-ic_xiazuan"></span>
            </div>
          </div>
        </li>
      </ul>
      <van-empty description="暂无数据" v-else />
    </main>
    <van-dialog
      v-model="showDialog"
      title=""
      show-cancel-button
      cancel-button-text="重置"
      confirm-button-text="查询"
      confirm-button-color="#4ca786"
      :before-close="beforeClose"
    >
      <van-field label="记录开始时间" label-width="100%" />
      <van-field
        v-model="startTime"
        label=""
        placeholder="请选择开始时间"
        is-link
        arrow-direction="down"
        readonly
        :border="false"
        @click="handleClick('startTime')"
      />
      <van-field label="记录结束时间" label-width="100%" />
      <van-field
        v-model="endTime"
        label=""
        placeholder="请选择结束时间"
        is-link
        arrow-direction="down"
        readonly
        :border="false"
        @click="handleClick('endTime')"
      />
    </van-dialog>
    <van-popup v-model="showPicker" position="bottom">
      <van-datetimesec-picker
        v-model="currentDate"
        :datetimePickerProps="{ 'visible-item-count': 6, title: '选择完整时间' }"
        :pickerProps="{ 'visible-item-count': 6 }"
        @cancel="showPicker = false"
        @input="onConfirm"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import moment from "moment";
  import VanDatetimesecPicker from "@/components/van-datetimesec-picker";
  export default {
    components: { VanDatetimesecPicker },
    data() {
      return {
        filterForm: {
          plateNumber: "",
        },
        plateNumber: "",
        carList: [],
        carOptions: [],
        apis: {
          carList: "/api/vehicle/dossier/list",
        },
        showDialog: false,
        showPicker: false,
        currentDate: new Date().getTime(),
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        startTime: "",
        endTime: "",
        currentField: "",
      };
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let res = await createApiFun({ statusList: [0, 1] }, this.apis.carList);
        this.carList = res.data;
        this.carOptions = res.data;
      },
      // 选择车牌号
      selectPlateNumber(item) {
        this.plateNumber = item.name;
        this.showDialog = true;
      },
      // 重置
      resetList() {
        this.filterForm.plateNumber = "";
        this.carOptions = this.carList;
      },
      // 查询
      searchList() {
        if (!this.filterForm.plateNumber) {
          this.carOptions = this.carList;
        }
        this.carOptions = this.carList.filter((list) => list.name.includes(this.filterForm.plateNumber));
      },
      // 点击
      handleClick(field) {
        this.currentField = field;
        this.showPicker = true;
      },
      // 确认
      onConfirm(value) {
        this[this.currentField] = moment(value).format("YYYY-MM-DD HH:mm:ss");
        this.showPicker = false;
      },
      // 关闭前回调
      beforeClose(action, done) {
        if (action === "cancel") {
          this.startTime = "";
          this.endTime = "";
          done();
        }
        if (action === "confirm") {
          if (!this.startTime) {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: "请选择开始时间",
              duration: 1500,
            });
            done(false);
            return;
          }
          if (!this.endTime) {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: "请选择结束时间",
              duration: 1500,
            });
            done(false);
            return;
          }
          this.$commonSkip("travelRecord", {
            plateNumber: this.plateNumber,
            startTime: this.startTime,
            endTime: this.endTime,
          });
          done();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
