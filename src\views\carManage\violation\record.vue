<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList">
    <template #other="{ form }">
      <div class="field-item pb-15">
        <div class="border-line"></div>
        <baseTitle class="record-title" title="凭证信息"></baseTitle>
        <div class="record-content">
          <div class="title">凭证：</div>
          <div class="content mb-15 flex-center-start">
            <img
              class="image-item"
              :src="form.violationPhoto?.url"
              alt=""
              @click="imagePreview([form.violationPhoto.url])"
            />
            <span class="tip-text">违章事故现场照片</span>
          </div>
          <div class="content mb-15 flex-center-start">
            <img
              class="image-item"
              :src="form.violationPenaltyCertificate?.url"
              alt=""
              @click="imagePreview([form.violationPenaltyCertificate.url])"
            />
            <span class="tip-text">违章事故处罚凭证</span>
          </div>
          <div class="content flex-center-start">
            <img
              class="image-item"
              :src="form.violationPayCertificate?.url"
              alt=""
              @click="imagePreview([form.violationPayCertificate.url])"
            />
            <span class="tip-text">违章事故处罚缴费凭证</span>
          </div>
        </div>
      </div>
    </template>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex"></van-image-preview>
  </pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: { pageRecord, baseTitle },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicle/illegal/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "driverName",
                value: "司机",
              },
            ],
          },
          {
            title: "违章事故信息",
            recordFieldArr: [
              {
                key: "occurrenceTime",
                value: "发生时间",
              },
              {
                key: "location",
                value: "发生地点",
              },
              {
                key: "penaltyAmount",
                value: "罚款金额",
                unit: "元",
              },
              {
                key: "deductionPoint",
                value: "扣分",
              },
              {
                key: "degree",
                value: "车辆损坏程度",
                options: ["轻微", "一般", "严重", "特别严重", "无"],
              },
              {
                key: "description",
                value: "事故描述",
              },
              {
                key: "status",
                value: "处理状态",
                options: ["未处理", "已处理"],
              },
            ],
          },
        ],
        previewImages: [],
        previewIndex: 0,
        showPreview: false,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    methods: {
      // 图片预览
      imagePreview(fileList) {
        this.previewImages = fileList;
        this.previewIndex = 0;
        this.showPreview = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .field-item {
    .title {
      margin-right: 4px;
      font-weight: 400;
      font-size: 14px;
      color: #162e25;
      line-height: 16px;
      margin-bottom: 12px;
    }
    .content {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
    }
    .tip-text {
      color: #999999;
      margin-left: 30px;
    }
    .image-item {
      object-fit: cover;
    }
  }
  .mb-15 {
    margin-bottom: 15px;
  }
  .pb-15 {
    padding-bottom: 15px;
  }
</style>
