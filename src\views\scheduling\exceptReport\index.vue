<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入上报人/处理人名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { ABNORMAL_TYPE, CONTINUECARRYING_STATUS_MAP } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "reportingTime",
            value: "上报时间",
          },
          {
            key: "reportPerson",
            value: "上报人",
          },
          {
            key: "continueCarrying",
            value: "是否可以继续收运",
            enums: CONTINUECARRYING_STATUS_MAP,
          },
          {
            key: "exceptionType",
            value: "异常类型",
            enums: ABNORMAL_TYPE,
          },
        ],
        listPageApi: "/api/abnormalreporting/listPage",
        filterList: [
          {
            type: "Options",
            key: "exceptionType",
            value: "异常类型",
            enums: ABNORMAL_TYPE,
          },
          {
            type: "Date",
            key: "reportingTime",
            value: "上报日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("exceptReportRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
