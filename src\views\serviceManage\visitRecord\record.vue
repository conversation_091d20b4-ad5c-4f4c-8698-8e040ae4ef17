<template>
  <div class="create-container">
    <div class="create-box">
      <div class="create-content">
        <main class="create-main">
          <baseTitle title="基础信息"></baseTitle>
          <div class="record-item" v-for="(item, index) in baseList" :key="index">
            <div class="record-left">{{ item.name }}</div>
            <div class="record-right">{{ item.enums ? item.enums[ruleForm[item.key]] : ruleForm[item.key] }}</div>
          </div>
        </main>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="处理信息"></baseTitle>
          <div class="record-item" v-for="(item, index) in dealList" :key="index">
            <div class="record-left">{{ item.name }}</div>
            <div class="record-right">{{ item.enums ? item.enums[ruleForm[item.key]] : ruleForm[item.key] }}</div>
          </div>
        </main>
        <div class="border-line"></div>
        <main class="question-main">
          <baseTitle title="回访信息表" class="question-title"></baseTitle>
          <div class="question-item" v-for="(item, index) in questionList" :key="index">
            <van-field
              :label="`${index + 1}.${item.textName}(${QUESTION_TYPE[item.type]})`"
              :border="false"
              label-width="100%"
              :required="item.required"
            >
            </van-field>
            <van-field label="" :border="false" label-width="0">
              <template #input>
                <van-field
                  :value="item.textValue"
                  rows="5"
                  autosize
                  label=""
                  type="textarea"
                  maxlength="500"
                  show-word-limit
                  readonly
                  class="question-text"
                  v-if="item.type === 1"
                />
                <van-radio-group :value="item.textValue" checked-color="#4ca786" v-else-if="item.type === 2">
                  <van-radio
                    class="question-radio"
                    :name="child"
                    v-for="(child, childIndex) in item.options"
                    :key="childIndex"
                    >{{ child }}</van-radio
                  >
                </van-radio-group>
                <van-checkbox-group :value="item.textValue" checked-color="#4ca786" v-else-if="item.type === 3">
                  <van-checkbox
                    class="question-checkbox"
                    shape="square"
                    :name="child"
                    v-for="(child, childIndex) in item.options"
                    :key="childIndex"
                    >{{ child }}</van-checkbox
                  >
                </van-checkbox-group>
              </template>
            </van-field>
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { VISIT_RECORD_TYPE, VISIT_HANDLE_STATUS, QUESTION_TYPE } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: {
      baseTitle,
    },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/followUpRecord/get/",
        },
        titleList: [
          { sort: "①", name: "基础信息" },
          { sort: "②", name: "处理信息" },
          { sort: "③", name: "回访记录表" },
        ],
        baseList: [
          { key: "customerName", name: "客商名称" },
          { key: "customerContactNumber", name: "客商联系电话" },
          { key: "customerEmail", name: "客商电子邮件" },
          { key: "method", name: "回访方式", enums: VISIT_RECORD_TYPE },
          { key: "personName", name: "回访人" },
          { key: "followUpDate", name: "回访日期" },
          { key: "content", name: "主要回访内容" },
          { key: "opinion", name: "客户反馈意见" },
        ],
        dealList: [
          { key: "handlingStatus", name: "处理情况", enums: VISIT_HANDLE_STATUS },
          { key: "handlingDate", name: "处理日期" },
          { key: "remarks", name: "处理备注" },
        ],
        QUESTION_TYPE,
        ruleForm: {},
        questionList: [],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取合同详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          for (let key in this.ruleForm) {
            this.ruleForm[key] = this.ruleForm[key] || this.ruleForm[key] === 0 ? this.ruleForm[key] : "-";
          }
          this.ruleForm.infos.forEach((item) => {
            item.textValue = JSON.parse(item.textValue);
          });
          this.questionList = this.ruleForm.infos;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 24px;
  }
  .question-main {
    background-color: #fff;
    .question-title {
      padding: 12px 24px;
    }
    .question-item {
      padding: 0 12px;
    }
  }
  .record-item {
    padding: 12px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 16px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 16px;
    text-align: right;
    word-break: break-all;
  }
  .question-radio,
  .question-checkbox {
    margin-bottom: 10px;
  }
  .question-text {
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
