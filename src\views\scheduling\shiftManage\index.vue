<template>
  <div class="page-container">
    <header class="page-header">
      <div class="header-input">
        <van-field
          v-model="applyFullName"
          label=""
          placeholder="请输入申请人名称"
          right-icon="search"
          clearable
          @change="onRefresh"
          @clear="onRefresh"
        >
          <template #right-icon>
            <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
          </template>
        </van-field>
      </div>
      <div class="filter-button" @click="showPop = !showPop">
        <span class="filter-title">筛选</span>
        <span class="sctmp-iconfont icon-ic_xia filter-icon" v-show="!showPop"></span>
        <span class="sctmp-iconfont icon-ic_shang filter-icon" v-show="showPop"></span>
      </div>
    </header>
    <div class="filter-box" v-show="showPop">
      <template v-if="filterList.length > 0">
        <div v-for="(filterItem, filterIndex) in filterList" :key="filterIndex">
          <component
            :ref="filterItem.type"
            :is="filterItem.type"
            v-model="filterForm[filterItem.key]"
            labelWidth="150"
            :filterItem="filterItem"
          />
        </div>
        <div class="filter-bottom">
          <div class="reset-button" @click="resetList">重置</div>
          <div class="search-button" @click="searchList">查询</div>
        </div>
      </template>
    </div>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="item-content" @click="itemClick(item)">
                <div class="item-left">
                  <div class="item-title">申请日期：{{ item.applyDate }}</div>
                  <div class="item-text">申请人：{{ item.applyFullName }}</div>
                  <div class="item-text">换班开始日期：{{ item.changeStartDate }}</div>
                  <div class="item-text">换班结束日期：{{ item.changeEndDate }}</div>
                  <div v-if="item.auditDate !== '-'" class="item-text"
                    >是否批准：{{ IS_NORMAL_OPERATION[item.applyStatus] }}</div
                  >
                </div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { IS_NORMAL_OPERATION } from "@/enums";
  import components from "@/components/pageList/componentImport";
  import moment from "moment";
  export default {
    components: components,
    data() {
      return {
        IS_NORMAL_OPERATION,
        showPop: false,
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        listPageApi: "/api/waybill/change/listPage",
        tableList: [],
        filterForm: {},
        filterList: [
          {
            type: "Date",
            key: "applyDate",
            value: "申请日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
        applyFullName: "",
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          applyFullName: this.applyFullName,
          ...this.filterForm,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas;
            dataList.forEach((list) => {
              for (let key in list) {
                list[key] = list[key] || list[key] === 0 ? list[key] : "-";
                list.showMore = false;
              }
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 列表项点击事件
      itemClick(item) {
        this.$commonSkip("shiftRecord", { id: item.id });
      },
      // 重置
      resetList() {
        this.filterForm = {};
        this.onRefresh();
        this.showPop = !this.showPop;
      },
      // 查询
      searchList() {
        this.onRefresh();
        this.showPop = !this.showPop;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
  }
  .header-input {
    flex: 1;
    overflow: hidden;
  }
  .filter-button {
    display: flex;
    align-items: center;
    margin-left: 16px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
      margin-right: 2px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
      margin-top: 2px;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .filter-box {
    background-color: #fff;
  }
  .filter-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    margin-bottom: 16px;
  }
  .reset-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #dce0df;
    border-radius: 20px;
  }
  .search-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #4ca786;
    border-radius: 20px;
    margin-left: 8px;
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  .item-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    .footer-box {
      padding: 6px 8px;
      background: #f0f2f1;
      border-radius: 3px;
      opacity: 0.5;
      display: flex;
      align-items: center;
      .footer-text {
        font-weight: 400;
        font-size: 12px;
        color: #5c6663;
        line-height: 14px;
        margin-right: 4px;
      }
      .footer-icon {
        font-size: 14px;
        color: #5c6663;
      }
    }
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
