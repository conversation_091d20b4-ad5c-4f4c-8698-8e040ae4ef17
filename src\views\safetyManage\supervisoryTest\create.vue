<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.inspectorName"
              name="inspectorName"
              label="检查人"
              placeholder="请输入检查人"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.inspectorName"
              required
              clearable
              maxlength="20"
            />
            <van-field
              v-model="ruleForm.checkedPersonName"
              name="checkedPersonName"
              label="被检查人"
              placeholder="请输入被检查人"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.checkedPersonName"
              required
              clearable
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.checkedVehiclePlateNumber"
              name="checkedVehiclePlateNumber"
              label="被检查车辆"
              placeholder="请选择被检查车辆"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.checkedVehiclePlateNumber"
              required
              @click="showCarPicker = true"
            />
            <van-field
              v-model="ruleForm.spotCheckDate"
              name="spotCheckDate"
              label="抽查日期"
              placeholder="请选择抽查日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.spotCheckDate"
              required
              @click="openDatePicker('spotCheckDate')"
            />
            <van-field label="是否危运车辆" input-align="right" error-message-align="right" :border="false" required>
              <template #input>
                <el-switch
                  v-model="ruleForm.isError"
                  active-text="是"
                  inactive-text="否"
                  active-color="#4CA786"
                  :active-value="1"
                  :inactive-value="0"
                >
                </el-switch>
              </template>
            </van-field>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="抽查信息"></baseTitle>
            <van-field
              :value="SUPERVISORY_TEST_COMPLETED[ruleForm.rectifiedStatus]"
              name="rectifiedStatus"
              label="是否完成整改"
              placeholder="请选择是否完成整改"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.rectifiedStatus"
              required
              @click="showCompletedPicker = true"
            />
            <van-field label="问题" :border="false" label-width="100%" required />
            <van-field
              v-model="ruleForm.problemDescription"
              name="problemDescription"
              label=""
              rows="5"
              type="textarea"
              maxlength="250"
              placeholder="请输入问题"
              show-word-limit
              :border="true"
              :rules="rules.problemDescription"
            />
            <van-field
              label="是否进行酒精测试"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              label-width="140"
            >
              <template #input>
                <el-switch
                  v-model="ruleForm.isAlcohol"
                  active-text="是"
                  inactive-text="否"
                  active-color="#4CA786"
                  :active-value="1"
                  :inactive-value="0"
                >
                </el-switch>
              </template>
            </van-field>
            <template v-if="ruleForm.isAlcohol">
              <van-field
                name="alcoholTestCount"
                label="酒精测试/人"
                input-align="right"
                error-message-align="right"
                :border="false"
              >
                <template #input>
                  <van-stepper v-model="ruleForm.alcoholTestCount" integer min="0" max="99999999" />
                </template>
              </van-field>
              <van-field label="酒精测试异常人员" :border="false" label-width="100%" />
              <van-field
                v-model="ruleForm.alcoholAbnormalPerson"
                label=""
                rows="5"
                type="textarea"
                placeholder="请输入酒精测试异常人员"
                :border="true"
              />
            </template>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="附件信息"></baseTitle>
            <van-field :border="false" label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">整改前照片</div>
                  <div class="label-text">(请上传整改前照片)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.beforeRectificationPhotoUrl"
              :fileList="files.beforeRectificationPhotoUrl"
              :isRequired="false"
              formId="beforeRectificationPhotoUrl"
              formPlaceHolder="请上传整改前照片"
            ></imageUpload>
            <van-field :border="false" label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">整改后照片</div>
                  <div class="label-text">(请上传整改后照片)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.afterRectificationPhotoUrl"
              :fileList="files.afterRectificationPhotoUrl"
              :isRequired="false"
              formId="afterRectificationPhotoUrl"
              formPlaceHolder="请上传整改后照片"
            ></imageUpload>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <VanSearchPicker
      value-key="name"
      :value.sync="showCarPicker"
      :options="carOptions"
      :index-value="ruleForm.checkedVehiclePlateNumber"
      index-key="name"
      @confirm="onCarConfirm"
    ></VanSearchPicker>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    <VanSearchPicker
      :value.sync="showCompletedPicker"
      :options="SUPERVISORY_TEST_COMPLETED"
      :index-value="ruleForm.rectifiedStatus"
      :show-search="false"
      is-index-value
      @confirm="onCompletedConfirm"
    ></VanSearchPicker>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun, updateApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import baseTitle from "@/components/baseTitle";
  import { SUPERVISORY_TEST_COMPLETED } from "@/enums";
  import { deepCopy } from "@/utils";
  import VanSearchPicker from "@/components/VanSearchPicker/index.vue";
  export default {
    components: {
      imageUpload,
      baseTitle,
      VanSearchPicker,
    },
    data() {
      return {
        SUPERVISORY_TEST_COMPLETED,
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/safety/spotCheckRecord/create",
          update: "/api/safety/spotCheckRecord/update",
          carList: "/api/vehicle/dossier/list",
          info: "/api/safety/spotCheckRecord/get/",
        },
        // 车辆
        showCarPicker: false,
        carOptions: [],
        // 司机
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(),
        showCompletedPicker: false,
        ruleForm: {
          inspectorName: "", //检查人
          checkedPersonId: "", //被检查人id
          checkedPersonName: "", //被检查人名称
          checkedVehiclePlateNumber: "", //检查车辆
          spotCheckDate: moment(new Date()).format("YYYY-MM-DD"), //抽查日期
          rectifiedStatus: "", //是否完成整改
          problemDescription: "", //问题
          alcoholTestCount: 0, //酒精测试/人
          beforeRectificationPhotoUrl: [], //整改前照片
          afterRectificationPhotoUrl: [], //整改后照片
          isError: 0, //是否危运车辆
          isAlcohol: 0, //是否进行酒精测试
          alcoholAbnormalPerson: "", //酒精测试异常人员
        },
        rules: {
          inspectorName: [{ required: true, message: "请输入检查人" }],
          checkedPersonName: [{ required: true, message: "请输入被检查人" }],
          checkedVehiclePlateNumber: [{ required: true, message: "请选择被检查车辆" }],
          spotCheckDate: [{ required: true, message: "请选择抽查日期" }],
          rectifiedStatus: [{ required: true, message: "请选择是否完成整改" }],
          problemDescription: [{ required: true, message: "请输入问题" }],
        },
        files: {
          beforeRectificationPhotoUrl: [], //整改前照片
          afterRectificationPhotoUrl: [], //整改后照片
        },
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
      this.getOptions();
      document.title = `${this.$route.query.id ? "编辑" : "新增"}监督抽查记录`;
      if (this.$route.query.id) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.$route.query.id, this.apis.info);
        if (res.success) {
          try {
            res.data.beforeRectificationPhotoUrl = JSON.parse(res.data.beforeRectificationPhotoUrl);
            res.data.afterRectificationPhotoUrl = JSON.parse(res.data.afterRectificationPhotoUrl);
          } catch (error) {
            res.data.beforeRectificationPhotoUrl = [];
            res.data.afterRectificationPhotoUrl = [];
          }
          this.ruleForm = res.data;
          this.currentDate = new Date(this.ruleForm.spotCheckDate);
          this.handleBackImage();
        }
      },
      // 确认选择车牌号
      onCarConfirm({ item }) {
        this.ruleForm.checkedVehiclePlateNumber = item.name;
        this.showCarPicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 确认选择是否完成整改
      onCompletedConfirm({ index }) {
        this.ruleForm.rectifiedStatus = index;
        this.showCompletedPicker = false;
      },
      handleBackImage() {
        for (const key in this.files) {
          if (Object.hasOwnProperty.call(this.files, key)) {
            if (this.ruleForm[key] && this.ruleForm[key].length > 0) {
              this.files[key] = this.ruleForm[key];
            }
          }
        }
      },
      // 提交
      onSubmit() {
        console.log("this.ruleForm ==> ", this.ruleForm);

        this.$refs.formCreate
          .validate()
          .then(async () => {
            let params = deepCopy(this.ruleForm);
            params.beforeRectificationPhotoUrl = JSON.stringify(params.beforeRectificationPhotoUrl);
            params.afterRectificationPhotoUrl = JSON.stringify(params.afterRectificationPhotoUrl);
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = this.$route.query.id
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: `${this.$route.query.id ? "修改" : "新增"}监督抽查记录成功`,
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
