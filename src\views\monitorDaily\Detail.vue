<template>
  <div class="carCount-wrapper">
    <div class="card">
      <div class="card-item" v-for="item in itemList" :key="item.name">
        <div class="label">{{ item.name }}</div>
        <div class="value">{{ item.value }} {{ item.beside }}</div>
      </div>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";
  import { percentageCb } from "@/utils/dataCb";
  export default {
    components: {},
    props: {},
    data() {
      return {
        api: {
          getData: "/api/regulatory/waybillInfo",
        },
        itemList: [
          { name: "今日应收24小时点位数量", value: "0", beside: "个" },
          { name: "今日已收24小时点位数量", value: "0", beside: "个" },
          { name: "今日24小时点位收运率", value: "0", beside: "%" },
          { name: "今日应收48小时点位数量", value: "0", beside: "个" },
          { name: "今日已收48小时点位数量", value: "0", beside: "个" },
          { name: "今日48小时点位收运率", value: "0", beside: "%" },
          { name: "本周收运率", value: "0", beside: "%" },
          { name: "本月收运率", value: "0", beside: "%" },
          { name: "本年收运率", value: "0", beside: "%" },
        ],
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
    },
    methods: {
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          let summaryData = data.data;
          this.itemList[0].value = summaryData.dayPointNum;
          this.itemList[1].value = summaryData.dayCollectNum;
          this.itemList[2].value = percentageCb(summaryData.dayCollectPercentage);
          this.itemList[3].value = summaryData.twoDayPointNum;
          this.itemList[4].value = summaryData.twoDayCollectNum;
          this.itemList[5].value = percentageCb(summaryData.twoDayCollectPercentage);
          this.itemList[6].value = percentageCb(summaryData.weekCollectPercentage);
          this.itemList[7].value = percentageCb(summaryData.monthCollectPercentage);
          this.itemList[8].value = percentageCb(summaryData.yearCollectPercentage);
        } catch (error) {
          console.log(error);
        }
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .carCount-wrapper {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    padding: 12px;
  }
  .card {
    background-color: #fff;
    width: 100%;
    border-radius: 8px;
  }
  .card-item {
    display: flex;
    justify-content: space-between;
  }
  .label {
    font-size: 14px;
    font-weight: 400;
    color: #162e25;
    line-height: 40px;
    margin-left: 12px;
  }
  .value {
    font-weight: 400;
    font-size: 14px;
    line-height: 40px;
    color: #5c6663;
    margin-right: 12px;
  }
</style>
