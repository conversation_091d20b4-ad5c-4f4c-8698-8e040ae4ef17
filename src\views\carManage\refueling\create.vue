<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" label-width="180px" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.plateNumber"
              name="plateNumber"
              label="车牌号"
              placeholder="请选择车牌号"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.plateNumber"
              required
              @click="handleOpenPicker('showCarPicker')"
            />
            <van-field
              v-model="ruleForm.driverName"
              name="driverDossierId"
              label="驾驶司机"
              placeholder="请选择驾驶司机"
              input-align="right"
              error-message-align="right"
              is-link
              readonly
              :border="false"
              :rules="rules.driverDossierId"
              required
              @click="handleOpenPicker('showUserPicker')"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="加油信息"></baseTitle>
            <van-field
              v-model="ruleForm.refuelTypeName"
              name="refuelType"
              label="加油类型"
              placeholder="请选择加油类型"
              input-align="right"
              error-message-align="right"
              is-link
              readonly
              :border="false"
              :rules="rules.refuelType"
              @click="handleOpenPicker('showRefuelTypePicker')"
              required
            />
            <van-field
              v-model="ruleForm.fuelQuantity"
              name="fuelQuantity"
              label="加油(L)/充电量(Kwh)"
              placeholder="请输入加油/充电量"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :maxlength="9"
              :rules="rules.fuelQuantity"
              required
            />
            <van-field
              v-model="ruleForm.fuelTime"
              name="fuelTime"
              label="加油/充电日期"
              placeholder="请选择加油/充电日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.fuelTime"
              required
              @click="openDatePicker('fuelTime')"
            />
            <van-field
              v-model="ruleForm.amount"
              name="amount"
              label="加油/充电金额(元)"
              placeholder="请输入加油/充电金额"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :maxlength="9"
              :rules="rules.amount"
              required
            />
            <van-field
              v-model="ruleForm.fuelPlace"
              name="fuelPlace"
              label="加油地点"
              placeholder="请输入加油地点"
              input-align="right"
              error-message-align="right"
              type="textarea"
              :border="false"
              :autosize="{ maxHeight: 100, minHeight: 50 }"
              :maxlength="50"
              show-word-limit
            />
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">凭证</div>
                  <div class="label-text">(请上传加油凭证)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.vehicleFileList"
              :isRequired="true"
              formId="vehicleFileList"
              formPlaceHolder="请上传加油凭证"
            ></imageUpload>
            <div class="copy-button" v-if="presentRecord">
              <van-button
                native-type="button"
                class="round-22"
                block
                type="info"
                color="#4CA786"
                size="small"
                @click="copyRecord"
                >使用上次填写的加油记录信息</van-button
              >
            </div>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="里程信息"></baseTitle>
            <van-field
              v-model="ruleForm.totalMileage"
              name="totalMileage"
              label="行驶总里程(KM)"
              placeholder="请输入行驶总里程"
              input-align="right"
              error-message-align="right"
              type="number"
              :maxlength="9"
              :border="false"
              :rules="rules.totalMileage"
              required
            />
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">仪表里程照片</div>
                  <div class="label-text">(请上传车辆仪表里程照片)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.dashBoardFileList"
              :isRequired="true"
              formId="dashBoardFileList"
              formPlaceHolder="请上传车辆仪表里程照片"
            ></imageUpload>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="handlePickerConfirm($event, 'plateNumber', 'plateNumber')"
        @cancel="showCarPicker = false"
      />
    </van-popup>
    <van-popup v-model="showUserPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="userOptions"
        :default-index="defaultUserIndex"
        @confirm="handlePickerConfirm($event, 'driverDossierId', 'driverName')"
        @cancel="showUserPicker = false"
      />
    </van-popup>
    <van-popup v-model="showRefuelTypePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="refuelTypeOptions"
        :default-index="defaultRefuelTypeIndex"
        @confirm="handlePickerConfirm($event, 'refuelType', 'refuelTypeName')"
        @cancel="showRefuelTypePicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, getInfoApiFunByParams, getListApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import { filterObjectByFieldArr } from "@/utils";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: {
      imageUpload,
      baseTitle,
    },
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/vehicleFuel/create",
          userList: "/api/baseuser/list",
          carList: "/api/vehicle/dossier/list",
          defaultCar: "/api/vehicle/dossier/findByPhone",
          refuelTypeList: "/api/dict/refuelType/list",
          update: "/api/vehicleFuel/update",
          info: "/api/vehicleFuel/get/",
          getNewVehicleFuel: "/api/vehicleFuel/getNewVehicleFuel",
        },
        showCarPicker: false,
        carOptions: [],
        defaultCarIndex: 0,
        showUserPicker: false,
        userOptions: [],
        defaultUserIndex: 0,
        showRefuelTypePicker: false,
        defaultRefuelTypeIndex: 0,
        refuelTypeOptions: [],
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(),
        ruleForm: {
          plateNumber: null,
          driverDossierId: null,
          driverName: null,
          totalMileage: null,
          refuelType: null,
          refuelTypeName: null,
          fuelQuantity: null,
          fuelTime: null,
          amount: null,
          fuelPlace: null,
          vehicleFileList: [],
          dashBoardFileList: [],
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号" }],
          driverDossierId: [{ required: true, message: "请选择驾驶司机" }],
          refuelType: [{ required: true, message: "请选择加油类型" }],
          fuelQuantity: [{ required: true, message: "请输入加油/充电量" }],
          fuelTime: [{ required: true, message: "请选择加油/充电日期" }],
          amount: [{ required: true, message: "请输入加油/充电金额" }],
          totalMileage: [{ required: true, message: "请输入行驶总里程" }],
        },
        presentRecord: "",
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getInfoApiFun("", this.apis.refuelTypeList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.userOptions = res[1].data.map((item) => {
          return {
            id: item.lgUnionId,
            name: item.fullName,
          };
        });
        let index = this.userOptions.findIndex((option) => option.id === this.userInfo.lgUnionId);
        if (index > 0) {
          this.ruleForm.driverDossierId = this.userOptions[index].id;
          this.ruleForm.driverName = this.userOptions[index].name;
          this.defaultUserIndex = index;
        }
        this.refuelTypeOptions = res[2].data;
        this.getUserDefaultCar();
      },
      // 获取当前用户默认车牌号
      async getUserDefaultCar() {
        let res = await getInfoApiFunByParams(
          { phone: this.userInfo.phone ? this.$sm2Encrypt(this.userInfo.phone) : "" },
          this.apis.defaultCar,
        );
        if (res.data) {
          this.ruleForm.plateNumber = res.data.plateNumber;
          let index = this.carOptions.findIndex((option) => option.id == res.data.id);
          if (index > 0) {
            this.defaultCarIndex = index;
          }
        }
      },
      //打开弹窗
      handleOpenPicker(field) {
        this[field] = true;
      },
      // 确认选择
      handlePickerConfirm(item, field, fieldName) {
        this.ruleForm[field] = item.id;
        this.ruleForm[fieldName] = item.name;
        this.showCarPicker = false;
        this.showUserPicker = false;
        this.showRefuelTypePicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 根据车牌号获取最新的提交记录
      async getPresentRefueling() {
        let res = await createApiFun({ plateNumber: this.ruleForm.plateNumber }, this.apis.getNewVehicleFuel);
        if (res.success) {
          this.presentRecord = res.data;
        }
      },
      // 复制上一次的提交记录
      copyRecord() {
        Object.assign(
          this.ruleForm,
          filterObjectByFieldArr(this.presentRecord, [
            "refuelType",
            "refuelTypeName",
            "fuelQuantity",
            "fuelTime",
            "amount",
            "fuelPlace",
          ]),
        );
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增车辆加油记录成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .copy-button {
    padding: 12px 24px;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
