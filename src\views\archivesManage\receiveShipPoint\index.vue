<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    :ifShowMore="true"
    :listFieldArrOther="listFieldArrOther"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入点位名称/点位编号"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList/formIndex.vue";
  import { POINT_TYPE, DEVICE_STATUS, COLLECTION_CYCLE } from "@/enums";
  import { getInfoApiFunByParams } from "@/api/base";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "name",
            value: "点位名称",
          },
          {
            key: "code",
            value: "点位编号",
          },
          {
            key: "type",
            value: "点位类型",
            enums: POINT_TYPE,
          },
          {
            key: "contact",
            value: "点位联系人",
          },
          {
            key: "contactPhone",
            value: "联系方式",
          },
          {
            key: "address",
            value: "点位地址",
          },
        ],
        listFieldArrOther: [
          {
            key: "isUndock",
            value: "移除状态",
            enums: ["否", "移除"],
          },
          {
            key: "deviceStatus",
            value: "设备状态",
            enums: DEVICE_STATUS,
          },
          {
            key: "period",
            value: "收运周期",
            enums: COLLECTION_CYCLE,
          },
          {
            key: "frequency",
            value: "收运频次",
          },
        ],
        apis: {
          listPage: "/api/pickup/pickupPoint/listPage",
          regionList: "/api/region/regionList",
        },
        filterList: [
          {
            type: "Options",
            key: "deviceStatus",
            value: "设备状态",
            enums: DEVICE_STATUS,
          },
          {
            type: "Options",
            key: "isUndock",
            value: "移除状态",
            enums: ["否", "移除"],
          },
          {
            type: "Options",
            key: "districtId",
            value: "所属区域",
            keyword: "id",
            valueKey: "name",
            enums: [],
          },
        ],
      };
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [getInfoApiFunByParams({ pid: "440100000000" }, this.apis.regionList)];
        let res = await Promise.all(promiseList);
        this.filterList[2].enums = res[0].data;
      },
      // 查看详情
      itemClick(item) {
        this.$commonSkip("receiveShipPointRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
