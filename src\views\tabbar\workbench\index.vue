<template>
  <div class="page-container">
    <div class="page-main">
      <div class="main-title">工作台</div>
      <menus-grid :menusData="menusData" v-if="menusData && menusData.length"></menus-grid>
    </div>
    <tabbar active="workbench"></tabbar>
  </div>
</template>

<script>
  import tabbar from "@/components/tabbar";
  import menusGrid from "./components/menusGrid.vue";
  import { getUserInfo, getLevelId } from "@/utils/storage";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      tabbar,
      menusGrid,
    },
    data() {
      return {
        menusData: [],
      };
    },
    created() {
      let userInfo = getUserInfo();
      if (userInfo) {
        this.getAuthorizationDistinct();
      } else {
        this.menusData = [];
      }
    },
    methods: {
      async getAuthorizationDistinct() {
        let roleId = getLevelId();
        try {
          let res = await createApiFun(
            {
              roleId,
              menuType: 36,
              isPolymerization: true,
            },
            "/api/userrole/authorizationRole",
          );
          let menus = this.formatData(res.data.menus);
          this.menusData = menus;
        } catch (error) {
          this.menusData = [];
          console.warn(error);
        }
      },
      //格式化图标数据
      formatData(list) {
        let newArr = [];
        list.forEach((item) => {
          let obj = {
            ...item,
            icon: item.icon ? JSON.parse(item.icon).url : null,
            checkedIcon: item.checkedIcon ? JSON.parse(item.checkedIcon).url : null,
            menus: [],
          };
          if (item.menus && item.menus.length) {
            obj.menus = this.formatData(item.menus);
          }
          newArr.push(obj);
        });
        return newArr;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    .page-main {
      flex: 1;
      overflow-y: auto;
    }
  }
  .main-title {
    padding: 16px 0;
    text-align: center;
    font-size: 17px;
    color: #323233;
    line-height: 18px;
    font-weight: 600;
    background-color: #fff;
  }
</style>
