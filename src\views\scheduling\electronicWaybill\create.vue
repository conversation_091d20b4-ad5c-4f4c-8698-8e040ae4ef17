<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate" v-show="!showPointAdjust">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.productionUnit"
              name="productionUnit"
              label="产废单位名称"
              placeholder="请填写产废单位名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="[{ required: true, message: '请填写产废单位名称' }]"
              required
              maxlength="50"
              readonly
              v-if="ruleForm.id"
            />
            <van-field
              v-else
              v-model="ruleForm.productionUnit"
              name="productionUnit"
              label="产废单位名称"
              placeholder="请填写产废单位名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="[{ required: true, message: '请填写产废单位名称' }]"
              required
              maxlength="50"
              readonly
              is-link
              arrow-direction="down"
              @click="showPointPicker = true"
            />
            <van-field
              v-model="ruleForm.productionUnitOperator"
              name="productionUnitOperator"
              label="产废单位经办人"
              placeholder="请填写产废单位经办人"
              input-align="right"
              error-message-align="right"
              label-width="120"
              :border="false"
              maxlength="50"
              :readonly="ruleForm.id ? true : false"
              :required="ruleForm.id ? false : true"
              :rules="[{ required: ruleForm.id ? false : true, message: '请填写产废单位经办人' }]"
            />
            <van-field
              v-model="ruleForm.productionUnitOperatorPhone"
              name="productionUnitOperatorPhone"
              label="经办人联系方式"
              placeholder="请填写经办人联系方式"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="[{ required: true, message: '请填写经办人联系方式' }]"
              required
              maxlength="11"
              label-width="120"
              v-if="!ruleForm.id"
            />
            <van-field
              v-if="!ruleForm.id"
              :value="regionObj[ruleForm.districtId]"
              name="districtId"
              label="产废单位省市区"
              placeholder="请填写产废单位省市区"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="[{ required: true, message: '请填写产废单位省市区' }]"
              required
              readonly
              is-link
              arrow-direction="down"
              label-width="120"
              @click="showRegionPicker = true"
            />
            <van-field
              v-model="ruleForm.address"
              name="address"
              label="产废单位地址"
              placeholder="点击按钮获取当前定位地址"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="[{ required: true, message: '请填写产废单位地址' }]"
              required
              readonly
              @click-right-icon="showPointAdjust = true"
            >
              <template #right-icon>
                <span class="el-icon-map-location location-icon"></span>
              </template>
            </van-field>
            <van-field
              v-if="!ruleForm.id"
              :value="POINT_TYPE[ruleForm.type]"
              name="type"
              label="产废单位类型"
              placeholder="请填写产废单位类型"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="[{ required: true, message: '请填写产废单位类型' }]"
              required
              readonly
              is-link
              arrow-direction="down"
              @click="showTypePicker = true"
            />
            <van-field
              name="operation"
              label="点位是否正常经营"
              input-align="right"
              error-message-align="right"
              label-width="130"
              :border="false"
              :rules="[{ required: true, message: '请选择点位是否正常经营' }]"
              required
            >
              <template #input>
                <van-radio-group v-model="ruleForm.operation" direction="horizontal" checked-color="#4CA786">
                  <van-radio :name="1">是</van-radio>
                  <van-radio :name="0">否</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              name="isRubbish"
              label="点位是否产生垃圾"
              input-align="right"
              error-message-align="right"
              label-width="130"
              :border="false"
              :rules="[{ required: true, message: '请选择点位是否产生垃圾' }]"
              required
              v-if="ruleForm.operation === 1"
            >
              <template #input>
                <van-radio-group v-model="ruleForm.isRubbish" direction="horizontal" checked-color="#4CA786">
                  <van-radio :name="1">是</van-radio>
                  <van-radio :name="0">否</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field label="废物类型" label-width="100%" :border="false" required />
            <van-field
              :name="item.key"
              :label="item.name + '重量（kg）'"
              input-align="right"
              error-message-align="right"
              label-width="150px"
              required
              :rules="[{ required: true, message: `请填写${item.name}` }]"
              :border="false"
              v-for="(item, index) in trashList"
              :key="index"
            >
              <template #input>
                <van-stepper
                  v-model="ruleForm[item.key]"
                  min="0"
                  max="99999999"
                  :decimal-length="2"
                  default-value=""
                  input-width="80px"
                />
              </template>
            </van-field>
            <van-field
              name="baggingMethod"
              label="垃圾存储方式"
              input-align="right"
              error-message-align="right"
              label-width="120px"
              :border="false"
              :rules="[{ required: true, message: '请选择垃圾存储方式' }]"
              required
            >
              <template #input>
                <van-radio-group
                  v-model="ruleForm.baggingMethod"
                  direction="horizontal"
                  checked-color="#4CA786"
                  v-if="waybillType === 2 || !ruleForm.id"
                >
                  <van-radio :name="index" v-for="(item, index) in BARRELS_BAGS" :key="index">{{ item }}</van-radio>
                </van-radio-group>
                <van-radio-group v-model="ruleForm.baggingMethod" direction="horizontal" checked-color="#4CA786" v-else>
                  <van-radio :name="ruleForm.baggingMethod">{{ BARRELS_BAGS[ruleForm.baggingMethod] }}</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </main>
          <div class="border-line"></div>
          <main class="create-main" v-if="ruleForm.operation === 1 && ruleForm.isRubbish === 1">
            <baseTitle title="点位状态"></baseTitle>
            <van-field
              name="isClear"
              label="是否清空"
              input-align="right"
              error-message-align="right"
              label-width="120px"
              :border="false"
              :rules="[{ required: true, message: '请选择是否清空' }]"
              required
            >
              <template #input>
                <van-radio-group v-model="ruleForm.isClear" direction="horizontal" checked-color="#4CA786">
                  <van-radio :name="1">是</van-radio>
                  <van-radio :name="0">否</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              name="residueRubbish"
              label="点位剩余废物重量（kg）"
              input-align="right"
              error-message-align="right"
              label-width="150px"
              :border="false"
              :rules="[{ required: true, message: '请填写点位剩余废物重量' }]"
              required
              v-if="ruleForm.isClear == 0"
            >
              <template #input>
                <van-stepper
                  v-model="ruleForm.residueRubbish"
                  min="0"
                  max="99999999"
                  :decimal-length="2"
                  default-value=""
                  input-width="80px"
                />
              </template>
            </van-field>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="图片信息"></baseTitle>
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <span class="label-title">收运照片</span>
                  <span class="label-text">(请拍摄收运完成后的图片/点位剩余医疗废物照片)</span>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.picture"
              :isRequired="true"
              :capture="'camera'"
              formId="picture"
              formPlaceHolder="请拍摄收运完成后的图片/点位剩余医疗废物照片"
            ></imageUpload>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <pointAdjust
      v-if="showPointAdjust"
      :lng="ruleForm.userLongitude"
      :lat="ruleForm.userLatitude"
      :isSave="isSave"
      @closePointAdjust="closePointAdjust"
      @savePointAdjust="savePointAdjust"
    ></pointAdjust>
    <van-popup v-model="showPointPicker" position="bottom">
      <div class="popup-container">
        <point @closePopup="showPointPicker = false" @selectItem="selectItem" @confirmPoint="confirmPoint"></point>
      </div>
    </van-popup>
    <mapContainer @initMap="initMap" class="map-box" mapId="current-position"></mapContainer>
    <van-popup v-model="showRegionPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="regionOptions"
        :default-index="defaultRegionIndex"
        @confirm="onRegionConfirm"
        @cancel="showRegionPicker = false"
      />
    </van-popup>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="POINT_TYPE"
        :default-index="defaultTypeIndex"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFunByParams } from "@/api/base";
  import { BARRELS_BAGS, POINT_TYPE } from "@/enums";
  import imageUpload from "@/components/imageUpload";
  import { floatAdd, isEmpty } from "@/utils";
  import baseTitle from "@/components/baseTitle";
  import mapContainer from "@/components/mapContainer";
  import pointAdjust from "./components/pointAdjust";
  import point from "./components/point.vue";
  export default {
    components: {
      imageUpload,
      baseTitle,
      mapContainer,
      pointAdjust,
      point,
    },
    data() {
      return {
        BARRELS_BAGS,
        POINT_TYPE,
        submitFormThrottling: () => {},
        apis: {
          create: "/api/waybill/waybillDetail/addWaybillDetail",
          update: "/api/waybill/waybillDetail/completeWaybillDetail",
          regionList: "/api/region/regionList",
        },
        ruleForm: {
          id: "",
          productionUnit: "", //产废单位名称
          productionUnitOperator: "", //产废单位经办人
          productionUnitOperatorPhone: "", //经办人联系方式
          address: "", //产废单位地址
          operation: 1, //点位是否正常经营
          isRubbish: 1, //点位是否产生垃圾
          infectiousWaste: "", //感染性废物
          damagingWaste: "", //损伤性废物
          pharmaceuticalWaste: "", //药物性废物
          pathologicalWaste: "", //病理性废物
          chemicalWaste: "", //化学性废物
          sludge: "", //污泥
          baggingMethod: "", //垃圾存储方式
          isClear: "", //是否清空
          residueRubbish: "", //点位剩余废物重量
          rubbishTotal: 0, //废物总量
          picture: [],
          userLongitude: "", //经度
          userLatitude: "", //纬度
          districtId: "", //省市区
          type: "", //类型
        },
        trashList: [
          {
            name: "感染性",
            value: "",
            key: "infectiousWaste",
          },
          {
            name: "损伤性",
            value: "",
            key: "damagingWaste",
          },
          {
            name: "药物性",
            value: "",
            key: "pharmaceuticalWaste",
          },
          {
            name: "病理性",
            value: "",
            key: "pathologicalWaste",
          },
          {
            name: "化学性",
            value: "",
            key: "chemicalWaste",
          },
          {
            name: "感染性一污泥",
            value: "",
            key: "sludge",
          },
        ],
        waybillType: 2, //收运方式
        showRouteTaskPicker: false,
        routeTaskList: [],
        showPointAdjust: false,
        map: "",
        isSave: false,
        showPointPicker: false,
        showRegionPicker: false,
        showTypePicker: false,
        regionOptions: [],
        defaultRegionIndex: 0,
        defaultTypeIndex: 0,
      };
    },
    computed: {
      regionObj() {
        let obj = {};
        this.regionOptions.forEach((item) => {
          obj[item.value] = item.text;
        });
        return obj;
      },
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
      let { id, productionUnit, productionUnitOperator, waybillType, address } = this.$route.query;
      this.ruleForm.id = id;
      this.ruleForm.productionUnit = productionUnit || "";
      this.ruleForm.productionUnitOperator = productionUnitOperator || "";
      this.ruleForm.address = address || "";
      if (id) {
        this.waybillType = isEmpty(waybillType) ? 2 : Number(waybillType);
        this.ruleForm.baggingMethod = this.waybillType === 2 ? "" : this.waybillType;
      }
      if (!this.ruleForm.id) {
        this.getRegionList();
      }
    },
    methods: {
      // 初始化地图
      initMap(map) {
        if (map) {
          this.map = map;
          window.AMap.plugin("AMap.Geolocation", () => {
            let geolocation = new window.AMap.Geolocation({
              enableHighAccuracy: true, //是否使用高精度定位，默认:true
              timeout: 10000, //超过10秒后停止定位，默认：无穷大
              maximumAge: 0, //定位结果缓存0毫秒，默认：0
              convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
              showButton: true, //显示定位按钮，默认：true
              buttonPosition: "LB", //定位按钮停靠位置，默认：'LB'，左下角
              showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
              showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
              panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
              zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
            });
            geolocation.getCurrentPosition((status, result) => {
              if (status == "complete") {
                this.ruleForm.userLongitude = result.position.lng;
                this.ruleForm.userLatitude = result.position.lat;
              }
            });
          });
        }
      },
      // 获取省市区列表
      async getRegionList() {
        let res = await getInfoApiFunByParams({ pid: "440100000000" }, this.apis.regionList);
        if (res.success) {
          this.regionOptions = res.data.map((item) => {
            return {
              value: item.code,
              text: item.name,
            };
          });
        }
      },
      // 省市区选择事件
      onRegionConfirm(item) {
        this.ruleForm.districtId = item.value;
        this.showRegionPicker = false;
      },
      // 点位类型选择事件
      onTypeConfirm(_, index) {
        this.ruleForm.type = index;
        this.showTypePicker = false;
      },
      // 上一步
      backStep() {
        let index = this.headerOptions.findIndex((list) => list.id === this.step);
        this.step = this.headerOptions[index - 1].id;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = JSON.parse(JSON.stringify(this.ruleForm));
            let wasteKeyList = [
              "infectiousWaste",
              "damagingWaste",
              "pharmaceuticalWaste",
              "pathologicalWaste",
              "chemicalWaste",
              "sludge",
            ];
            wasteKeyList.forEach((list) => {
              params.rubbishTotal = floatAdd(params.rubbishTotal, params[list]);
            });
            if (params.picture.length > 0) {
              params.picture = JSON.stringify(params.picture);
            } else {
              params.picture = "";
            }
            if (!params.id) {
              delete params.id;
              params.waybillId = this.$route.query.waybillId;
            } else {
              delete params.productionUnitOperatorPhone;
              delete params.waybillId;
            }
            if (params.operation != 1) {
              delete params.isRubbish;
            }
            delete params.waybillName;
            try {
              let res = params.id
                ? await createApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: `${this.ruleForm.id ? "填写电子收运单成功" : "增加点位收运任务成功"}`,
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      // 关闭弹窗
      closePointAdjust() {
        this.showPointAdjust = false;
      },
      // 保存地址、经纬度
      savePointAdjust(item) {
        if (!this.isSave) {
          this.isSave = true;
        }
        if (item) {
          this.ruleForm.address = item.address;
          this.ruleForm.userLongitude = item.location.lng;
          this.ruleForm.userLatitude = item.location.lat;
        }
        this.showPointAdjust = false;
      },
      // 选择点位
      selectItem(item) {
        this.ruleForm.productionUnit = item.name;
        this.ruleForm.productionUnitOperator = item.contact;
        this.ruleForm.productionUnitOperatorPhone = item.contactPhone;
        this.ruleForm.address = item.address;
        this.ruleForm.pickupPointId = item.id;
        this.ruleForm.districtId = item.districtId;
        this.ruleForm.type = item.type;
        this.ruleForm.baggingMethod = item.baggingMethod;
        let districtIndex = this.regionOptions.findIndex((o) => o.value === item.districtId);
        if (districtIndex > 0) {
          this.defaultRegionIndex = districtIndex;
        }
        let typeIndex = this.POINT_TYPE.findIndex((_, index) => index === item.type);
        if (typeIndex > 0) {
          this.defaultTypeIndex = typeIndex;
        }
        this.showPointPicker = false;
      },
      // 确认输入框内容为点位名称
      confirmPoint(value) {
        this.ruleForm.productionUnit = value;
        this.ruleForm.productionUnitOperator = "";
        this.ruleForm.productionUnitOperatorPhone = "";
        this.ruleForm.address = "";
        this.ruleForm.pickupPointId = "";
        this.ruleForm.baggingMethod = "";
        this.showPointPicker = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .question-radio,
  .question-checkbox {
    margin-bottom: 10px;
  }
  .question-text {
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .location-icon {
    font-size: 16px;
    color: #909399;
  }
  .map-box {
    position: fixed;
    top: -9999px;
    left: -9999px;
    visibility: hidden;
  }
  .popup-container {
    width: 100vw;
    height: 80vh;
  }
  ::v-deep .van-form {
    flex: 1;
    overflow: hidden;
  }
</style>
