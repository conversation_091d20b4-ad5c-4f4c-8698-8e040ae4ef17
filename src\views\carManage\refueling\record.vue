<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicleFuel/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "driverName",
                value: "驾驶司机",
              },
            ],
          },
          {
            title: "加油信息",
            recordFieldArr: [
              {
                key: "refuelTypeName",
                value: "加油类型",
              },
              {
                key: "fuelQuantity",
                value: "加油/充电量",
                unit: "L/kwh",
              },
              {
                key: "fuelTime",
                value: "加油/充电量日期",
              },
              {
                key: "amount",
                value: "加油/充电量金额",
                unit: "元",
              },
              {
                key: "fuelPlace",
                value: "加油/充电地点",
              },
              {
                key: "vehicleFileList",
                value: "凭证",
                isImage: true,
              },
            ],
          },
          {
            title: "里程信息",
            recordFieldArr: [
              {
                key: "totalMileage",
                value: "行驶总里程",
                unit: "KM",
              },
              {
                key: "dashBoardFileList",
                value: "车辆仪表里程照片",
                isImage: true,
              },
            ],
          },
        ],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped></style>
