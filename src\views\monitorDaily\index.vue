<template>
  <div class="operateOverview-wrapper">
    <TopBar @updateActive="updateActive" :list="['①基础信息', '②今日收运详情', '③趋势分析']"></TopBar>
    <Info v-if="active == 1"></Info>
    <Detail v-if="active == 2"></Detail>
    <Charts v-if="active == 3"></Charts>
  </div>
</template>
<script>
  import TopBar from "@/views/overview/operateOverview/TopBar";
  import Info from "./Info";
  import Detail from "./Detail";
  import Charts from "./Charts";
  export default {
    components: { TopBar, Info, Detail, Charts },

    props: {},
    data() {
      return {
        active: 1,
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      updateActive(active) {
        this.active = active;
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .operateOverview-wrapper {
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
  }
</style>
