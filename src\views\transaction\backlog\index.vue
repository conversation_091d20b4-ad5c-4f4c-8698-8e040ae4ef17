<template>
  <div class="page-container">
    <header class="page-header">
      <div class="tab-box">
        <!-- 通过css behind 控制显示隐藏箭头 -->
        <div
          @click="handleTabBoxClick(item.id)"
          :class="item.id === activeTab ? 'active' : ''"
          v-for="item in tabList"
          :key="item.id"
          class="tab-item"
          >{{ item.name }}
          <span
            :class="item.id === activeTab ? '' : 'behind'"
            v-if="isExpend"
            class="sctmp-iconfont icon-ic_shang filter-icon"
          ></span>
          <span
            :class="item.id === activeTab ? '' : 'behind'"
            v-else
            class="sctmp-iconfont icon-ic_xia filter-icon"
          ></span>
        </div>
      </div>
      <div v-if="isExpend" class="filter-box">
        <ul class="grid-list">
          <li
            class="grid-item"
            :class="{ active: activeModule === index }"
            v-for="(item, index) in gridList"
            :key="index"
            @click="toggleGrid(index)"
            >{{ item.name }}</li
          >
        </ul>
      </div>
    </header>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="list-item-left">
                <div class="item-title">{{ item.todoTask }}</div>
                <div class="item-text">上报时间：{{ item.createTime }}</div>
                <div class="flex-item">
                  <div class="item-text">截止日期：{{ item.deadline }} </div>
                  <div class="item-status">{{ BACKLOG_STATUS[item.status] }}</div>
                </div>
                <template v-if="activeTab === 1">
                  <div class="item-text">处理人：{{ item.handlePerson }}</div>
                  <div class="item-text">处理时间：{{ item.completionTime }}</div>
                </template>
              </div>
              <div class="list-item-right" v-if="activeTab === 0" @click="itemClick(item)">
                <div>去处理</div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>
<script>
  import { getListPageApiFun, getInfoApiFun } from "@/api/base";
  import { BACKLOG_STATUS } from "@/enums";
  export default {
    components: {},
    props: {},
    data() {
      return {
        BACKLOG_STATUS,
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        tableList: [],
        filterForm: {},
        listPageApi: "/api/task/listPage",
        activeModule: "",
        isExpend: false,
        activeTab: 0,
        tabList: [
          {
            id: 0,
            name: "未处理",
          },
          {
            id: 1,
            name: "已处理",
          },
        ],
        gridList: [
          { name: "收运管理", codeList: ["20", "21", "22", "23", "24", "28"] },
          { name: "事件监测", codeList: ["60"] },
          { name: "安全管理", codeList: ["40", "41"] },
          { name: "车辆管理", codeList: ["00", "01", "02", "03", "04", "05", "06"] },
          { name: "证件管理", codeList: ["10", "11", "12", "13", "14"] },
          { name: "绩效考核", codeList: ["50", "51"] },
          { name: "客服管理", codeList: ["30"] },
        ],
        apis: {
          formRecord: "/api/assess/form/get/",
        },
      };
    },
    computed: {},
    created() {},
    mounted() {
      this.onLoad();
    },
    methods: {
      async itemClick(item) {
        let baseParams = { id: item.businessId, createTime: item.createTime, deadline: item.deadline };
        switch (Number(item.code)) {
          case 23:
            this.$commonSkip("electronicWaybillAudit");
            break;
          case 24:
            this.$commonSkip("shiftAuditCreate", baseParams);
            break;
          case 28:
            this.$commonSkip("overtimeApprovalRecord", baseParams);
            break;
          case 50:
            this.$commonSkip("examineMark");
            break;
          case 51:
            getInfoApiFun(item.businessId, this.apis.formRecord).then((res) => {
              this.$commonSkip("examineConfirmCreate", res.data);
            });
            break;
          case 20:
          case 21:
          case 60:
            this.$commonSkip("backlogRecord", {
              code: item.code,
              ...baseParams,
            });
            break;
        }
      },
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          type: 0,
          status: this.activeTab,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          ...this.filterForm,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas;
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 切换模块
      toggleGrid(index) {
        // 当前tab与点击的tab一致，则取消
        if (this.activeModule === index) {
          this.activeModule = "";
        } else {
          this.activeModule = index;
        }
        this.searchList();
      },
      // 点击tab栏
      handleTabBoxClick(activeTab) {
        // 当前tab与点击的tab一致，则展开筛选条件
        if (this.activeTab === activeTab) {
          this.isExpend = !this.isExpend;
        } else {
          // 当切换tab，清除筛选条件，不展开搜索框
          this.isExpend = false;
          this.activeTab = activeTab;
          this.activeModule = "";
          this.onRefresh();
        }
      },
      // 查询
      searchList() {
        this.filterForm.module = this.activeModule;
        if (this.activeModule !== "") {
          let item = this.gridList.filter((_, index) => index === this.activeModule)[0];
          this.filterForm.codeList = item.codeList;
        } else {
          this.filterForm.codeList = [];
        }
        this.onRefresh();
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .flex-item {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .item-status {
    margin-top: 8px;
    margin-left: 4px;
    background: #ff7d00;
    border-radius: 6px 6px 6px 1px;
    padding: 0 3px;
    padding-top: 2px;
    text-align: center;
    color: #fff;
    line-height: 17px;
    font-size: 11px;
    font-weight: 400;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    .list-item-left {
      flex: 1;
      overflow: hidden;
    }
    .list-item-right {
      font-size: 14px;
      color: #4ca786;
      line-height: 16px;
      margin-left: 10px;
      text-align: right;
    }
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    min-height: 54px;
    background: #fff;
  }
  .tab-box {
    min-height: 54px;
    background: #fff;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .tab-item {
    width: 50%;
    text-align: center;
    color: #5c6663;
    font-weight: 400;
    font-size: 14px;
  }
  .active {
    color: #4ca786;
  }
  .behind {
    color: transparent;
  }
  .filter-box {
    padding: 12px 0 20px 0;
  }
  .grid-list {
    display: grid;
    grid-gap: 12px;
    grid-template-columns: repeat(4, 1fr);
    padding: 0 12px;
    .grid-item {
      font-size: 12px;
      padding: 8px 12px;
      border: 1px solid #dce0df;
      border-radius: 14px;
      text-align: center;
      &.active {
        color: #4ca786;
        border-color: #4ca786;
      }
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
</style>
