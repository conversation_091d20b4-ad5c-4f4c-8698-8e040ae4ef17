{"name": "sctmp_h5", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "dev": "vue-cli-service build --mode development", "test": "vue-cli-service build --mode test", "prod": "vue-cli-service build --mode production", "pre": "vue-cli-service build --mode pre", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{js,jsx,vue}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/ --allow-empty-input", "lint:lint-staged": "lint-staged", "prepare": "husky install"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "ali-oss": "^6.19.0", "axios": "^0.24.0", "core-js": "^3.6.5", "echarts": "^5.5.1", "element-ui": "^2.15.14", "moment": "^2.29.4", "nanoid": "^4.0.2", "qrcode": "^1.5.4", "qs": "^6.10.2", "signature_pad": "^3.0.0-beta.4", "sm-crypto": "^0.3.13", "vant": "^2.12.54", "vconsole": "^3.9.1", "vue": "^2.6.11"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "filemanager-webpack-plugin": "^8.0.0", "husky": "^8.0.3", "less": "^3.12.2", "less-loader": "^7.0.1", "lint-staged": "^11.1.2", "postcss": "^8.4.12", "postcss-html": "^1.4.1", "postcss-preset-env": "^8.0.1", "postcss-px-to-viewport": "^1.1.1", "postcss-selector-namespace": "^3.0.1", "prettier": "^2.8.4", "sass": "^1.58.3", "sass-loader": "^13.2.0", "style-resources-loader": "^1.5.0", "stylelint": "^14.7.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-eslint-parser": "^8.3.0", "vue-template-compiler": "^2.6.14"}, "gitHooks": {"pre-commit": "lint-staged"}}