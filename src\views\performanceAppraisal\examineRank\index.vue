<template>
  <div class="page-container">
    <header class="page-header">
      <div class="header-input">
        <van-field
          v-model="assessSchemeName"
          label=""
          placeholder="请输入考核方案名称"
          right-icon="search"
          clearable
          @change="onRefresh"
          @clear="onRefresh"
        >
          <template #right-icon>
            <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
          </template>
        </van-field>
      </div>
      <div class="filter-button" @click="showPop = !showPop">
        <span class="filter-title">筛选</span>
        <span class="sctmp-iconfont icon-ic_xia filter-icon" v-show="!showPop"></span>
        <span class="sctmp-iconfont icon-ic_shang filter-icon" v-show="showPop"></span>
      </div>
    </header>
    <div v-if="showPop" position="top">
      <div class="filter-box" v-if="filterList.length > 0">
        <div v-for="(filterItem, filterIndex) in filterList" :key="filterIndex">
          <component
            :ref="filterItem.type"
            :is="filterItem.type"
            v-model="filterForm[filterItem.key]"
            :filterItem="filterItem"
          />
        </div>
        <div class="is-flex">
          <div class="flex-1"></div>
          <div class="sort-box flex-h" @click="toggleSort">
            <div class="sort-name">{{ totalScoreSort === 1 ? "倒序" : "正序" }}</div>
            <div class="sort-icon">
              <span class="el-icon-caret-top ascending-icon" :class="{ active: totalScoreSort === 0 }"></span>
              <span class="el-icon-caret-bottom descending-icon" :class="{ active: totalScoreSort === 1 }"></span>
            </div>
          </div>
        </div>
        <div class="filter-bottom">
          <div class="reset-button" @click="resetList">重置</div>
          <div class="search-button" @click="searchList">查询</div>
        </div>
      </div>
    </div>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="item-title"
                >考核{{ item.period === 1 ? "年度" : "月度" }}：{{
                  item.period === 1 ? item.year : `${item.year}-${item.month >= 10 ? item.month : "0" + item.month}`
                }}</div
              >
              <div class="item-text">被考核人：{{ item.fullName }}</div>
              <div class="item-text">考核方案：{{ item.assessSchemeName }}</div>
              <div class="item-text">考核周期：{{ EXAMINE_PERIOD[item.period] }}</div>
              <div class="item-text">考核总分：{{ item.totalScore }}</div>
              <div class="item-text">考核结果：{{ item.assessGradeName }}</div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { EXAMINE_PERIOD, EXAMINE_FLOW, PERFORMANCE_RULE, ASSESSMENT_FLOWS } from "@/enums";
  import components from "@/components/pageList/componentImport";
  import moment from "moment";
  export default {
    components: components,
    data() {
      return {
        showPop: false,
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        listPageApi: "/api/assess/form/listPage",
        tableList: [],
        filterForm: {},
        filterList: [
          {
            type: "YearMonth",
            key: "yearOrMonth",
            value: "考核月度/年度",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
        EXAMINE_PERIOD,
        EXAMINE_FLOW,
        ASSESSMENT_FLOWS,
        PERFORMANCE_RULE,
        totalScoreSort: 1,
        assessSchemeName: "",
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 切换筛选弹窗
      togglePop() {
        this.showPop = !this.showPop;
      },
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          verifyStatus: 2,
          totalScoreSort: this.totalScoreSort,
          assessSchemeName: this.assessSchemeName,
          ...this.filterForm,
        };
        if (this.filterForm.yearOrMonth) {
          params.yearOrMonth = moment(params.yearOrMonth).format("YYYY/MM");
        }
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas;
            dataList.forEach((list) => {
              for (let key in list) {
                list[key] = list[key] || list[key] === 0 ? list[key] : "-";
              }
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 重置
      resetList() {
        this.assessSchemeName = "";
        this.filterForm = {};
        this.onRefresh();
        this.togglePop();
      },
      // 查询
      searchList() {
        this.onRefresh();
        this.togglePop();
      },
      // 切换排序
      toggleSort() {
        this.totalScoreSort = this.totalScoreSort === 1 ? 0 : 1;
        this.onRefresh();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f7faf9;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
    display: flex;
  }
  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0 10px 10px;
    border-radius: 4px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 20px;
      margin-right: 4px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  .filter-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    padding-bottom: 10px;
    background: #fff;
  }
  .reset-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #dce0df;
    border-radius: 20px;
  }
  .search-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #4ca786;
    border-radius: 20px;
    margin-left: 8px;
  }
  .sort-box {
    margin-right: 16px;
    font-size: 14px;
    .sort-name {
      color: #4ca786;
      margin-right: 4px;
    }
    .sort-icon {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #c0c4cc;
      .ascending-icon {
        transform: translateY(3px);
        &.active {
          color: #4ca786;
        }
      }
      .descending-icon {
        transform: translateY(-3px);
        &.active {
          color: #4ca786;
        }
      }
    }
  }
  .is-flex {
    background: #fff;
  }
  .header-input {
    flex: 1;
    overflow: hidden;
    ::v-deep .van-field {
      background: #f7faf9;
    }
  }
</style>
