<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              :value="createTime"
              label="上报时间"
              placeholder="请输入上报时间"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="deadline"
              label="截止日期"
              placeholder="请输入截止日期"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="baseForm.rubbishTotal"
              label="废物总量（kg）"
              placeholder="请输入废物总量（kg）"
              input-align="right"
              :border="false"
              readonly
              label-width="120"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="业务调整信息"></baseTitle>
            <van-field
              :value="ruleForm.defaultVehiclePlateNumber"
              name="defaultVehiclePlateNumber"
              label="收运车辆"
              placeholder="请选择收运车辆"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultVehiclePlateNumber"
              required
              @click="showPlateNumberPicker = true"
            />
            <van-field
              :value="ruleForm.defaultDriverDossierName"
              name="defaultDriverDossierId"
              label="司机"
              placeholder="请选择司机"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.defaultDriverDossierId"
              required
              @click="showDriverPicker = true"
            />
            <van-field
              :value="ruleForm.defaultDriverDossierPhone"
              name="defaultDriverDossierPhone"
              label="司机联系方式"
              placeholder="请输入司机联系方式"
              input-align="right"
              error-message-align="right"
              required
              maxlength="11"
              readonly
              :border="false"
              :rules="rules.defaultDriverDossierPhone"
            />
            <van-field
              :value="ruleForm.supercargoDossierOneName"
              label="押运工1"
              placeholder="请选择押运工1"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierOnePicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierOnePhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoName"
              label="押运工2"
              placeholder="请选择押运工2"
              readonly
              input-align="right"
              is-link
              :border="false"
              @click="showDossierTwoPicker = true"
            />
            <van-field
              :value="ruleForm.supercargoDossierTwoPhone"
              label="押运工联系方式"
              placeholder="请输入押运工联系方式"
              label-width="130"
              input-align="right"
              maxlength="11"
              readonly
              :border="false"
            />
            <van-field
              label="收运任务"
              placeholder=""
              input-align="right"
              :border="false"
              readonly
              label-width="100%"
            />
            <ul class="list" v-if="tableData.length > 0">
              <li class="list-item" v-for="(item, index) in tableData" :key="index">
                <div class="item-content">
                  <div class="item-left">
                    <div class="item-title">产废单位名称：{{ item.productionUnit }}</div>
                    <div class="item-text">产废单位经办人：{{ item.productionUnitOperator }}</div>
                    <div class="item-text">收运状态：{{ RECEIVING_CONDITION[item.waybillStatus] }}</div>
                    <van-field
                      :value="POINT_TASK_TYPE[ruleForm.detailType]"
                      name="detailType"
                      label="任务类型"
                      placeholder="请选择任务类型"
                      readonly
                      input-align="right"
                      error-message-align="right"
                      is-link
                      :border="false"
                      :rules="rules.detailType"
                      required
                      @click="showTypePicker = true"
                      class="detail-type"
                    />
                  </div>
                </div>
              </li>
            </ul>
            <van-empty description="暂无数据" v-else />
            <van-field
              label="备注信息"
              placeholder=""
              input-align="right"
              :border="false"
              readonly
              label-width="100%"
            />
            <van-field
              v-model="ruleForm.memo"
              rows="4"
              maxlength="500"
              label=""
              type="textarea"
              placeholder="请输入备注信息"
              show-word-limit
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >完成</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showPlateNumberPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="finalName"
        :columns="carOptions"
        :default-index="defaultPlateNumberIndex"
        @confirm="onCarConfirm"
        @cancel="showPlateNumberPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDriverPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="driverOptions"
        :default-index="defaultDriverIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'defaultDriverDossier', 'showDriverPicker')"
        @cancel="showDriverPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDossierOnePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierOneIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierOne', 'showDossierOnePicker')"
        @cancel="showDossierOnePicker = false"
      />
    </van-popup>
    <van-popup v-model="showDossierTwoPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierTwoIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierTwo', 'showDossierTwoPicker')"
        @cancel="showDossierTwoPicker = false"
      />
    </van-popup>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker show-toolbar :columns="POINT_TASK_TYPE" @confirm="onTypeConfirm" @cancel="showTypePicker = false" />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import componentMinxins from "./mixins";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, RECEIVING_CONDITION, POINT_TASK_TYPE } from "@/enums";
  export default {
    mixins: [componentMinxins],
    components: {
      baseTitle,
    },
    data() {
      return {
        submitFormThrottling: () => {},
        apis: {
          info: "/api/waybill/waybillDetail/listPage",
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          create: "/api/waybill/commissionTemporaryUpdate",
        },
        baseForm: {}, //业务基础信息
        ruleForm: {
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //司机
          defaultDriverDossierName: "", //司机
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1
          supercargoDossierOneName: "", //押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //押运工2
          supercargoDossierTwoName: "", //押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          memo: "",
          detailType: "", //任务类型
        },
        rules: {
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式" }],
          detailType: [{ required: true, message: "请选择任务类型" }],
        },
        carOptions: [],
        showPlateNumberPicker: false,
        defaultPlateNumberIndex: 0,
        driverOptions: [],
        showDriverPicker: false,
        defaultDriverIndex: 0,
        shipWorkerOptions: [],
        showDossierOnePicker: false,
        defaultDossierOneIndex: 0,
        showDossierTwoPicker: false,
        defaultDossierTwoIndex: 0,
        tableData: [],
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        RECEIVING_CONDITION,
        POINT_TASK_TYPE,
        showTypePicker: false,
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    async mounted() {
      await this.getOptions();
      this.getRecord();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data.map((item) => {
          return {
            ...item,
            finalName: item.plateNumber + (item.isFree === 1 ? "" : "（空闲）"),
          };
        });
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取详情
      async getRecord() {
        let res = await createApiFun({ pageNo: 1, pageSize: 1, waybillId: this.id }, this.apis.info);
        if (res.success) {
          this.tableData = res.data.datas;
          this.baseForm = res.data.datas[0];
        }
      },
      // 选择收运车辆
      async onCarConfirm(item) {
        if (this.ruleForm.defaultVehiclePlateNumber == item.plateNumber) {
          this.showPlateNumberPicker = false;
          return;
        }
        let promiseList = [
          createApiFun({ userIdentity: "3", plateNumber: item.plateNumber }, this.apis.driverAndWorkerInfo),
          createApiFun({ userIdentity: "4", plateNumber: item.plateNumber }, this.apis.driverAndWorkerInfo),
        ];
        let res = await Promise.all(promiseList);
        let driverInfo = res[0].data;
        let workerInfo = res[1].data;
        if (driverInfo) {
          this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
          this.ruleForm.defaultDriverDossierName = driverInfo.fullName;
          this.ruleForm.defaultDriverDossierPhone = driverInfo.phone ? this.$sm2Decrypt(driverInfo.phone) : "";
          let defaultDriverIndex = this.driverOptions.findIndex((item) => item.lgUnionId == driverInfo.lgUnionId);
          if (defaultDriverIndex >= 0) {
            this.defaultDriverIndex = defaultDriverIndex;
          }
        } else {
          this.ruleForm.defaultDriverDossierId = "";
          this.ruleForm.defaultDriverDossierName = "";
          this.ruleForm.defaultDriverDossierPhone = "";
        }
        if (workerInfo) {
          this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
          this.ruleForm.supercargoDossierOneName = workerInfo.fullName;
          this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          let defaultDossierOneIndex = this.shipWorkerOptions.findIndex(
            (item) => item.lgUnionId == workerInfo.lgUnionId,
          );
          if (defaultDossierOneIndex >= 0) {
            this.defaultDossierOneIndex = defaultDossierOneIndex;
          }
        } else {
          this.ruleForm.supercargoDossierOneId = "";
          this.ruleForm.supercargoDossierOneName = "";
          this.ruleForm.supercargoDossierOnePhone = "";
        }
        this.ruleForm.defaultVehiclePlateNumber = item.plateNumber;
        this.showPlateNumberPicker = false;
      },
      // 选择驾驶司机、押运工
      onDriverShipWorkerConfirm(item, field, pickerPopup) {
        this.ruleForm[`${field}Id`] = item.lgUnionId;
        this.ruleForm[`${field}Name`] = item.fullName;
        this.ruleForm[`${field}Phone`] = item.phone;
        this[pickerPopup] = false;
      },
      // 选择任务类型
      onTypeConfirm(_, index) {
        this.ruleForm.detailType = index;
        this.showTypePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun({ ...this.ruleForm, id: this.id }, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "操作成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-flex {
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .warning-text {
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      text-align: center;
      margin-top: 10px;
    }
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .tips-icon {
    font-size: 50px;
    color: #ff7d00;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .list {
    padding-top: 4px;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
  ::v-deep .detail-type {
    padding: 0;
    padding-left: 5px;
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  ::v-deep .detail-type::before {
    left: 0;
  }
  ::v-deep .detail-type .van-cell__right-icon {
    font-size: 14px;
    height: 0;
    line-height: 13px;
  }
</style>
