//混入组件数据
export default {
  props: {
    value: {
      default: null,
    },
    filterItem: {
      type: Object,
      default: () => {},
    },
    labelWidth: {
      type: String,
      default: "120",
    },
  },
  data() {
    return {};
  },
  computed: {
    _value: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
};
