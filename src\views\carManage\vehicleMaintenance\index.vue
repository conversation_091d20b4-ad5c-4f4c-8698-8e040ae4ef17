<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入车牌号/经办人名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { MAINTENANCE_TYPE } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "operatorName",
            value: "经办人",
          },
          {
            key: "type",
            value: "维保类型",
            enums: MAINTENANCE_TYPE,
          },
          {
            key: "recentMaintenanceTime",
            value: "最近维保日期",
          },
        ],
        listPageApi: "/api/vehicleMaintenance/listPage",
        filterList: [
          {
            type: "Date",
            key: "recentMaintenanceBeginTime",
            value: "最近维保开始日期",
            minDate: new Date(moment().subtract(100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(),
          },
          {
            type: "Date",
            key: "recentMaintenanceEndTime",
            value: "最近维保结束日期",
            minDate: new Date(moment().subtract(100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("vehicleMaintenanceRecord", { id: item.id, type: item.type });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
