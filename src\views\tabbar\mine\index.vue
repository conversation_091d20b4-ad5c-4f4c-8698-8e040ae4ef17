<template>
  <div class="page-container">
    <div class="page-main">
      <div class="main-title">我的</div>
      <div class="mine-main">
        <div class="user-card">
          <div class="user-avatar flex-h">
            <van-image
              class="avatar-box"
              :src="userInfo.avatar.url"
              fit="fill"
              v-if="userInfo && userInfo.avatar && userInfo.avatar.url"
            />
            <van-image class="avatar-box" :src="defaultAvatar" fit="fill" v-else />
            <div class="user-name">{{ userInfo ? userInfo.fullName : "请登录/注册" }}</div>
          </div>
          <div class="user-phone flex-h" v-if="userInfo && userInfo.phone">
            <div class="sctmp-iconfont icon-ic_dianhua icon-box"></div>
            <div class="icon-value">{{ userInfo.maskPhone }}</div>
          </div>
          <div class="user-company flex-h" v-if="userInfo && userInfo.company">
            <div class="sctmp-iconfont icon-ic_gongsi icon-box"></div>
            <div class="icon-value">{{ userInfo.companyName }}</div>
          </div>
          <van-image class="card-image" :src="mineCar" fit="fill" />
        </div>
        <div
          class="user-tip flex-h"
          v-if="userInfo && !userInfo.fullName && !(userInfo.userIdentity || userInfo.userIdentity === 0)"
        >
          <span class="sctmp-iconfont icon-ic_tishi tip-icon"></span>
          <div class="tip-label">个人信息待完善</div>
          <div class="tip-title">请完善个人信息以便使用完整服务</div>
        </div>
        <div class="info-box flex-h" @click="updateUserInfo" v-if="userInfo">
          <van-image class="info-image" :src="iconDriver" fit="fill" />
          <div class="info-title flex-1">个人信息</div>
          <van-icon name="arrow" color="#C4CCC9" />
        </div>
        <div class="info-box flex-h" @click="changeRole" v-if="userInfo">
          <van-image class="info-image" :src="iconDriver" fit="fill" />
          <div class="info-title flex-1">角色切换</div>
          <van-icon name="arrow" color="#C4CCC9" />
        </div>
      </div>
      <div class="mine-footer" @click="handleFooterClick">{{ userInfo ? "退出登录" : "登录/注册" }}</div>
    </div>
    <tabbar active="mine"></tabbar>
  </div>
</template>

<script>
  import tabbar from "@/components/tabbar";
  import { createApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  export default {
    components: {
      tabbar,
    },
    data() {
      return {
        defaultAvatar: require("@/assets/images/default_avatar.png"),
        mineCar: require("@/assets/images/mine_car.png"),
        iconDriver: require("@/assets/images/icon_driver.png"),
        userInfo: "",
      };
    },
    created() {
      this.initData();
    },
    methods: {
      initData() {
        let userInfo = getUserInfo();
        if (userInfo) {
          try {
            userInfo.avatar = JSON.parse(userInfo.avatar);
          } catch (error) {
            userInfo.avatar = "";
          }
          userInfo.maskPhone = userInfo.phone.replace(userInfo.phone.substring(3, 7), "xxxx");
        }
        this.userInfo = userInfo;
      },
      updateUserInfo() {
        this.$commonSkip("userInfo");
      },
      changeRole() {
        this.$commonSkip("role");
      },
      handleFooterClick() {
        if (this.userInfo) {
          this.logout();
        } else {
          this.$commonSkip("authority");
        }
      },
      // 退出登录
      logout() {
        this.$dialog
          .confirm({
            title: "退出账号",
            message: "确定退出账号吗？",
          })
          .then(async () => {
            await createApiFun("", "/api/oauth/removeToken");
            localStorage.clear();
            this.initData();
            this.$toast({
              type: "success",
              message: "退出登录成功",
              forbidClick: true,
              duration: 1500,
            });
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background-color: #f7faf9;
    .page-main {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      padding: 0 12px;
    }
  }
  .main-title {
    padding: 16px 0;
    text-align: center;
    font-size: 17px;
    color: #323233;
    line-height: 18px;
    font-weight: 600;
    background-color: #fff;
  }

  .mine-main {
    padding: 12px 0;
    flex: 1;
    overflow: auto;
  }

  .user-card {
    background: linear-gradient(135deg, #55c69d 0%, #55c6c1 100%);
    border-radius: 8px;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) 1 1;
    padding: 16px 12px;
    position: relative;
  }

  .avatar-box {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
  }

  .user-name {
    margin-left: 12px;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
  }

  .user-phone {
    margin-top: 16px;
  }

  .icon-box {
    font-size: 20px;
    width: 20px;
    height: 20px;
    background-color: #ddf3ec;
    border-radius: 50%;
    color: #4ca786;
  }

  .icon-value {
    margin-left: 9px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
  }

  .user-company {
    margin-top: 12px;
  }

  .card-image {
    width: 160px;
    height: 78px;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .user-tip {
    padding: 8px 8px 12px 8px;
    background-color: #ffffff;
    border-radius: 0 0 8px 8px;
  }

  .tip-icon {
    font-size: 12px;
    color: #ff7d00;
  }

  .tip-label {
    margin-left: 5px;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
  }

  .tip-title {
    font-size: 12px;
    color: #f53f3f;
    line-height: 20px;
    flex: 1;
    text-align: right;
  }

  .info-box {
    padding: 12px 8px;
    background: #ffffff;
    border-radius: 8px;
    margin-top: 12px;
  }

  .info-image {
    width: 24px;
    height: 24px;
    overflow: hidden;
  }

  .info-title {
    margin-left: 4px;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
  }

  .mine-footer {
    margin-bottom: 32px;
    padding: 14px 0;
    background: #ffffff;
    border-radius: 24px;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #4ca786;
    line-height: 20px;
  }
</style>
