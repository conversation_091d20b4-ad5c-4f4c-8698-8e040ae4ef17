<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { CUSTOMER_TYPE, CREDIT_STATUS, CUSTOMER_STATUS, HOSPITAL_NATURE } from "@/enums";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/merchant/merchantFile/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "name",
                value: "客商名称",
              },
              {
                key: "code",
                value: "客商编号",
              },
              {
                key: "oldCode",
                value: "旧编码",
              },
              {
                key: "oldName",
                value: "曾用名",
              },
              {
                key: "address",
                value: "详细地址",
              },
              {
                key: "corporateName",
                value: "法人代表",
              },
              {
                key: "billingRate",
                value: "开票税率",
              },
              {
                key: "type",
                value: "客户类型",
                options: CUSTOMER_TYPE,
              },
              {
                key: "creditStatus",
                value: "信用状态",
                options: CREDIT_STATUS,
              },
              {
                key: "status",
                value: "客商状态",
                options: CUSTOMER_STATUS,
              },
              {
                key: "hospitalNature",
                value: "医院性质",
                options: HOSPITAL_NATURE,
              },
              {
                key: "registerNumber",
                value: "登记号",
              },
              {
                key: "receivingTeller",
                value: "收款员",
              },
              {
                key: "contact",
                value: "联系人名称",
              },
              {
                key: "contactPhone",
                value: "联系电话",
              },
              {
                key: "remark",
                value: "备注",
              },
            ],
          },
        ],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped></style>
