<template>
  <div>
    <div v-if="isSuccess" class="success-box">
      <van-image class="login-logo" :src="logoImg" fit="fill" />
      <div class="success-title">{{ dnaName }}</div>
      <van-image class="success-img" :src="successImg" fit="fill" />
      <div class="success-text">登录成功</div>
    </div>
    <div v-else>
      <div class="login-header">
        <van-image class="login-logo" :src="logoImg" fit="fill" />
        <div class="login-title">{{ dnaName }}</div>
      </div>
      <div class="login-button" v-if="showButton">
        <div class="button-box" @click="toggleToAccount">账号登录</div>
        <div class="back-button" @click="goBack">返回</div>
      </div>
      <div class="account-box" v-else>
        <div class="mb-64">
          <van-radio-group class="mb-12" v-model="radioValue" direction="horizontal" checked-color="#4ca786">
            <van-radio :name="item.id" v-for="item in channelList" :key="item.id">{{ item.name }}</van-radio>
          </van-radio-group>
          <van-field size="large" v-model="username" required clearable placeholder="请输入用户名" />
          <van-field size="large" v-model="password" required clearable type="password" placeholder="请输入密码" />
        </div>
        <div class="button-box" @click="accountLogin">登录</div>
        <div class="back-button" @click="backToButton">返回</div>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    getDna,
    setDna,
    getChannel,
    setChannel,
    getDnaName,
    setDnaName,
    setToken,
    setUserInfo,
    setLevelList,
    setCurrentLevel,
    setLevelId,
  } from "@/utils/storage";
  import { sm3Encrypt } from "@/utils";
  import { getInfoApiFunByParams, createApiFun, getInfoApiFun } from "@/api/base";
  export default {
    data() {
      return {
        isSuccess: false,
        showButton: true,
        username: "", //账号
        password: "", //密码
        logoImg: require("@/assets/images/logo.png"),
        successImg: require("@/assets/images/login_success.png"),
        dna: "",
        channelId: "",
        dnaName: "",
        radioValue: "",
        channelList: [{ id: "", name: "本部" }],
        levelArr: [110, 120, 130, 140, 180, 190, 199],
      };
    },
    async created() {
      let { dna, channelId, dnaName } = await this.getDnaAndChannel();
      this.dna = dna;
      this.channelId = channelId;
      this.dnaName = dnaName;
      let { data } = await getInfoApiFun(this.dna, "/api/base/dna/listByDna/");
      data.forEach((item) => {
        this.channelList.push(item);
      });
    },
    methods: {
      async getDnaAndChannel() {
        let dna = getDna();
        let channelId = getChannel();
        let dnaName = getDnaName();
        if (!dna || !channelId || !dnaName) {
          let { data } = await getInfoApiFunByParams(
            {
              ocbsUrl: process.env.VUE_APP_OCBS_URL,
            },
            "/api/base/getInfoByUrl",
          );
          setDna(data.dna);
          setChannel(data.channelId);
          setDnaName(data.name);
          dna = data.dna;
          channelId = data.channelId;
          dnaName = data.name;
          return {
            dna,
            channelId,
            dnaName,
          };
        }
        return {
          dna,
          channelId,
          dnaName,
        };
      },
      toggleToAccount() {
        this.showButton = false;
      },
      goBack() {
        this.$commonBack();
      },
      async accountLogin() {
        if (!this.username.trim()) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "请输入用户名",
            duration: 1500,
          });
          return;
        }
        if (!this.password.trim()) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "请输入密码",
            duration: 1500,
          });
          return;
        }
        this.$toast({
          type: "loading",
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });
        try {
          let { data } = await createApiFun(
            {
              client_id: "logansoft_web_id",
              client_secret: "logansoft_web_secret",
              grant_type: "password",
              scope: "scope",
              username: this.username.trim(),
              password: sm3Encrypt(this.password.trim()),
              dna: this.dna,
              channelId: this.radioValue,
            },
            "/api/oauth/token",
          );
          setToken(data.accessToken);
          this.getUserInfo();
          this.$toast.clear();
        } catch (error) {
          this.$toast.clear();
        }
      },
      backToButton() {
        this.username = "";
        this.password = "";
        this.showButton = true;
      },
      // 根据token获取用户信息
      async getUserInfo() {
        let { data } = await createApiFun("", "/api/user/loginUserAccountInfo");
        let rsp = await getInfoApiFun(data.lgUnionId, "/api/baseuser/getInfoByLgUnionId/");
        let res = await getInfoApiFun("", "/api/userrole/currentRoleList");
        let roleList = res.data;
        if (data) {
          let userdata = Object.assign(data, rsp.data);
          userdata.phone = userdata.phone ? this.$sm2Decrypt(userdata.phone) : "";
          userdata.idCard = userdata.idCard ? this.$sm2Decrypt(userdata.idCard) : "";
          setUserInfo(userdata);
        }
        if (roleList && roleList.length > 0) {
          let levelList = roleList
            .filter((list) => this.levelArr.includes(list.level))
            .map((item) => item.level)
            .sort((a, b) => a - b);
          setLevelList(levelList);
          setCurrentLevel(levelList[0]);
          let item = roleList.filter((list) => {
            return list.level == levelList[0];
          })[0];
          setLevelId(item.roleId);
        }
        this.$toast({
          type: "success",
          message: "登录成功",
          forbidClick: true,
          duration: 1500,
        });
        this.showPop = false;
        this.isSuccess = true;
        setTimeout(() => {
          this.$commonBack();
        }, 1800);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .login-header {
    height: 332px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url(@/assets/images/login_bg.png);
  }

  .login-logo {
    margin-top: 60px;
    width: 60px;
    height: 60px;
    overflow: hidden;
  }

  .login-title {
    padding-top: 12px;
    font-weight: 800;
    font-size: 18px;
    color: #162e25;
    line-height: 25px;
    text-align: center;
  }

  .login-button {
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .account-box {
    margin-top: 15px;
    padding: 0 16px;
  }

  .button-tip {
    font-weight: 400;
    font-size: 14px;
    color: #939c99;
    line-height: 20px;
    text-align: center;
  }

  .button-box {
    margin-top: 8px;
    background-color: #4ca786;
    font-weight: 400;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    text-align: center;
    padding: 11px 88px;
    border-radius: 32px;
  }

  .button-box::after {
    border: none;
  }

  .back-button {
    margin-top: 12px;
    border: 1px solid #dce0df;
    background-color: #fff;
    font-weight: 400;
    font-size: 16px;
    color: #939c99;
    line-height: 22px;
    text-align: center;
    padding: 11px 104px;
    border-radius: 32px;
  }

  .back-button::after {
    border: none;
  }

  .back-box {
    width: 100%;
    position: absolute;
    justify-content: center;
  }

  .pop-header {
    padding: 12px 15px 0 15px;
  }

  .pop-logo {
    width: 24px;
    height: 24px;
    overflow: hidden;
  }

  .header-title {
    font-weight: 400;
    font-size: 16px;
    color: #2e3432;
    line-height: 22px;
  }

  .ml-16 {
    margin-left: 8px;
  }

  .ml-24 {
    margin-left: 12px;
  }

  .tip-title {
    font-weight: bold;
    font-size: 18px;
    color: #162e25;
    line-height: 25px;
    margin-top: 28px;
  }

  .tip-memo {
    font-weight: 400;
    font-size: 14px;
    color: #939c99;
    line-height: 20px;
    margin-top: 8px;
  }

  .pop-main {
    margin-top: 12px;
  }

  .field-label {
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    margin-right: 16px;
  }

  .field-logo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
  }

  .cell-button {
    position: relative;
  }

  .avatar-button {
    width: 16px;
    height: 32px;
    position: absolute;
    top: 0;
    right: 0;
    background-color: transparent;
  }

  .avatar-button::after {
    border: none;
  }

  .pop-footer {
    margin-top: 32px;
  }

  .refuse-button {
    padding: 11px 44px;
    background: #dce0df;
    border-radius: 22px;
    font-weight: 400;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    margin: 0;
  }

  .refuse-button::after {
    border: none;
  }

  .allow-button {
    padding: 11px 44px;
    background: #4ca786;
    border-radius: 22px;
    font-weight: 400;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    margin: 0;
    margin-left: 16px;
  }

  .allow-button::after {
    border: none;
  }

  .success-box {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .success-title {
    margin-top: 12px;
    font-weight: 400;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    text-align: center;
  }

  .success-img {
    margin-top: 119px;
    width: 64px;
    height: 64px;
    overflow: hidden;
  }

  .success-text {
    margin-top: 10px;
    font-weight: 500;
    font-size: 16px;
    color: #2e3432;
    line-height: 22px;
    text-align: center;
  }

  .mb-64 {
    margin-bottom: 32px;
  }
  .mb-12 {
    margin-bottom: 12px;
    margin-left: 12px;
  }
</style>
