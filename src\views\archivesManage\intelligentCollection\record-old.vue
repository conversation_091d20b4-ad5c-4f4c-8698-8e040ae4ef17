<template>
  <div class="create-container">
    <van-tabs v-model="step" animated swipeable>
      <van-tab title="">
        <div class="create-box">
          <header class="create-header">
            <div
              class="header-title"
              :class="{ active: index === 0 }"
              v-for="(item, index) in titleList"
              :key="index"
              @click="toggleTab(index)"
            >
              <div class="header-sort">{{ item.sort }}</div>
              <div>{{ item.name }}</div>
            </div>
          </header>
          <div class="create-content">
            <main class="create-main">
              <div class="record-item" v-for="(item, index) in recordList" :key="index">
                <div class="record-left">{{ item.name }}</div>
                <div class="record-right">{{ item.enums ? item.enums[ruleForm[item.key]] : ruleForm[item.key] }}</div>
              </div>
            </main>
          </div>
        </div>
      </van-tab>
      <van-tab title="">
        <div class="create-box">
          <header class="create-header">
            <div
              class="header-title"
              :class="{ active: index === 1 }"
              v-for="(item, index) in titleList"
              :key="index"
              @click="toggleTab(index)"
            >
              <div class="header-sort">{{ item.sort }}</div>
              <div>{{ item.name }}</div>
            </div>
          </header>
          <div class="create-content">
            <van-pull-refresh v-model="dropRefreshing" @refresh="onDropRefresh" v-if="dropList.length > 0">
              <van-list v-model="dropLoading" :finished="dropFinished" @load="onDropLoad" :immediate-check="false">
                <ul class="list">
                  <li class="list-item" v-for="item in dropList" :key="item.id">
                    <div class="item-title">投放编号：{{ item.collectionNumber }}</div>
                    <div class="item-text">设备编号：{{ item.deviceNumber }}</div>
                    <div class="item-text">设备名称：{{ item.deviceName }}</div>
                    <div class="item-text">投放卡号：{{ item.advertisingCardNumber }}</div>
                    <div class="item-text">产废单位：{{ item.customersDossierName }}</div>
                    <div class="item-text">废物类型：{{ item.garbageType }}</div>
                    <div class="item-text">产生重量（Kg）：{{ item.advertisingWeight }}</div>
                    <div class="item-text">投放时间：{{ item.advertisingTime }}</div>
                    <div class="item-text">运输时间：{{ item.collectionTime }}</div>
                    <div class="item-text">废物状态：{{ TRASH_STATUS[item.status] }}</div>
                  </li>
                </ul>
              </van-list>
            </van-pull-refresh>
            <van-empty description="暂无投放记录数据" v-else />
          </div>
        </div>
      </van-tab>
      <van-tab title="">
        <div class="create-box">
          <header class="create-header">
            <div
              class="header-title"
              :class="{ active: index === 2 }"
              v-for="(item, index) in titleList"
              :key="index"
              @click="toggleTab(index)"
            >
              <div class="header-sort">{{ item.sort }}</div>
              <div>{{ item.name }}</div>
            </div>
          </header>
          <div class="create-content">
            <van-pull-refresh v-model="transportRefreshing" @refresh="onTransportRefresh">
              <van-list
                v-model="transportLoading"
                :finished="transportFinished"
                @load="onTransportLoad"
                :immediate-check="false"
              >
                <ul class="list" v-if="transportList.length > 0">
                  <li class="list-item" v-for="item in transportList" :key="item.id">
                    <div class="item-title">运输编号：{{ item.transportNumber }}</div>
                    <div class="item-text">设备名称：{{ item.deviceName }}</div>
                    <div class="item-text">设备编号：{{ item.deviceNumber }}</div>
                    <div class="item-text">运输单位：{{ item.transportUnit }}</div>
                    <div class="item-text">运输人员：{{ item.transportPerson }}</div>
                    <div class="item-text">收运重量（Kg）：{{ item.collectionWeight }}</div>
                    <div class="item-text">收运时间：{{ item.collectionTime }}</div>
                  </li>
                </ul>
                <van-empty description="暂无收运记录数据" v-else />
              </van-list>
            </van-pull-refresh>
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
  import { getInfoApiFun, getListPageApiFun } from "@/api/base";
  import { DEVICE_STATUS, CURRENT_STATUS, TRASH_STATUS } from "@/enums";
  export default {
    data() {
      return {
        id: "",
        step: 0, //新增步骤
        apis: {
          info: "/api/holding/holdingTank/get/",
          droplistPage: "/api/holding/holdingTank/dropRecordListPage",
          transportlistPage: "/api/holding/holdingTank/pickupRecordListPage",
        },
        titleList: [
          { sort: "①", name: "收集柜详情" },
          { sort: "②", name: "投放记录" },
          { sort: "③", name: "收运记录" },
        ],
        recordList: [
          { key: "companyName", name: "所属公司" },
          { key: "deviceCode", name: "设备编号" },
          { key: "deviceName", name: "设备名称" },
          { key: "deviceStatus", name: "设备状态", enums: DEVICE_STATUS },
          { key: "runStatus", name: "运行状态", enums: CURRENT_STATUS },
          { key: "deviceAddress", name: "设备地址" },
        ],
        TRASH_STATUS,
        ruleForm: {},
        dropRefreshing: false,
        dropLoading: false,
        dropFinished: false,
        dropPageNo: 1,
        dropPageSize: 10,
        dropTotal: 0,
        dropList: [],
        transportRefreshing: false,
        transportLoading: false,
        transportFinished: false,
        transportPageNo: 1,
        transportPageSize: 10,
        transportTotal: 0,
        transportList: [],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
      this.onDropLoad();
      this.onTransportLoad();
    },
    methods: {
      // 获取智能收集柜档案详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
      // 初始化数据
      async onDropLoad() {
        if (this.dropRefreshing) {
          this.dropList = [];
          this.dropPageNo = 1;
          this.dropRefreshing = false;
        }
        let params = {
          pageNo: this.dropPageNo,
          pageSize: this.dropPageSize,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.apis.droplistPage);
          if (res.success) {
            let dataList = res.data.datas;
            this.dropList = this.dropList.concat(dataList);
            this.dropTotal = res.data.total;
            this.dropPageNo++;
            if (this.dropList.length >= this.dropTotal) {
              this.dropFinished = true;
            }
          } else {
            this.dropFinished = true;
          }
          this.dropLoading = false;
        } catch (error) {
          this.dropLoading = false;
        }
      },
      // 刷新
      onDropRefresh() {
        this.dropFinished = false;
        this.dropRefreshing = true;
        this.dropLoading = true;
        this.onDropLoad();
      },
      // 初始化数据
      async onTransportLoad() {
        if (this.transportRefreshing) {
          this.transportList = [];
          this.transportPageNo = 1;
          this.transportRefreshing = false;
        }
        let params = {
          pageNo: this.transportPageNo,
          pageSize: this.transportPageSize,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.apis.transportlistPage);
          if (res.success) {
            let dataList = res.data.datas;
            this.transportList = this.transportList.concat(dataList);
            this.transportTotal = res.data.total;
            this.transportPageNo++;
            if (this.transportList.length >= this.transportTotal) {
              this.transportFinished = true;
            }
          } else {
            this.transportFinished = true;
          }
          this.transportLoading = false;
        } catch (error) {
          this.transportLoading = false;
        }
      },
      // 刷新
      onTransportRefresh() {
        this.transportFinished = false;
        this.transportRefreshing = true;
        this.transportLoading = true;
        this.onTransportLoad();
      },
      // 切换tab栏
      toggleTab(index) {
        this.step = index;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
    padding: 0 12px;
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    word-break: break-all;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
