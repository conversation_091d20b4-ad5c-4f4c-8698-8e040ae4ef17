<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入检查人/被检查人名称/车牌号"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { SUPERVISORY_TEST_COMPLETED, IS_NORMAL_OPERATION } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "inspectorName",
            value: "检查人",
          },
          {
            key: "checkedVehiclePlateNumber",
            value: "车牌号",
          },
          {
            key: "spotCheckDate",
            value: "抽查日期",
          },
          {
            key: "rectifiedStatus",
            value: "是否完成整改",
            enums: SUPERVISORY_TEST_COMPLETED,
          },
          {
            key: "isError",
            value: "是否危运车辆",
            enums: IS_NORMAL_OPERATION,
          },
        ],
        listPageApi: "/api/safety/spotCheckRecord/listPage",
        filterList: [
          {
            type: "Date",
            key: "spotCheckBeginDate",
            value: "抽查开始日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Date",
            key: "spotCheckEndDate",
            value: "抽查结束日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Options",
            key: "rectifiedStatus",
            value: "是否完成整改",
            enums: SUPERVISORY_TEST_COMPLETED,
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("supervisoryTestCreate", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
