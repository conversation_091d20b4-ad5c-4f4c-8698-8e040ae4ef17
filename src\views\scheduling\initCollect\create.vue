<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <van-field
              :value="ruleForm.pickupPointName"
              name="pickupPointId"
              label="点位名称"
              placeholder="请选择点位"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              required
              :border="false"
              :rules="rules.pickupPointId"
              @click="showPopup = true"
            />
            <van-field
              v-model="ruleForm.contact"
              name="contact"
              label="点位联系人名称"
              placeholder="请输入点位联系人名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
              label-width="120"
            />
            <van-field
              v-model="ruleForm.contactPhone"
              name="contactPhone"
              label="点位联系人联系电话"
              placeholder="请输入点位联系人联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
              label-width="150"
            />
            <van-field
              v-model="ruleForm.districtName"
              name="districtName"
              label="省市区"
              placeholder="请选择省市区"
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
              required
            />
            <van-field
              v-model="ruleForm.address"
              name="address"
              label="详细地址"
              placeholder="请输入详细地址"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.address"
              readonly
              required
              type="textarea"
              :autosize="{ maxHeight: 100, minHeight: 50 }"
            />
            <van-field
              v-model="ruleForm.expectTime"
              label="期望收运时间"
              placeholder="请选择期望收运时间"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              @click="showDatePicker = true"
            />
            <van-field
              name="rubbishTotal"
              label="废物总量（kg）"
              input-align="right"
              error-message-align="right"
              label-width="160px"
              :border="false"
              :rules="rules.rubbishTotal"
              required
            >
              <template #input>
                <van-stepper
                  v-model="ruleForm.rubbishTotal"
                  min="0"
                  max="99999999"
                  :decimal-length="2"
                  :default-value="''"
                  input-width="80px"
                />
              </template>
            </van-field>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-22"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >完成</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showPopup" position="right">
      <template v-if="showPopup">
        <div class="popup-container">
          <point
            :pickupPointId="ruleForm.pickupPointId"
            @closePopup="showPopup = false"
            @selectItem="selectPickupPoint"
          ></point>
        </div>
      </template>
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        title="选择年月日小时"
        type="datehour"
        v-model="currentDate"
        :formatter="formatter"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import { filterObjectByFieldArr } from "@/utils";
  import point from "./components/point.vue";
  import moment from "moment";
  // 校验函数
  const validator = function (val) {
    return val && val > 0 ? true : false;
  };
  export default {
    components: {
      point,
    },
    data() {
      return {
        submitFormThrottling: () => {},
        apis: {
          create: "/api/waybill/sponsorWaybill",
        },
        ruleForm: {
          pickupPointId: "", //点位id
          pickupPointName: "", //点位名称
          pointName: "", //点位名称
          contact: "", //点位联系人
          contactPhone: "", //点位联系电话
          districtName: "", //省市区
          address: "", //详细地址
          rubbishTotal: "", //废物总量
          expectTime: "", //期望收运时间
        },
        rules: {
          pickupPointId: [{ required: true, message: "请选择点位" }],
          contact: [{ required: true, message: "请输入点位联系人名称" }],
          contactPhone: [{ required: true, message: "请输入点位联系人联系电话" }],
          districtName: [{ required: true, message: "请选择省市区" }],
          address: [{ required: true, message: "请输入详细地址" }],
          rubbishTotal: [{ validator, message: "请填写废物总量" }],
        },
        showPointPicker: false,
        showPopup: false,
        showDatePicker: false,
        currentDate: new Date(),
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {},
    methods: {
      formatter(type, val) {
        if (type === "year") {
          return val + "年";
        }
        if (type === "month") {
          return val + "月";
        }
        if (type === "day") {
          return val + "日";
        }
        if (type === "hour") {
          return val + "时";
        }
        return val;
      },
      // 选择点位
      selectPickupPoint(item) {
        if (item.id !== this.ruleForm.pickupPointId) {
          this.ruleForm.pickupPointName = item.name;
          this.ruleForm.pickupPointId = item.id;
          this.ruleForm.contact = item.contact;
          this.ruleForm.contactPhone = item.contactPhone;
          this.ruleForm.districtName = `${item.provinceName}/${item.cityName}/${item.districtName}`;
          this.ruleForm.address = item.address;
        } else {
          this.ruleForm.pickupPointName = "";
          this.ruleForm.pickupPointId = "";
          this.ruleForm.contact = "";
          this.ruleForm.contactPhone = "";
          this.ruleForm.districtName = "";
          this.ruleForm.address = "";
        }
        this.showPopup = false;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm.expectTime = moment(value).format("YYYY-MM-DD HH:mm");
        this.showDatePicker = false;
      },
      // 提交
      onSubmit() {
        if (!this.ruleForm.pickupPointId) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "请选择点位",
            duration: 1500,
          });
          return;
        }
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = filterObjectByFieldArr(this.ruleForm, ["pickupPointId", "rubbishTotal", "expectTime"]);
            try {
              let res = await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "发起收运成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .popup-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
  }
  .pickup-point-select {
    width: 100%;
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .pickup-point {
    padding-top: 0;
  }
</style>
