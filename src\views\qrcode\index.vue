<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="用户信息"></baseTitle>
            <van-field
              :value="userInfo.fullName"
              label="姓名"
              placeholder="请输入姓名"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="sexOptions[userInfo.sex] || '-'"
              label="性别"
              placeholder="请选择性别"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="userInfo.phone"
              label="联系电话"
              placeholder="请输入联系电话"
              input-align="right"
              :border="false"
              readonly
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="点位信息"></baseTitle>
            <van-field
              :value="ruleForm.name"
              label="点位名称"
              placeholder="请输入点位名称"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="ruleForm.code"
              label="点位编号"
              placeholder="请输入点位编号"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="POINT_TYPE[ruleForm.type]"
              label="点位类型"
              placeholder="请选择点位类型"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="ruleForm.contact"
              label="点位联系人"
              placeholder="请输入点位联系人"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="ruleForm.contactPhone"
              label="联系方式"
              placeholder="请输入联系方式"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="ruleForm.address"
              label="点位地址"
              placeholder="请输入点位地址"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="undockOptions[ruleForm.isUndock]"
              label="移除状态"
              placeholder="请选择移除状态"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="DEVICE_STATUS[ruleForm.deviceStatus] || '-'"
              label="设备状态"
              placeholder="请选择设备状态"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="COLLECTION_CYCLE[ruleForm.period]"
              label="收运周期"
              placeholder="请选择收运周期"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="ruleForm.frequency"
              label="收运频次"
              placeholder="请选择收运频次"
              input-align="right"
              :border="false"
              readonly
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >绑定</van-button
          >
        </footer>
      </div>
    </van-form>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import baseTitle from "@/components/baseTitle";
  import { POINT_TYPE, COLLECTION_CYCLE, DEVICE_STATUS } from "@/enums";
  export default {
    components: {
      baseTitle,
    },
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/pickup/pickupPoint/bindPhone",
          info: "/api/pickup/pickupPoint/get/",
        },
        ruleForm: {},
        sexOptions: ["男", "女"],
        POINT_TYPE,
        COLLECTION_CYCLE,
        DEVICE_STATUS,
        undockOptions: ["否", "移除"],
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
      this.getRecord();
    },
    mounted() {},
    methods: {
      // 获取点位相亲
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.$route.query.id, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
            this.ruleForm.contactPhone = this.ruleForm.contactPhone ? this.$sm2Decrypt(this.ruleForm.contactPhone) : "";
            for (let key in this.ruleForm) {
              this.ruleForm[key] =
                this.ruleForm[key] || this.ruleForm[key] === 0 || this.ruleForm[key] === false
                  ? this.ruleForm[key]
                  : "-";
            }
          }
        } catch (error) {
          console.log("error ==> ", error);
        }
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun({ pickupPointId: this.$route.query.id }, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "绑定成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  window.wx.miniProgram.reLaunch({
                    url: `/pages/index/index`,
                  });
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-flex {
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .warning-text {
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      text-align: center;
      margin-top: 10px;
    }
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .tips-icon {
    font-size: 50px;
    color: #ff7d00;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
