//小程序内嵌H5 web-view路由跳转处理
//js-sdk
/**
 * 注意：关于是否开启小程序环境页面返回刷新，路由需要拼接isRefresh=true参数开启
 */

export const wxHomeWebview = "/pages/index/index"; //小程序首页
export const wxWebview = "/pages/webview/index"; //二级页面
export const openDocView = "/pages/opendoc/index";

//保留当前页面，跳转到应用内的某个页面。但是不能跳到 tabbar 页面。使用 wx.navigateBack 可以返回到原页面。小程序中页面栈最多十层
export function wxNavigateTo(url, suc, wxurl) {
  window.wx.miniProgram.navigateTo({
    url: `${wxurl || wxWebview}?to=${encodeURIComponent(url)}`,
    success: function () {
      return typeof suc === "function" && suc();
    },
  });
}

//关闭当前页面，返回上一页面或多级页面。可通过 getCurrentPages 获取当前的页面栈，决定需要返回几层
export function wxNavigateBack(suc, delta = 1) {
  window.wx.miniProgram.navigateBack({
    delta,
    success: function () {
      return typeof suc === "function" && suc();
    },
  });
}

//跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
export function wxSwitchTab(url, suc) {
  window.wx.miniProgram.switchTab({
    url: url,
    success: function () {
      return typeof suc === "function" && suc();
    },
  });
}

//关闭所有页面，打开到应用内的某个页面
export function wxReLaunch(url, suc) {
  window.wx.miniProgram.reLaunch({
    url: `${wxWebview}?to=${encodeURIComponent(url)}`,
    success: function () {
      return typeof suc === "function" && suc();
    },
  });
}

//关闭当前页面，跳转到应用内的某个页面。但是不允许跳转到 tabbar 页面
export function wxRedirectTo(url, suc) {
  window.wx.miniProgram.redirectTo({
    url: `${wxWebview}?to=${encodeURIComponent(url)}`,
    success: function () {
      return typeof suc === "function" && suc();
    },
  });
}

//前进跳转至小程序页面
export function wxAdvanceNavigateTo(url, suc) {
  window.wx.miniProgram.navigateTo({
    url: url,
    success: function () {
      return typeof suc === "function" && suc();
    },
  });
}

//向小程序发送消息，会在特定时机（小程序后退、组件销毁、分享）触发组件的message事件
export function wxPostMessage(data) {
  let content = window.wx.miniProgram;
  content.postMessage({ data });
}
