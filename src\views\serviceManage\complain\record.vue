<template>
  <div class="create-container">
    <div class="create-box">
      <div class="create-content">
        <div v-for="(field, fieldIndex) in fieldList" :key="fieldIndex">
          <div class="border-line" v-if="fieldIndex > 0"></div>
          <main class="create-main">
            <baseTitle :title="field.title"></baseTitle>
            <div class="record-item" v-for="(item, index) in field.dataList" :key="index">
              <div class="file-box" v-if="item.isFile">
                <div class="record-left"
                  >{{ item.name }}<el-link type="info" :underline="false">（文件请前往管理端下载）</el-link></div
                >
                <div
                  class="file-right"
                  v-if="ruleForm[item.key] && (isJSON(ruleForm[item.key]) || Array.isArray(ruleForm[item.key]))"
                >
                  <div class="link-block" v-for="(fileItem, fileIndex) in ruleForm[item.key]" :key="fileIndex">
                    <el-link type="primary" @click="previewImage(fileItem)">{{ fileItem.name }}</el-link>
                  </div>
                </div>
                <div class="file-right" v-else>-</div>
              </div>
              <template v-else>
                <div class="record-left">{{ item.name }}</div>
                <div class="record-right">{{ item.enums ? item.enums[ruleForm[item.key]] : ruleForm[item.key] }}</div>
              </template>
            </div>
          </main>
        </div>
      </div>
    </div>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="0"></van-image-preview>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { HANDLING_STATUS, COMPLAINT_TYPE } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  import { isJSON } from "@/utils";
  export default {
    components: {
      baseTitle,
    },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/complaintRecord/get/",
        },
        fieldList: [
          {
            title: "基础信息",
            dataList: [
              { key: "customerName", name: "客商名称" },
              { key: "customerContactNumber", name: "客商联系电话" },
              { key: "customerEmail", name: "客商电子邮件" },
              { key: "complaintDate", name: "投诉日期" },
            ],
          },
          {
            title: "投诉详情",
            dataList: [
              { key: "type", name: "投诉类型", enums: COMPLAINT_TYPE },
              { key: "content", name: "投诉内容" },
            ],
          },
          {
            title: "附件信息",
            dataList: [{ key: "fileList", name: "附件", isFile: true }],
          },
          {
            title: "处理信息",
            dataList: [
              { key: "handlingStatus", name: "处理情况", enums: HANDLING_STATUS },
              { key: "handlingDate", name: "处理日期" },
              { key: "remarks", name: "处理备注" },
            ],
          },
        ],
        ruleForm: {},
        isJSON,
        previewImages: [],
        showPreview: false,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取合同详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          for (let key in this.ruleForm) {
            this.ruleForm[key] = this.ruleForm[key] || this.ruleForm[key] === 0 ? this.ruleForm[key] : "-";
          }
        }
      },
      // 预览附件
      previewImage(item) {
        let suffix = item.url.slice(item.url.lastIndexOf("."));
        if ([".png", ".jpg", ".jpeg", ".gif"].includes(suffix)) {
          this.previewImages = [item.url];
          this.showPreview = true;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 24px;
  }
  .record-item {
    padding: 12px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 16px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 16px;
    text-align: right;
    word-break: break-all;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .file-right {
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    word-break: break-all;
    margin-top: 4px;
  }
  .link-block {
    padding-bottom: 4px;
  }
</style>
