//12 #222
.text-xs-primary {
  font-size: $font-size-xs;
  color: $--color-text-primary;
}

//14 #222
.text-sm-primary {
  font-size: $font-size-sm;
  color: $--color-text-primary;
}

//16 #222
.text-bs-primary {
  font-size: $font-size-bs;
  color: $--color-text-primary;
}

//20 #222
.text-md-primary {
  font-size: $font-size-md;
  color: $--color-text-primary;
}

//12 #666
.text-xs-regular {
  font-size: $font-size-xs;
  color: $--color-text-regular;
}

//14 #666
.text-sm-regular {
  font-size: $font-size-sm;
  color: $--color-text-regular;
}

//16 #666
.text-bs-regular {
  font-size: $font-size-bs;
  color: $--color-text-regular;
}

//20 #666
.text-md-regular {
  font-size: $font-size-md;
  color: $--color-text-regular;
}

//12 #999
.text-xs-secondary {
  font-size: $font-size-xs;
  color: $--color-text-secondary;
}

//14 #999
.text-sm-secondary {
  font-size: $font-size-sm;
  color: $--color-text-secondary;
}

//16 #999
.text-bs-secondary {
  font-size: $font-size-bs;
  color: $--color-text-secondary;
}

//20 #999
.text-md-secondary {
  font-size: $font-size-md;
  color: $--color-text-secondary;
}

//12 #aaa
.text-xs-placeholder {
  font-size: $font-size-xs;
  color: $--color-text-placeholder;
}

//14 #aaa
.text-sm-placeholder {
  font-size: $font-size-sm;
  color: $--color-text-placeholder;
}

//16 #aaa
.text-bs-placeholder {
  font-size: $font-size-bs;
  color: $--color-text-placeholder;
}

//20 #aaa
.text-md-placeholder {
  font-size: $font-size-md;
  color: $--color-text-placeholder;
}

//34
.first-title {
  font-size: $font-size-xl;
}

//26
.second-title {
  font-size: $font-size-lg;
}

//44
.important-number {
  font-size: $font-size-bx;
}

.mt-4 {
  margin-top: 4px;
}

.mb-4 {
  margin-bottom: 4px;
}
.mr-2 {
  margin-right: 2px;
}
.mr-4 {
  margin-right: 4px;
}

.ml-4 {
  margin-left: 4px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mr-8 {
  margin-right: 8px;
}

.ml-8 {
  margin-left: 8px;
}

.mt-12 {
  margin-top: 12px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mr-12 {
  margin-right: 12px;
}

.ml-12 {
  margin-left: 12px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mr-16 {
  margin-right: 16px;
}

.ml-16 {
  margin-left: 16px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mr-20 {
  margin-right: 20px;
}

.ml-20 {
  margin-left: 20px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mr-24 {
  margin-right: 24px;
}

.ml-24 {
  margin-left: 24px;
}

.mt-28 {
  margin-top: 28px;
}

.mb-28 {
  margin-bottom: 28px;
}

.mr-28 {
  margin-right: 28px;
}

.ml-28 {
  margin-left: 28px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-32 {
  margin-bottom: 32px;
}

.mr-32 {
  margin-right: 32px;
}

.ml-32 {
  margin-left: 32px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mr-40 {
  margin-right: 40px;
}

.ml-40 {
  margin-left: 40px;
}

.h-100 {
  height: 100%;
}

.w-100 {
  width: 100%;
}

.w-25 {
  width: 25%;
}

.w-50 {
  width: 50%;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex-start-center {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.flex-center-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-center-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-center-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start-end {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.flex-start-between {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.flex-stretch-between {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex-column-center-start {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.flex-column-center-end {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

.fw-r {
  font-weight: 400;
}

.fw-m {
  font-weight: 500;
}

.fw-sb {
  font-weight: 600;
  font-weight: 700;
}

.fw-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.pointer {
  cursor: pointer;
}

.color-green {
  color: $--color-primary;
}

.color-white {
  color: #fff;
}

.color-light-green {
  color: $--color-success;
}

.color-red {
  color: $--color-danger;
}

// 1行
.text-ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

//需line-clamp-x组合一起用
.line-clamp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  word-wrap: break-word;
}

// 2行
.line-clamp-2 {
  -webkit-line-clamp: 2;
}

// 3行
.line-clamp-3 {
  -webkit-line-clamp: 3;
}

/*细滚动条样式*/
.small-scroll {
  overflow-y: auto;
  overflow-y: overlay; //滚动条不占宽度，chrome下可用

  &::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: #9fa0a2;
  }

  &::-webkit-scrollbar-track {
    background: #ebebeb;
    border-radius: 5px;
  }

  &::-webkit-scrollbar-track-piece {
    background: #ebebeb;
  }
}

.padding-none {
  padding: 0 !important;
}
