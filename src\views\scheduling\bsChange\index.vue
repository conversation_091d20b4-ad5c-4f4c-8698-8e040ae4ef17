<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <van-field
              v-model="ruleForm.pickUpPointName"
              name="pickUpPointId"
              label="点位名称"
              placeholder="请选择点位"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.pickUpPointId"
              required
              readonly
              is-link
              @click="showPointPicker = true"
            />
            <van-field
              v-model="ruleForm.phone"
              name="phone"
              label="点位联系人联系电话"
              placeholder="请输入点位联系人联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.phone"
              required
              label-width="150"
              maxlength="11"
            />
            <van-field
              v-model="ruleForm.districtName"
              name="districtName"
              label="省市区"
              placeholder="选择点位自动填入省市区"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.districtName"
              readonly
              required
            />
            <van-field
              v-model="ruleForm.address"
              name="address"
              label="详细地址"
              placeholder="选择点位自动填入详细地址"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.address"
              readonly
              required
              type="textarea"
              :autosize="{ maxHeight: 100, minHeight: 50 }"
            />
            <van-field
              v-model="ruleForm.email"
              name="email"
              label="联系人邮箱"
              placeholder="请输入点位联系人邮箱"
              input-align="right"
              error-message-align="right"
              :border="false"
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.startDate"
              name="startDate"
              label="变更开始日期"
              placeholder="请选择变更开始日期"
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.startDate"
              required
              @click="openStartDatePicker()"
            />
            <van-field
              v-model="ruleForm.endDate"
              name="endDate"
              label="变更结束日期"
              placeholder="请选择变更结束日期"
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.endDate"
              required
              @click="openEndDatePicker()"
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showStartDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showEndDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showPointPicker" position="right">
      <div class="popup-container">
        <point
          :selectedId="ruleForm.pickUpPointId"
          @closePopup="showPointPicker = false"
          @selectItem="selectItem"
        ></point>
      </div>
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import moment from "moment";
  import point from "./components/point.vue";
  export default {
    components: { point },
    data() {
      return {
        currentDate: new Date(),
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        submitFormThrottling: () => {},
        apis: {
          create: "/api/supplierAlter/save",
        },
        ruleForm: {
          districtName: "", //前端展示
          address: "", //前端展示
          pickUpPointId: "", //点位id
          pickUpPointName: "", //点位名称
          phone: "",
          email: "",
          startDate: "",
          endDate: "",
        },
        rules: {
          phone: [{ required: true, message: "请输入点位联系人联系电话" }],
          pickUpPointId: [{ required: true, message: "请选择点位" }],
          districtName: [{ required: true, message: "选择点位自动填入省市区" }],
          address: [{ required: true, message: "选择点位自动填入详细地址" }],
          startDate: [{ required: true, message: "请选择变更开始日期" }],
          endDate: [{ required: true, message: "请选择变更结束日期" }],
        },
        showStartDatePicker: false,
        showEndDatePicker: false,
        showPointPicker: false,
        selectedId: "",
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {},
    methods: {
      onStartDateConfirm(value) {
        this.ruleForm.startDate = moment(value).format("YYYY-MM-DD");
        this.showStartDatePicker = false;
      },
      onEndDateConfirm(value) {
        this.ruleForm.endDate = moment(value).format("YYYY-MM-DD");
        this.showEndDatePicker = false;
      },
      openStartDatePicker() {
        this.showStartDatePicker = true;
      },
      openEndDatePicker() {
        this.showEndDatePicker = true;
      },
      // 点位选择change事件
      changePoint(value) {
        if (!value) {
          this.ruleForm.phone = "";
          this.ruleForm.districtName = "";
          this.ruleForm.address = "";
          return;
        }
        let item = this.pickupPointOptions.filter((o) => o.id === value)[0];
        this.ruleForm.phone = item.contactPhone;
        this.ruleForm.districtName = `${item.provinceName}/${item.cityName}/${item.districtName}`;
        this.ruleForm.address = item.address;
      },
      // 选择点位
      selectItem(item) {
        this.ruleForm.pickUpPointId = item.id;
        this.ruleForm.pickUpPointName = item.name;
        this.ruleForm.phone = item.contactPhone;
        this.ruleForm.districtName = `${item.provinceName}/${item.cityName}/${item.districtName}`;
        this.ruleForm.address = item.address;
        this.showPointPicker = false;
      },
      // 提交
      onSubmit() {
        if (!this.ruleForm.pickUpPointId) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "请选择点位",
            duration: 1500,
          });
          return;
        }
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "提交经营状态变更成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .pickup-point-select {
    width: 100%;
  }
  .popup-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .pickup-point {
    padding-top: 0;
  }
</style>
