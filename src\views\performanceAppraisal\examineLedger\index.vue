<template>
  <div class="page-container">
    <div class="page-tab">
      <van-tabs v-model="activeTab" color="#4ca786" title-active-color="#4ca786" @change="changeTab">
        <van-tab title="月度" name="0"></van-tab>
        <van-tab title="年度" name="1"></van-tab>
      </van-tabs>
    </div>
    <header class="page-header">
      <div class="header-input">
        <van-field
          v-model="filterForm.keyword"
          label=""
          placeholder="请输入考核方案名称/被考核人名称"
          right-icon="search"
          clearable
          @change="onRefresh"
          @clear="onRefresh"
        >
          <template #right-icon>
            <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
          </template>
        </van-field>
      </div>
      <div class="filter-button" @click="showPop = !showPop">
        <span class="filter-title">筛选</span>
        <span class="sctmp-iconfont icon-ic_xia filter-icon" v-show="!showPop"></span>
        <span class="sctmp-iconfont icon-ic_shang filter-icon" v-show="showPop"></span>
      </div>
    </header>
    <div v-if="showPop" position="top">
      <div class="filter-box">
        <component
          v-if="activeTab == 0"
          :ref="filterList[0].type"
          :is="filterList[0].type"
          v-model="filterForm[filterList[0].key]"
          :filterItem="filterList[0]"
        />
        <component
          v-else
          :ref="filterList[1].type"
          :is="filterList[1].type"
          v-model="filterForm[filterList[1].key]"
          :filterItem="filterList[1]"
        />
        <div class="filter-bottom">
          <div class="reset-button" @click="resetList">重置</div>
          <div class="search-button" @click="searchList">查询</div>
        </div>
      </div>
    </div>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index" @click="itemClick(item)">
              <div class="item-left">
                <div class="item-title" v-if="activeTab == 0"
                  >考核月度：{{ item.year }}-{{ item.month >= 10 ? item.month : "0" + item.month }}</div
                >
                <div class="item-title" v-else>考核年度：{{ item.year }}</div>
                <div class="item-text">考核方案：{{ item.assessSchemeName }}</div>
                <div class="item-text">被考核人：{{ item.fullName }}</div>
                <div class="item-text" v-if="activeTab == 0"
                  >当前流程：{{
                    [0, 2].includes(item.verifyStatus)
                      ? EXAMINE_FLOW[item.verifyStatus]
                      : ASSESSMENT_FLOWS[item.verifyStatus]
                  }}</div
                >
                <div class="item-text" v-if="activeTab == 0">基础绩效：{{ item.basicPerformance }}</div>
                <div class="item-text">总分：{{ item.totalScore }}</div>
                <div class="item-text">考核等级：{{ item.assessGrade }}</div>
                <div class="item-text" v-if="activeTab == 0">绩效：{{ item.payIncentives }}</div>
              </div>
              <div class="item-right">
                <span class="sctmp-iconfont icon-ic_xiazuan"></span>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import components from "@/components/pageList/componentImport";
  import moment from "moment";
  import { EXAMINE_FLOW, ASSESSMENT_FLOWS } from "@/enums";
  export default {
    components: components,
    data() {
      return {
        EXAMINE_FLOW,
        ASSESSMENT_FLOWS,
        showPop: false,
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        listPageApi: "/api/access/record/listPage",
        tableList: [],
        filterForm: {},
        filterList: [
          {
            type: "YearMonth",
            key: "yearMonth",
            value: "考核月度",
            format: "YYYY/MM",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Year",
            key: "yearMonth",
            value: "考核年度",
            format: "YYYY/MM",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
        activeTab: "0",
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 切换筛选弹窗
      togglePop() {
        this.showPop = !this.showPop;
      },
      // 切换tab栏
      changeTab() {
        this.showPop = false;
        this.filterForm = {};
        this.onRefresh();
      },
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          period: this.activeTab,
          keyword: this.filterForm.keyword,
          isRole: 1,
        };
        if (this.filterForm.yearMonth) {
          params.yearOrMonth = moment(this.filterForm.yearMonth).format("YYYY/MM");
        }
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas;
            dataList.forEach((list) => {
              for (let key in list) {
                list[key] = list[key] || list[key] === 0 ? list[key] : "-";
              }
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 列表项点击事件
      itemClick(item) {
        this.$commonSkip("examineLedgerRecord", { id: item.id });
      },
      // 重置
      resetList() {
        this.filterForm = {};
        this.onRefresh();
        this.togglePop();
      },
      // 查询
      searchList() {
        this.onRefresh();
        this.togglePop();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f7faf9;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
  }
  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0 10px 10px;
    border-radius: 4px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 20px;
      margin-right: 4px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  .filter-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    padding-bottom: 10px;
    background: #fff;
  }
  .reset-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #dce0df;
    border-radius: 20px;
  }
  .search-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #4ca786;
    border-radius: 20px;
    margin-left: 8px;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
  }
  .header-input {
    flex: 1;
    overflow: hidden;
    ::v-deep .van-field {
      background: #f7faf9;
    }
  }
</style>
