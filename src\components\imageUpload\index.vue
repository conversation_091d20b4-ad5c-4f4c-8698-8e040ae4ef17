<template>
  <div>
    <van-field :name="formId" :rules="[{ required: isRequired, message: formPlaceHolder }]" :border="false">
      <template #input>
        <van-uploader
          v-if="capture"
          v-model="imageList"
          :before-read="beforeRead"
          :after-read="afterRead"
          :max-count="maxNumber"
          :accept="acceptString"
          :capture="capture"
          result-type="file"
          @delete="deleteFile"
        />
        <van-uploader
          v-else
          v-model="imageList"
          :before-read="beforeRead"
          :after-read="afterRead"
          :max-count="maxNumber"
          :accept="acceptString"
          result-type="file"
          @delete="deleteFile"
        />
      </template>
    </van-field>
    <div class="upload__tip">
      <div>
        <span>允许格式：{{ acceptString }}</span>
      </div>
      <div>
        <span
          >限制文件最大上传数量为：{{ maxNumber }}个，单文件大小最大{{ maxSize }}MB，当前已上传：{{
            imageList.length
          }}个。</span
        >
      </div>
    </div>
  </div>
</template>

<script>
  import { OSSUpload } from "@/utils/upload";
  export default {
    props: {
      value: {
        type: [Array, String],
        default: () => [],
      },
      maxSize: {
        type: Number,
        default: 10,
      },
      maxNumber: { type: Number, default: 5 },
      formId: {
        type: String,
        default: "",
      },
      isRequired: {
        type: Boolean,
        default: false,
      },
      formPlaceHolder: {
        type: String,
        default: "",
      },
      acceptString: {
        type: String,
        default: ".jpg,.png,.jpeg,.bmp,.gif",
      },
      capture: {
        type: String,
        default: "",
      },
      // 初始化回显数据
      fileList: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      fileList(val) {
        this.imageList = val;
        this.urlList = val;
      },
    },
    name: "ImageUpload",
    data() {
      return {
        imageList: [],
        acceptList: [],
        urlList: [],
      };
    },
    created() {
      this.acceptList = this.acceptString.split(",");
    },
    async mounted() {
      this.imageList = this.value;
      this.urlList = this.value;
      await OSSUpload.getOssBase();
    },
    methods: {
      beforeRead(fileList) {
        let flag = true;
        let suffixFlag = true;
        if (Array.isArray(fileList)) {
          for (let i = 0; i < fileList.length - 1; i++) {
            suffixFlag = this.checkSuffix(fileList[i].name);
            if (!suffixFlag) {
              break;
            } else {
              if (fileList[i].size > this.maxSize * 1024 * 1024) {
                flag = false;
                break;
              }
            }
          }
        } else {
          suffixFlag = this.checkSuffix(fileList.name);
          flag = fileList.size > this.maxSize * 1024 * 1024 ? false : true;
        }
        if (!suffixFlag) {
          this.$toast.fail(`请上传${this.acceptString}后缀的文件`);
        } else {
          if (!flag) {
            this.$toast.fail(`文件大小不能超过${this.maxSize}MB`);
          }
        }
        return suffixFlag && flag;
      },
      async afterRead(fileList) {
        if (Array.isArray(fileList)) {
          for (let i = 0; i < fileList.length; i++) {
            await this.httpRequest(fileList[i], fileList[i].file);
          }
        } else {
          // let compressImg = await this.handleBeforeUpload(fileList.file);
          this.httpRequest(fileList, fileList.file);
        }
      },
      async httpRequest(file, currFile) {
        let ossClient = new OSSUpload();
        ossClient.init();
        ossClient
          .upload(currFile, (percentage) => {
            file.status = "uploading";
            file.percentage = percentage;
          })
          .then((data) => {
            file.url = data.url;
            file.status = "done";
            let urlFile = {
              name: currFile.name,
              size: currFile.size,
              url: data.url,
            };
            this.urlList.push(urlFile);
            this.$emit("input", this.urlList);
          })
          .catch((err) => {
            console.log("上传失败", err);
            file.status = "failed";
            let index = this.imageList.findIndex((data) => JSON.stringify(file) === JSON.stringify(data));
            if (index >= 0) {
              this.imageList.splice(index, 1);
            }
          });
      },
      checkSuffix(filename) {
        let index = filename.lastIndexOf("."); //后缀名所在位置
        let suffix = filename.slice(index).toLowerCase(); //后缀名
        return this.acceptList.includes(suffix);
      },
      deleteFile(_, detail) {
        let { index } = detail;
        this.urlList.splice(index, 1);
        this.$emit("input", this.urlList);
      },
      handleBeforeUpload(file) {
        return new Promise((resolve) => {
          if (file.size <= 200 * 1024) {
            resolve(file);
            return;
          }
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = (event) => {
            const img = new Image();
            img.src = event.target.result;
            img.onload = () => {
              const canvas = document.createElement("canvas");
              const ctx = canvas.getContext("2d");
              let width = img.width;
              let height = img.height;
              let ratio; //像素比
              if ((ratio = (width * height) / 4000000) > 1) {
                ratio = Math.sqrt(ratio);
                width /= ratio;
                height /= ratio;
              } else {
                ratio = 1;
              }
              canvas.width = width; // 设置压缩后图片宽度
              canvas.height = height; // 设置压缩后图片高度
              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
              canvas.toBlob(
                (blob) => {
                  resolve(new File([blob], file.name, { type: "image/jpeg", lastModified: Date.now() }));
                },
                "image/jpeg",
                0.9, // 压缩质量，0.9表示压缩率为90%
              );
            };
          };
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .upload__tip {
    padding: 12px 16px;
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 20px;
    padding-top: 0;
  }
</style>
