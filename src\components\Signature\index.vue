<template>
  <div>
    <van-popup :lazy-render="false" :value="showPop" position="bottom" round @click-overlay="closePop">
      <div class="popup-header">
        <div class="header-title">电子签名</div>
        <van-icon name="cross" size="20" color="#c0c4cc" @click="closePop" />
      </div>
      <div class="popup-content">
        <div style="border: 1px solid #eee" ref="canvasBox">
          <canvas id="canvasId" />
        </div>
        <div class="btn-box">
          <van-button type="default" block style="margin-right: 10px" @click="clearSignature">重签</van-button>
          <van-button type="info" block @click="saveSignature">确定</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
  import SignaturePad from "signature_pad";
  export default {
    name: "Signature",
    props: {
      showPop: {
        type: Boolean,
        default: false,
      },
      baseUrl: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        SignaturePad: null,
        config: {
          penColor: "#000000", //笔刷颜色
          minWidth: 3, //最小宽度
        },
      };
    },
    watch: {
      showPop(newV) {
        if (newV) {
          this.$nextTick(() => {
            this.getCanvas();
          });
        }
      },
    },
    mounted() {},
    methods: {
      // 关闭弹窗
      closePop() {
        this.$emit("closePop");
      },
      getCanvas() {
        let canvas = document.getElementById("canvasId");
        this.signaturePad = new SignaturePad(canvas, this.config);
        canvas.height = 200;
        canvas.width = this.$refs.canvasBox.clientWidth;
        if (this.baseUrl) {
          this.signaturePad.fromDataURL(this.baseUrl, {
            width: this.$refs.canvasBox.clientWidth,
            height: 200,
          });
        }
      },
      // 确定签名
      saveSignature() {
        if (this.signaturePad.isEmpty()) {
          this.$toast.fail("电子签名不能为空");
        } else {
          this.$emit("setSign", this.signaturePad.toDataURL());
        }
      },
      // 重签
      clearSignature() {
        this.signaturePad.clear();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .popup-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    .header-title {
      flex: 1;
      text-align: center;
    }
  }
  .popup-content {
    padding: 10px 16px;
  }
  .btn-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
  }
</style>
