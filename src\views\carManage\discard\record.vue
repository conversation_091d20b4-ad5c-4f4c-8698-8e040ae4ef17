<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicleDiscard/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "operatorName",
                value: "经办人",
              },
            ],
          },
          {
            title: "车辆报废信息",
            recordFieldArr: [
              {
                key: "organizationName",
                value: "报废单位",
              },
              {
                key: "costs",
                value: "报废金额",
                unit: "元",
              },
              {
                key: "discardTime",
                value: "报废日期",
              },
              {
                key: "vehicleFileList",
                value: "凭证",
                isImage: true,
              },
            ],
          },
        ],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped></style>
