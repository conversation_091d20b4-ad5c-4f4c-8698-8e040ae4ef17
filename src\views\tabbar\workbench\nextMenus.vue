<template>
  <menus-grid :menusData="menusData"></menus-grid>
</template>

<script>
  import menusGrid from "./components/menusGrid.vue";
  export default {
    components: {
      menusGrid,
    },
    data() {
      return {
        menusData: [],
      };
    },
    created() {
      let info = {};
      try {
        info = JSON.parse(localStorage.getItem("nextMenus"));
      } catch (error) {
        info = {};
      }
      document.title = info.name;
      this.menusData = info.menus;
    },
  };
</script>

<style lang="scss" scoped></style>
