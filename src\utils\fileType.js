const utils = {};
utils.isEmptyString = function (s) {
  if (!s) {
    return true;
  }
  if (s == "") {
    return true;
  }
  return false;
};
utils.isObject = function (OBJECT) {
  return OBJECT && typeof OBJECT == "object";
};
utils.isArray = function (array) {
  return Array.isArray(array);
};
// const docTypes={
//     "txt":true,"rtf":true,"pdf":true,"chm":true,"ltx":true,"inf":true,//通用文档
//     "xml":true,"xsl":true,"sdxf":true,
//     "csv":true,"tab":true,"dif":true,
//     "doc":true,"docx":true,"dot":true,"dotx":true,"docm":true,"dotm":true,"xps":true,"wtf":true,//word
//     "ppt":true,"pptx":true,"potx":true,"pot":true,"ppsx":true,"pps":true,"pptm":true,"potm":true,"ppsm":true,//ppt
//     "xls":true,"xlsx":true,"xltx":true,"xlt":true,"xlsm":true,"xlsb":true,"xltm":true,"xlam":true,"xla":true,//excel
//     "pst":true,"olm":true,//outlook
//     "wps":true,"wpt":true,"et":true,"ett":true,"dps":true,"dpt":true,//wps
//     "odt":true,"odp":true,"ods":true,//openOffice
//     "prn":true,"slk":true,
//     "html":true,"htm":true,"mht":true,"mhtml":true,"shtm":true,"xhtm":true,//网页
//     "c":true,"cpp":true,"json":true,"js":true,"css":true,"java":true,"s":true,"swift":true,"m":true,
//     "py":true,"rb":true,"go":true,"pas":true,"cob":true,"asp":true,"cgi":true,"pl":true,"php":true,
//     "jsp":true,"jspx":true,"sh":true
// }
const wordType = {
  doc: true,
  docx: true,
  dot: true,
  dotx: true,
  docm: true,
  dotm: true,
  xps: true,
  wtf: true,
};
const pptType = {
  ppt: true,
  pptx: true,
  potx: true,
  pot: true,
  ppsx: true,
  pps: true,
  pptm: true,
  potm: true,
  ppsm: true,
};
const excelType = {
  xls: true,
  xlsx: true,
  xltx: true,
  xlt: true,
  xlsm: true,
  xlsb: true,
  xltm: true,
  xlam: true,
  xla: true,
};
const pictureTypes = {
  jpg: true,
  jpeg: true,
  png: true,
  bmp: true,
  gif: true,
  tif: true,
  tiff: true,
  psd: true,
  sai: true,
  webp: true,
  raw: true,
  ico: true,
  cgm: true,
  dxf: true,
  dwg: true,
  emf: true,
  svg: true,
  wmf: true,
};
const audioTypes = {
  mp3: true,
  aac: true,
  ape: true,
  flac: true,
  ogg: true,
  ra: true,
  rma: true,
  pcm: true,
  m3u: true,
  m3u8: true,
  wma: true,
  asf: true,
  mid: true,
  midi: true,
  rmi: true,
  wav: true,
  amr: true,
  cda: true,
  vqf: true,
  tvq: true,
  mod: true,
  aiff: true,
  au: true,
  m4a: true,
};
const videoTypes = {
  mpeg: true,
  mpg: true,
  mp4: true,
  avi: true,
  mov: true,
  "3gp": true,
  mkv: true,
  wmv: true,
  asf: true,
  divx: true,
  xvid: true,
  rmvb: true,
  rm: true,
  flv: true,
  f4v: true,
  vob: true,
  dat: true,
  mpe: true,
  swf: true,
  qt: true,
  qtx: true,
};
const zipTypes = {
  zip: true,
  rar: true,
  "7z": true,
  ar: true,
  bz: true,
  bz2: true,
  gz: true,
  tar: true,
};
utils.fileExtension = function (value) {
  if (typeof value != "string") {
    return "";
  }
  var index = value.lastIndexOf(".");
  if (index == -1) {
    return "";
  }
  value = value.slice(index + 1);
  index = value.indexOf("?");
  if (index != -1) {
    value = value.slice(0, index);
  }
  return value;
};
utils.fileType = function (type) {
  type = type.toLocaleLowerCase();
  if (typeof type != "string") {
    return "unknown";
  }
  if (pictureTypes[type]) {
    return "picture";
  }
  if (audioTypes[type]) {
    return "audio";
  }
  if (videoTypes[type]) {
    return "video";
  }
  if (zipTypes[type]) {
    return "zip";
  }
  if (wordType[type]) {
    return "word";
  }
  if (pptType[type]) {
    return "ppt";
  }
  if (excelType[type]) {
    return "excel";
  }
  if (type == "pdf") {
    return "pdf";
  }
  if (type == "txt") {
    return "txt";
  }
  // if(docTypes[type]){return "document"}
  return "unknown";
};
utils.documentType = function (type) {
  type = type.toLocaleLowerCase();
  if (typeof type != "string") {
    return "unknown";
  }
  if (type == "pdf") {
    return "pdf";
  }
  if (wordType[type]) {
    return "word";
  }
  if (pptType[type]) {
    return "ppt";
  }
  if (excelType[type]) {
    return "excel";
  }
  return "unknown";
};
utils.fileSizeConvent = function (size) {
  if (typeof size == "string") {
    size = parseInt(size);
  }
  if (typeof size != "number") {
    return "";
  }
  if (size < 1024) {
    return size + "B";
  }
  size /= 1024;
  if (size < 1024) {
    return size.toFixed(2) + "KB";
  }
  size /= 1024;
  if (size < 1024) {
    return size.toFixed(2) + "MB";
  }
  size /= 1024;
  return size.toFixed(2) + "GB";
};

export default utils;
