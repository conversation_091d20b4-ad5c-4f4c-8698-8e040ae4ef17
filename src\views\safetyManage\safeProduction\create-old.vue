<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <van-tabs v-model="step" animated>
        <van-tab title="">
          <div class="create-box">
            <div class="create-content">
              <main class="create-main">
                <div class="create-title">基础信息</div>
                <van-field
                  v-model="ruleForm.activityName"
                  name="activityName"
                  label="活动名称"
                  placeholder="请输入活动名称"
                  input-align="right"
                  error-message-align="right"
                  :border="false"
                  :rules="rules.activityName"
                  required
                  maxlength="30"
                />
                <van-field
                  v-model="ruleForm.typeName"
                  name="type"
                  label="活动类型"
                  placeholder="请选择活动类型"
                  readonly
                  input-align="right"
                  error-message-align="right"
                  is-link
                  :border="false"
                  :rules="rules.type"
                  required
                  @click="showTypePicker = true"
                />
                <van-field
                  name="number"
                  label="参与人数"
                  input-align="right"
                  error-message-align="right"
                  :border="false"
                  :rules="rules.number"
                  required
                >
                  <template #input>
                    <van-stepper v-model="ruleForm.number" integer min="0" max="300" />
                  </template>
                </van-field>
                <van-field
                  v-model="ruleForm.personInCharge"
                  name="personInCharge"
                  label="负责人"
                  placeholder="请输入负责人"
                  input-align="right"
                  error-message-align="right"
                  :border="false"
                  :rules="rules.personInCharge"
                  required
                  maxlength="10"
                />
              </main>
            </div>
            <footer class="create-footer">
              <van-button
                native-type="button"
                class="round-32"
                block
                type="info"
                color="#4CA786"
                @click="handleStepClick"
                >下一步</van-button
              >
            </footer>
          </div>
        </van-tab>
        <van-tab title="">
          <div class="create-box">
            <header class="create-header">
              <div class="header-title active"><div class="header-sort">①</div><div>活动信息</div></div>
              <div class="header-title"><div class="header-sort">②</div><div>附件</div></div>
            </header>
            <div class="create-content">
              <main class="create-main">
                <van-field
                  v-model="ruleForm.activeDate"
                  name="activeDate"
                  label="活动日期"
                  placeholder="请选择活动日期"
                  readonly
                  input-align="right"
                  error-message-align="right"
                  is-link
                  :border="false"
                  :rules="rules.activeDate"
                  required
                  @click="openDatePicker('activeDate')"
                />
                <van-field
                  v-model="ruleForm.address"
                  name="address"
                  label="活动地点"
                  placeholder="请输入活动地点"
                  input-align="right"
                  error-message-align="right"
                  :border="false"
                  :rules="rules.address"
                  required
                  maxlength="50"
                />
                <van-field name="description" label="活动内容" label-width="100%" :border="false" />
                <van-field
                  v-model="ruleForm.description"
                  name="description"
                  label=""
                  placeholder="请输入活动内容"
                  type="textarea"
                  :border="false"
                  :autosize="{ maxHeight: 200, minHeight: 100 }"
                  :maxlength="500"
                  show-word-limit
                />
              </main>
            </div>
            <footer class="create-footer">
              <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="step--"
                >返回</van-button
              >
              <van-button
                native-type="button"
                class="round-22 ml-11"
                block
                type="info"
                color="#4CA786"
                @click="handleStepClick"
                >下一步</van-button
              >
            </footer>
          </div>
        </van-tab>
        <van-tab title="">
          <div class="create-box">
            <header class="create-header">
              <div class="header-title"><div class="header-sort">①</div><div>活动信息</div></div>
              <div class="header-title active"><div class="header-sort">②</div><div>附件</div></div>
            </header>
            <div class="create-content">
              <main class="create-main">
                <van-field :border="false" label-width="100%">
                  <template #label>
                    <div class="label-box">
                      <div class="label-title">活动附件</div>
                      <div class="label-text">(请上传压缩文件)</div>
                    </div>
                  </template>
                </van-field>
                <imageUpload
                  v-model="ruleForm.fileList"
                  :isRequired="false"
                  :maxNumber="1"
                  formId="fileList"
                  acceptString=".zip,.rar"
                  formPlaceHolder="请上传活动附件"
                ></imageUpload>
              </main>
            </div>
            <footer class="create-footer">
              <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="step--"
                >返回</van-button
              >
              <van-button
                native-type="button"
                class="round-22 ml-11"
                block
                type="info"
                color="#4CA786"
                @click="submitFormThrottling"
                >提交</van-button
              >
            </footer>
          </div>
        </van-tab>
      </van-tabs>
    </van-form>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="SAFEPRODUCTION_TYPE"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import { SAFEPRODUCTION_TYPE } from "@/enums";
  export default {
    components: {
      imageUpload,
    },
    data() {
      return {
        step: 0, //新增步骤
        submitFormThrottling: () => {},
        apis: {
          create: "/api/safety/safetyproduction/create",
        },
        showTypePicker: false, //活动类型下拉框
        SAFEPRODUCTION_TYPE,
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(),
        ruleForm: {
          activityName: "", //活动名称
          type: "", //活动类型
          number: "", //参与人数
          personInCharge: "", //负责人
          activeDate: moment(new Date()).format("YYYY-MM-DD"), //活动日期
          address: "", //活动地点
          description: "", //活动内容
          fileList: [],
        },
        rules: {
          activityName: [{ required: true, message: "请输入活动名称" }],
          type: [{ required: true, message: "请选择活动类型" }],
          number: [{ required: true, message: "请输入参与人数" }],
          personInCharge: [{ required: true, message: "请输入负责人" }],
          activeDate: [{ required: true, message: "请选择活动日期" }],
          address: [{ required: true, message: "请输入活动地点" }],
        },
        validateList: [
          ["activityName", "type", "number", "personInCharge"],
          ["activeDate", "address"],
        ],
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {},
    methods: {
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择自检类型
      onTypeConfirm(item, index) {
        this.ruleForm.typeName = item;
        this.ruleForm.type = index;
        this.showTypePicker = false;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 步骤点击事件
      handleStepClick() {
        this.$refs.formCreate
          .validate(this.validateList[this.step])
          .then(() => {
            this.step++;
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = this.ruleForm;
            if (this.ruleForm.fileList.length > 0) {
              params.fileUrl = JSON.stringify(this.ruleForm.fileList[0]);
            } else {
              params.fileUrl = "";
            }
            try {
              let res = await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增安全生产记录成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
