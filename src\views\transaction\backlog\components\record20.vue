<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              :value="createTime"
              label="上报时间"
              placeholder="请输入上报时间"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="deadline"
              label="截止日期"
              placeholder="请输入截止日期"
              input-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="baseForm.name"
              label="点位名称"
              placeholder="请输入点位名称"
              input-align="right"
              :border="false"
              required
              readonly
            />
            <van-field
              :value="baseForm.phone"
              label="点位联系人联系电话"
              placeholder="请输入点位联系人联系电话"
              input-align="right"
              :border="false"
              required
              readonly
              label-width="150"
            />
            <van-field
              :value="`${baseForm.province}/${baseForm.cityName}/${baseForm.districtName}`"
              label="省市区"
              placeholder="请输入省市区"
              input-align="right"
              :border="false"
              required
              readonly
            />
            <van-field
              :value="baseForm.address"
              label="详细地址"
              placeholder="请输入详细地址"
              input-align="right"
              :border="false"
              required
              readonly
            />
            <van-field
              :value="baseForm.email"
              label="联系人电子邮箱"
              placeholder="请输入联系人电子邮箱"
              input-align="right"
              :border="false"
              readonly
              label-width="120"
            />
            <van-field
              :value="baseForm.startDate"
              label="开始日期"
              placeholder="请输入开始日期"
              input-align="right"
              :border="false"
              required
              readonly
            />
            <van-field
              :value="baseForm.endDate"
              label="结束日期"
              placeholder="请输入结束日期"
              input-align="right"
              :border="false"
              required
              readonly
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="业务调整信息"></baseTitle>
            <van-field label="经营状态" input-align="right" error-message-align="right" :border="false" required>
              <template #input>
                <el-switch
                  v-model="ruleForm.status"
                  active-text="正常"
                  inactive-text="暂停营业"
                  active-color="#4CA786"
                  :active-value="0"
                  :inactive-value="1"
                >
                </el-switch>
              </template>
            </van-field>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >完成</van-button
          >
        </footer>
      </div>
    </van-form>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import componentMinxins from "./mixins";
  export default {
    mixins: [componentMinxins],
    components: {
      baseTitle,
    },
    data() {
      return {
        submitFormThrottling: () => {},
        apis: {
          create: "/api/supplierAlter/handle",
          info: "/api/supplierAlter/get/",
        },
        baseForm: {}, //业务基础信息
        ruleForm: {
          status: 0, //移除状态 0-否 1-移除
        },
        rules: {
          status: [{ required: true, message: "请选择移除状态" }],
        },
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.baseForm = res.data;
          this.baseForm.phone = this.baseForm.phone ? this.$sm2Decrypt(this.baseForm.phone) : "";
        }
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun({ ...this.ruleForm, id: this.id }, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "操作成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-flex {
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .warning-text {
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      text-align: center;
      margin-top: 10px;
    }
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .tips-icon {
    font-size: 50px;
    color: #ff7d00;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
</style>
