<template>
  <div class="container">
    <ul class="list" v-if="tableData.length > 0">
      <li class="list-item" v-for="item in tableData" :key="item.id">
        <div class="item-title">车牌号：{{ item.plateNumber }}</div>
        <div class="item-text">定位时间：{{ item.positionTime }}</div>
        <div class="item-text">速度（km/h）：{{ item.speed }}</div>
        <div class="item-text">里程数：{{ item.mileage }}</div>
        <div class="item-text">车辆状态：{{ DRIVING_CAR_STATUS[item.status] }}</div>
        <div class="item-text">详细地址：{{ item.address }}</div>
        <div class="item-text">经度：{{ item.longitude }}</div>
        <div class="item-text">纬度：{{ item.latitude }}</div>
      </li>
    </ul>
    <van-empty description="暂无数据" v-else />
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import { DRIVING_CAR_STATUS } from "@/enums";
  export default {
    data() {
      return {
        apis: {
          list: "/api/vehicle/drivingRecord/list",
        },
        tableData: [],
        DRIVING_CAR_STATUS,
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await createApiFun(this.$route.query, this.apis.list);
        if (res.success) {
          this.tableData = res.data;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    padding: 12px;
    overflow: auto;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
</style>
