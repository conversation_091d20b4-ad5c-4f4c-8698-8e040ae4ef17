<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.name"
              name="name"
              label="点位名称"
              placeholder="请输入点位名称"
              input-align="right"
              error-message-align="right"
              required
              maxlength="32"
              :border="false"
              :rules="rules.name"
            />
            <van-field
              v-model="ruleForm.code"
              name="code"
              label="点位编号"
              placeholder="请输入点位编号"
              input-align="right"
              error-message-align="right"
              required
              maxlength="32"
              :border="false"
              :rules="rules.code"
            />
            <van-field
              :value="ruleForm.regionName"
              name="region"
              label="省市区"
              placeholder="请选择省市区"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.region"
              required
              @click="showRegionPicker = true"
            />
            <van-field
              v-model="ruleForm.address"
              label="详细地址"
              placeholder="请输入详细地址"
              input-align="right"
              error-message-align="right"
              maxlength="255"
              :border="false"
            />
            <van-field label="经度" input-align="right" error-message-align="right" :border="false">
              <template #input>
                <van-stepper
                  v-model="ruleForm.longitude"
                  min="0"
                  :decimal-length="6"
                  :default-value="''"
                  input-width="120px"
                />
              </template>
            </van-field>
            <van-field label="纬度" input-align="right" error-message-align="right" :border="false">
              <template #input>
                <van-stepper
                  v-model="ruleForm.latitude"
                  min="0"
                  :decimal-length="6"
                  :default-value="''"
                  input-width="120px"
                />
              </template>
            </van-field>
            <div class="location-icon">
              <div class="flex-h" @click="openPopup(3)">
                <van-icon name="location-o" size="20" color="#909399" />
                <div class="location-text">定位调整</div>
              </div>
            </div>
            <van-field
              :value="POINT_TYPE[ruleForm.type]"
              name="type"
              label="点位类型"
              placeholder="请选择点位类型"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.type"
              required
              @click="showTypePicker = true"
            />
            <van-field
              :value="POINT_RECEIVING_METHOD[ruleForm.baggingMethod]"
              name="baggingMethod"
              label="点位收运方式"
              placeholder="请选择点位收运方式"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.baggingMethod"
              required
              @click="showBaggingMethodPicker = true"
            />
            <van-field
              :value="ruleForm.pickupPathName"
              label="所属路线"
              placeholder="请选择所属路线"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              @click="openPopup(2)"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="联络信息"></baseTitle>
            <van-field
              v-model="ruleForm.contact"
              label="联系人名称"
              placeholder="请输入联系人名称"
              input-align="right"
              error-message-align="right"
              maxlength="32"
              :border="false"
            />
            <van-field
              v-model="ruleForm.contactPhone"
              label="联系电话"
              placeholder="请输入联系电话"
              input-align="right"
              error-message-align="right"
              maxlength="11"
              :border="false"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="产废信息"></baseTitle>
            <van-field
              label="日排放量（KG）"
              input-align="right"
              error-message-align="right"
              :border="false"
              label-width="120px"
            >
              <template #input>
                <van-stepper
                  v-model="ruleForm.dailyEmissions"
                  min="0"
                  :decimal-length="2"
                  :default-value="''"
                  input-width="100px"
                />
              </template>
            </van-field>
            <van-field
              label="月排放量（KG）"
              input-align="right"
              error-message-align="right"
              :border="false"
              label-width="120px"
            >
              <template #input>
                <van-stepper
                  v-model="ruleForm.monthEmissions"
                  min="0"
                  :decimal-length="2"
                  :default-value="''"
                  input-width="100px"
                />
              </template>
            </van-field>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="收运信息"></baseTitle>
            <van-field
              :value="COLLECTION_CYCLE[ruleForm.period]"
              name="period"
              label="点位收运周期"
              placeholder="请选择点位收运周期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.period"
              required
              @click="showPeriodPicker = true"
            />
            <van-field
              label="点位收运频次"
              name="period"
              input-align="right"
              error-message-align="right"
              :border="false"
              label-width="120px"
              :rules="rules.frequency"
              required
            >
              <template #input>
                <van-stepper v-model="ruleForm.frequency" min="1" integer input-width="100px" />
              </template>
            </van-field>
          </main>
          <template v-if="ruleForm.type || ruleForm.type === 0">
            <div class="border-line"></div>
            <main class="create-main">
              <baseTitle title="关联信息"></baseTitle>
              <van-field
                :value="ruleForm.holdingTankDeviceName"
                label="关联收集柜"
                placeholder="请选择关联收集柜"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                @click="openPopup(0)"
                v-if="ruleForm.type === 2"
              />
              <van-field
                :value="ruleForm.merchantFileName"
                label="关联客商"
                placeholder="请选择关联客商"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                @click="openPopup(1)"
                v-else-if="[0, 1, 3].includes(ruleForm.type)"
              />
            </main>
          </template>
        </div>
        <footer class="create-footer">
          <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="cancel"
            >取消</van-button
          >
          <van-button
            native-type="button"
            class="round-22 ml-11"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >保存</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showRegionPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="label"
        :columns="regionOptions"
        @confirm="onRegionConfirm"
        @cancel="showRegionPicker = false"
      />
    </van-popup>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="POINT_TYPE"
        :default-index="defaultTypeIndex"
        @confirm="onTypefirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>
    <van-popup v-model="showPeriodPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="COLLECTION_CYCLE"
        :default-index="defaultPeriodIndex"
        @confirm="onPeriodfirm"
        @cancel="showPeriodPicker = false"
      />
    </van-popup>
    <van-popup v-model="showBaggingMethodPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="POINT_RECEIVING_METHOD"
        :default-index="defaultBaggingMethodIndex"
        @confirm="onBaggingMethodfirm"
        @cancel="showBaggingMethodPicker = false"
      />
    </van-popup>
    <van-popup v-model="showPopup" position="right">
      <template v-if="showPopup">
        <div class="popup-container">
          <holdingTank
            v-if="popupType === 0"
            :id="ruleForm.holdingTankId"
            @closePopup="showPopup = false"
            @selectItem="selectItem"
          ></holdingTank>
          <merchantFile
            v-if="popupType === 1"
            :id="ruleForm.merchantFileId"
            @closePopup="showPopup = false"
            @selectItem="selectItem"
          ></merchantFile>
          <route
            v-if="popupType === 2"
            :id="ruleForm.pickupPathId"
            @closePopup="showPopup = false"
            @selectItem="selectItem"
          ></route>
          <pointAdjust
            v-if="popupType === 3"
            :form-item="ruleForm"
            @closePointAdjust="showPopup = false"
            @savePointAdjust="savePointAdjust"
          ></pointAdjust>
        </div>
      </template>
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun, getInfoApiFunByParams, updateApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import baseTitle from "@/components/baseTitle";
  import { POINT_TYPE, COLLECTION_CYCLE, POINT_RECEIVING_METHOD } from "@/enums";
  import holdingTank from "./components/holdingTank.vue";
  import merchantFile from "./components/merchantFile.vue";
  import route from "./components/route.vue";
  import pointAdjust from "./components/pointAdjust.vue";
  export default {
    components: {
      baseTitle,
      holdingTank,
      merchantFile,
      route,
      pointAdjust,
    },
    data() {
      return {
        POINT_TYPE,
        COLLECTION_CYCLE,
        POINT_RECEIVING_METHOD,
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/pickup/pickupPoint/create",
          update: "/api/pickup/pickupPoint/update",
          info: "/api/pickup/pickupPoint/get/",
          regionList: "/api/region/regionList",
          pathList: "/api/pickup/pickupPath/list",
        },
        ruleForm: {
          name: "", //点位名称
          code: "", //点位编号
          region: [], //省市区
          regionName: "", //省市区名称
          address: "", //详细地址
          longitude: "", //经度
          latitude: "", //纬度
          type: "", //点位类型
          pickupPathId: "", //所属路线
          pickupPathName: "", //所属路线名称
          contact: "", //联系人名称
          contactPhone: "", //联系电话
          dailyEmissions: "", //日排放量
          monthEmissions: "", //月排放量
          merchantFileId: "", //关联客商
          merchantFileName: "", //关联客商
          holdingTankId: "", //关联收集柜
          holdingTankDeviceName: "",
          period: 1, //点位收运周期
          frequency: 1, //点位收运频次
          baggingMethod: "", //点位收运方式
        },
        rules: {
          name: [{ required: true, message: "请输入点位名称" }],
          code: [{ required: true, message: "请输入点位编号" }],
          region: [{ required: true, message: "请选择省市区" }],
          type: [{ required: true, message: "请选择点位类型" }],
          period: [{ required: true, message: "请选择点位收运周期" }],
          frequency: [{ required: true, message: "请输入点位收运频次" }],
          baggingMethod: [{ required: true, message: "请选择点位收运方式" }],
        },
        id: "",
        showRegionPicker: false,
        regionOptions: [
          {
            value: "440000000000",
            label: "广东省",
            children: [{ value: "440100000000", label: "广州市", children: [] }],
          },
        ],
        showTypePicker: false,
        defaultTypeIndex: 0,
        showPeriodPicker: false,
        defaultPeriodIndex: 0,
        showPopup: false,
        popupType: 0,
        showBaggingMethodPicker: false,
        defaultBaggingMethodIndex: 0,
      };
    },
    created() {
      this.id = this.$route.query.id || "";
      document.title = `${this.id ? "编辑" : "新增"}收运点位档案`;
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    async mounted() {
      await this.getOptions();
      if (this.id) {
        this.getRecord();
      }
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.contactPhone = this.ruleForm.contactPhone ? this.$sm2Decrypt(this.ruleForm.contactPhone) : "";
          this.ruleForm.regionName = `${this.ruleForm.provinceName} / ${this.ruleForm.cityName} / ${this.ruleForm.districtName}`;
          this.ruleForm.region = ["440000000000", "440100000000", this.ruleForm.districtId];
          let defaultRegionIndex = this.regionOptions[0].children[0].children.findIndex(
            (o) => o.value == this.ruleForm.districtId,
          );
          if (defaultRegionIndex > 0) {
            this.regionOptions[0].children[0].defaultIndex = defaultRegionIndex;
          }
          let defaultTypeIndex = POINT_TYPE.findIndex((_, index) => index === this.ruleForm.type);
          if (defaultTypeIndex > 0) {
            this.defaultTypeIndex = defaultTypeIndex;
          }
          let defaultPeriodIndex = COLLECTION_CYCLE.findIndex((_, index) => index === this.ruleForm.period);
          if (defaultPeriodIndex > 0) {
            this.defaultPeriodIndex = defaultPeriodIndex;
          }
          let defaultBaggingMethodIndex = POINT_RECEIVING_METHOD.findIndex(
            (_, index) => index === this.ruleForm.baggingMethod,
          );
          if (defaultBaggingMethodIndex > 0) {
            this.defaultBaggingMethodIndex = defaultBaggingMethodIndex;
          }
        }
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [getInfoApiFunByParams({ pid: "440100000000" }, this.apis.regionList)];
        let res = await Promise.all(promiseList);
        this.regionOptions[0].children[0].children = res[0].data.map((item) => {
          return {
            value: item.code,
            label: item.name,
          };
        });
      },
      // 选择省市区
      onRegionConfirm(item, index) {
        this.ruleForm.regionName = `${item[0]} / ${item[1]} / ${item[2]}`;
        this.ruleForm.region = [
          "440000000000",
          "440100000000",
          this.regionOptions[0].children[0].children[index[2]].value,
        ];
        this.showRegionPicker = false;
      },
      // 选择点位类型
      onTypefirm(_, index) {
        this.ruleForm.type = index;
        this.showTypePicker = false;
      },
      // 选择收运周期
      onPeriodfirm(_, index) {
        this.ruleForm.period = index;
        this.showPeriodPicker = false;
      },
      // 选择收运方式
      onBaggingMethodfirm(_, index) {
        this.ruleForm.baggingMethod = index;
        this.showBaggingMethodPicker = false;
      },
      // 打开右侧弹窗
      openPopup(type) {
        this.popupType = type;
        this.showPopup = true;
      },
      // 选择列表项事件
      selectItem(obj) {
        switch (obj.type) {
          case 0:
            this.ruleForm.holdingTankId = obj.id;
            this.ruleForm.holdingTankDeviceName = obj.name;
            break;
          case 1:
            this.ruleForm.merchantFileId = obj.id;
            this.ruleForm.merchantFileName = obj.name;
            break;
          case 2:
            this.ruleForm.pickupPathId = obj.id;
            this.ruleForm.pickupPathName = obj.name;
            break;
        }
        this.showPopup = false;
      },
      // 保存地址、经纬度
      savePointAdjust(item) {
        if (item) {
          this.ruleForm.address = item.address;
          this.ruleForm.longitude = item.location.lng;
          this.ruleForm.latitude = item.location.lat;
        }
        this.showPopup = false;
      },
      // 取消
      cancel() {
        this.$commonBack();
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = {
              ...this.ruleForm,
              provinceId: this.ruleForm.region[0],
              cityId: this.ruleForm.region[1],
              districtId: this.ruleForm.region[2],
            };
            try {
              let res = this.id
                ? await updateApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.update)
                : await createApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: `${this.id ? "编辑" : "新增"}收运点位档案成功`,
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .location-icon {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px 16px;
    .location-text {
      color: var(--color-primary);
    }
  }
  .popup-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
