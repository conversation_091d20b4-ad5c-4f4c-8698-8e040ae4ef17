<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate" v-show="!showPointAdjust">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <van-field
              v-model="ruleForm.exceptionTypeName"
              name="exceptionType"
              label="异常类型"
              placeholder="请选择异常类型"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.exceptionType"
              required
              @click="showTypePicker = true"
            />
            <van-field
              name="continueCarrying"
              label="是否还能继续收运"
              input-align="right"
              error-message-align="right"
              label-width="130"
              required
              :border="false"
              :rules="rules.continueCarrying"
            >
              <template #input>
                <van-radio-group v-model="ruleForm.continueCarrying" direction="horizontal" checked-color="#4CA786">
                  <van-radio :name="1">是</van-radio>
                  <van-radio :name="0">否</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="ruleForm.address"
              name="address"
              label="上报地址"
              placeholder="点击按钮获取当前定位地址"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="[{ required: true, message: '请填写上报地址' }]"
              required
              readonly
              @click-right-icon="showPointAdjust = true"
            >
              <template #right-icon>
                <span class="el-icon-map-location location-icon"></span>
              </template>
            </van-field>
            <van-field
              v-model="ruleForm.exceptionContent"
              name="exceptionContent"
              label="异常描述"
              placeholder="填写异常描述"
              input-align="right"
              error-message-align="right"
              type="textarea"
              required
              show-word-limit
              :border="false"
              :autosize="{ maxHeight: 200, minHeight: 100 }"
              :maxlength="500"
              :rules="rules.exceptionContent"
            />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-22"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker show-toolbar :columns="ABNORMAL_TYPE" @confirm="onTypeConfirm" @cancel="showTypePicker = false" />
    </van-popup>
    <pointAdjust
      v-if="showPointAdjust"
      :lng="ruleForm.longitude"
      :lat="ruleForm.latitude"
      :isSave="isSave"
      @closePointAdjust="closePointAdjust"
      @savePointAdjust="savePointAdjust"
    ></pointAdjust>
  </div>
</template>

<script>
  import { ABNORMAL_TYPE } from "@/enums";
  import { createApiFun } from "@/api/base";
  import pointAdjust from "@/views/scheduling/electronicWaybill/components/pointAdjust";
  export default {
    components: {
      pointAdjust,
    },
    data() {
      return {
        submitFormThrottling: () => {},
        apis: {
          create: "/api/abnormalreporting/save",
        },
        ruleForm: {
          exceptionType: "", //异常类型
          exceptionTypeName: "", //异常类型
          continueCarrying: 1, //是否能继续收运
          exceptionContent: "", //异常描述
          address: "", //上报地址
          longitude: "", //经度
          latitude: "", //纬度
        },
        rules: {
          exceptionType: [{ required: true, message: "请选择异常类型" }],
          continueCarrying: [{ required: true, message: "请选择是否还能继续收运" }],
          exceptionContent: [{ required: true, message: "请填写异常描述" }],
          address: [{ required: true, message: "请填写上报地址" }],
        },
        ABNORMAL_TYPE,
        showTypePicker: false,
        showPointAdjust: false,
        isSave: false,
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    methods: {
      // 异常类型选择
      onTypeConfirm(item, index) {
        this.ruleForm.exceptionTypeName = item;
        this.ruleForm.exceptionType = index;
        this.showTypePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = JSON.parse(JSON.stringify(this.ruleForm));
            params.continueCarrying = params.continueCarrying === 1 ? true : false;
            try {
              let res = await createApiFun({ ...params, waybillId: this.$route.query.waybillId }, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "异常上报成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      // 关闭弹窗
      closePointAdjust() {
        this.showPointAdjust = false;
      },
      // 保存地址、经纬度
      savePointAdjust(item) {
        if (!this.isSave) {
          this.isSave = true;
        }
        if (item) {
          this.ruleForm.address = item.address;
          this.ruleForm.longitude = item.location.lng;
          this.ruleForm.latitude = item.location.lat;
        }
        this.showPointAdjust = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .location-icon {
    font-size: 16px;
    color: #909399;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
