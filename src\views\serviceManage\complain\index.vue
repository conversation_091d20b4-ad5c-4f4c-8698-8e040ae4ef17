<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="customerName"
    searchPlaceholder="请输入客商名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  import { HANDLING_STATUS } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "customerName",
            value: "客商名称",
          },
          {
            key: "complaintDate",
            value: "投诉日期",
          },
          {
            key: "handlingStatus",
            value: "处理情况",
            enums: HANDLING_STATUS,
          },
        ],
        apis: {
          listPage: "/api/complaintRecord/listPage",
        },
        filterList: [
          {
            type: "Date",
            key: "complaintDate",
            value: "投诉日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
          {
            type: "Options",
            key: "handlingStatus",
            value: "处理情况",
            enums: HANDLING_STATUS,
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("complainRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
