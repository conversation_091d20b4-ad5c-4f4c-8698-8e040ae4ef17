<template>
  <div class="container">
    <div v-html="txtData"></div>
  </div>
</template>

<script>
  export default {
    props: {
      url: {
        type: String,
        default: null,
      },
      fileType: [String, Number],
    },
    data() {
      return {
        txtData: "",
        fileUrl: null,
      };
    },
    created() {
      this.fileUrl = this.url || this.$route.query.url;
      if (this.fileType == "txt" || this.$route.query.fileType == "txt") {
        this.getTxt();
      }
    },
    methods: {
      getTxt() {
        fetch(this.fileUrl)
          .then((res) => res.text())
          .then((data) => {
            console.log("文档数据", data);
            this.txtData = data;
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 5px;
    background: #ffffff;
    font-size: 16px;
  }
</style>
