<template>
  <div class="page-container">
    <div class="page-main">
      <div class="main-title">首页</div>
      <div class="home-header">
        <img class="header-img" src="@/assets/images/index_bg.png" />
        <div v-if="userInfo">
          <div class="header-list" :class="`grid-list-${headerLevelObj[currentLevel].length}`" v-if="currentLevel">
            <div
              class="header-item"
              v-for="(item, index) in headerLevelObj[currentLevel]"
              :key="index"
              @click="headerClick(item)"
            >
              <div class="header-item-value">{{ item.count }}</div>
              <div class="header-item-title"
                >{{ item.name }}<span v-if="item.unit" class="header-unit">（{{ item.unit }}）</span></div
              >
            </div>
          </div>
          <div class="today-receive" v-if="todayReceiveLevel.includes(currentLevel)"
            >今日已收运量：{{ todayReceiveWeight }}（kg）</div
          >
        </div>
        <div v-else>
          <div class="login-btn" color="#ffffff" @click="login" round>点击登录</div>
          <div class="tips-text">展示更多详情数据</div>
        </div>
      </div>
      <!-- 工具栏 -->
      <div class="toolbar-box">
        <div class="toolbar-title">
          <span>常用工具</span>
          <div class="right" @click="checkTool"
            ><span>自定义</span>
            <van-icon name="arrow" size="16px" />
          </div>
        </div>
        <div class="grid-list" v-if="userInfo">
          <div class="grid-item" v-for="(item, index) in customList" :key="index" @click="handleRouteJump(item)">
            <!-- <img class="grid-item-image" mode="aspectFill" /> -->
            <van-image class="grid-item-image" fit="fill" :src="item.icon" />
            <div class="grid-item-text">{{ item.name }}</div>
          </div>
          <div class="grid-item" @click="checkTool">
            <img class="grid-item-image" src="@/assets/images/ic_more.png" />
            <div class="grid-item-text">更多</div>
          </div>
        </div>
        <div class="grid-list" v-else>
          <div class="grid-item">
            <img class="grid-item-image" src="@/assets/images/ic_more.png" />
            <div class="grid-item-text">更多</div>
          </div>
        </div>
        <div class="driver-tip flex-h" v-if="showTip === 0 && currentLevel == 190" @click="toSelfTest">
          <van-icon name="warning-o" color="#D9001B" size="30" />
          <div class="driver-tip-title">今日车前检未完成，请完成检查再进行收运！</div>
        </div>
        <div class="card-list" v-if="userInfo && currentLevel">
          <card-box title="电子收运单" v-if="currentLevel == 180 || currentLevel == 190" @clickRight="handleDriverMore">
            <driver :currentLevel="currentLevel"></driver>
          </card-box>
        </div>

        <div class="card-list" v-else>
          <card-box title="公司新闻" :isRight="false">
            <div class="news-list">
              <div class="news-item" v-for="(item, index) in newList" :key="index" @click="goNewsDetail(item)">
                <div class="flex-center-start">
                  <div class="sctmp-iconfont icon-ic_shengchanpeixun news-item-icon"></div>
                  <div class="news-item-title">{{ item.title }}</div>
                </div>
                <div class="news-item-time">创建日期: {{ item.createTime }}</div>
              </div>
            </div>
          </card-box>
        </div>
      </div>
    </div>
    <tabbar></tabbar>
    <van-popup v-model="showNotice" round z-index="3001">
      <div class="scroll-container">
        <div class="scroll-title">通知公告</div>
        <div class="scroll-content">
          <van-list
            v-model="scrollLoading"
            :finished="scrollFinished"
            @load="getNoticeListPage"
            :immediate-check="false"
          >
            <ul class="notice-list" v-if="noticeList.length > 0">
              <van-notice-bar
                color="#4CA786"
                background="#fff"
                left-icon="volume-o"
                :text="item.title"
                v-for="item in noticeList"
                :key="item.id"
                @click="viewNoticeDetail(item)"
              />
            </ul>
            <van-empty description="暂无收运记录数据" v-else />
          </van-list>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
  import tabbar from "@/components/tabbar";
  import { getUserInfo, getCurrentLevel } from "@/utils/storage";
  import cardBox from "./components/cardBox.vue";
  import driver from "./components/driver.vue";
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import moment from "moment";
  export default {
    components: {
      tabbar,
      cardBox,
      driver,
    },
    data() {
      return {
        userInfo: "",
        currentLevel: "",
        headerLevelObj: {
          // 领导
          110: [
            {
              name: "签约合同",
              unit: "月",
              count: 0,
              url: "contractInfo",
            },
            {
              name: "签约合同",
              unit: "天",
              count: 0,
              url: "contractInfo",
            },
            {
              name: "即将到期合同",
              count: 0,
              url: "contractInfo",
            },
          ],
          // 部门负责人
          120: [
            {
              name: "待办事项",
              count: 0,
              url: "backlog",
            },
            {
              name: "告警通知",
              count: 0,
              url: "emergency",
            },
            {
              name: "客服投诉",
              count: 0,
              url: "complain",
            },
          ],
          // 部门主管
          // 130: [
          //   {
          //     name: "待办事项",
          //     count: 15,
          //     url: "backlog",
          //   },
          //   {
          //     name: "告警通知",
          //     count: 15,
          //     url: "emergency",
          //   },
          //   {
          //     name: "客服投诉",
          //     count: 12,
          //   },
          // ],
          // 运营
          140: [
            {
              name: "待办事项",
              count: 0,
              url: "backlog",
            },
            {
              name: "告警通知",
              count: 0,
              url: "emergency",
            },
            {
              name: "超时点位",
              count: 0,
            },
            {
              name: "上报事件",
              count: 0,
            },
          ],
          // 小程序-押运工、司机首页待办事项、告警通知、新收运任务、上报时间统计完成接口对接
          180: [
            {
              name: "待办事项",
              count: 0,
              url: "backlog",
            },
            {
              name: "告警通知",
              count: 0,
              url: "emergency",
            },
            {
              name: "今日已收点位",
              count: 0,
            },
            {
              name: "待收点位",
              count: 0,
            },
          ],
          190: [
            {
              name: "待办事项",
              count: 0,
              url: "backlog",
            },
            {
              name: "告警通知",
              count: 0,
              url: "emergency",
            },
            {
              name: "今日已收点位",
              count: 0,
            },
            {
              name: "待收点位",
              count: 0,
            },
          ],
          // 客商
          199: [
            {
              name: "待收运",
              count: 0,
            },
          ],
        },
        showTip: 1,
        customList: [],
        todayReceiveWeight: 0,
        todayReceiveLevel: [120, 130, 140, 180, 190],
        leaderInfo: "",
        pageNo: 1,
        pageSize: 10,
        noticeList: [],
        showNotice: false,
        total: 0,
        hasShowNotice: false,
        newList: [],
        scrollLoading: false,
        scrollFinished: false,
      };
    },
    created() {
      this.initData();
    },
    methods: {
      async initData() {
        let userInfo = getUserInfo();
        let currentLevel = getCurrentLevel();
        if (userInfo) {
          if (userInfo.lgUnionId && currentLevel == 190) {
            let res = await getInfoApiFun(
              userInfo.lgUnionId,
              "/api/vehicle/inspect/countCurrDateSnokeweedInfoByDriverDossierId/",
            );
            this.showTip = res.data;
          }
          if (userInfo.userType == 0) {
            if (this.hasShowNotice) return;
            this.pageNo = 1;
            await this.getNoticeListPage();
            if (this.total > 0) {
              this.showNotice = true;
              this.hasShowNotice = true;
            }
          }
          this.getUseCustomList();
        } else {
          this.getNews();
        }
        this.userInfo = userInfo;
        this.currentLevel = currentLevel;
        this.getDataByCurrentLevel(currentLevel);
      },
      // 获取公告分页列表
      async getNoticeListPage() {
        if (this.pageNo === 1) {
          this.noticeList = [];
        }
        const date = moment().format("YYYY-MM-DD");
        // 请求接口分页列表数据
        try {
          let res = await createApiFun(
            {
              pageNo: this.pageNo,
              pageSize: this.pageSize,
              noticeType: 1,
              noticeDate: date,
            },
            "/api/notice/app/listPage",
          );
          if (res.success) {
            let dataList = res.data.datas;
            this.noticeList = this.noticeList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.noticeList.length >= this.total) {
              this.scrollFinished = true;
            }
          } else {
            this.scrollFinished = true;
          }
          this.scrollLoading = false;
        } catch (error) {
          this.scrollLoading = false;
        }
      },
      // 获取常用工具列表
      async getUseCustomList() {
        try {
          let res = await createApiFun("", "/api/custom/list");
          let menus = this.formatData(res.data);
          this.customList = menus;
        } catch (error) {
          console.log(error);
        }
      },
      //格式化图标数据
      formatData(list) {
        let newArr = [];
        list.forEach((item) => {
          let obj = {
            ...item,
            icon: item.icon ? JSON.parse(item.icon).url : null,
            checkedIcon: item.checkedIcon ? JSON.parse(item.checkedIcon).url : null,
            menus: [],
          };
          if (item.menus && item.menus.length) {
            obj.menus = this.formatData(item.menus);
          }
          newArr.push(obj);
        });
        return newArr;
      },
      async getNews() {
        let res = await createApiFun(
          {
            pageNo: 1,
            pageSize: 6,
            noticeType: 0,
          },
          "/api/notice/app/listPage",
        );
        let newList = res.data.datas;
        this.newList = newList;
      },
      getDataByCurrentLevel(currentLevel) {
        if (currentLevel == 110) {
          this.homepageInfoFun110();
        } else if (currentLevel == 120) {
          this.homepageInfoFun120();
        } else if (currentLevel == 140) {
          this.homepageInfoFun140();
        } else if (currentLevel == 180 || currentLevel == 190) {
          this.homepageInfoFun190(currentLevel);
        } else if (currentLevel == 199) {
          this.homepageInfoFun199();
        }
      },
      //客商首页统计数据
      async homepageInfoFun199() {
        try {
          let { data } = await getInfoApiFun("", "/api/homepage/info");
          this.headerLevelObj[199][0].count = data?.unCollectPointNum || 0;
        } catch (error) {
          console.log(error);
        }
      },
      // 小程序-司机首页待办事项、告警通知、新收运任务、上报时间统计完成接口对接
      async homepageInfoFun190(currentLevel) {
        try {
          let { data } = await getInfoApiFun("", "/api/homepage/info");
          this.headerLevelObj[currentLevel][0].count = data.todoNum;
          this.headerLevelObj[currentLevel][1].count = data.alarmNum;
          this.headerLevelObj[currentLevel][2].count = data.collectPointNum;
          this.headerLevelObj[currentLevel][3].count = data.unCollectPointNum;
          this.todayReceiveWeight = data.dayCollectNum;
        } catch (error) {
          console.log(error);
        }
      },
      // 运营
      async homepageInfoFun140() {
        try {
          let { data } = await getInfoApiFun("", "/api/homepage/info");
          this.headerLevelObj[140][0].count = data.todoNum;
          this.headerLevelObj[140][1].count = data.alarmNum;
          this.headerLevelObj[140][2].count = data.overdueNum;
          this.headerLevelObj[140][3].count = data.abnormalNum;
          this.todayReceiveWeight = data.dayCollectNum;
        } catch (error) {
          console.log(error);
        }
      },
      // 部门负责人
      async homepageInfoFun120() {
        try {
          let { data } = await getInfoApiFun("", "/api/homepage/info");
          this.headerLevelObj[120][0].count = data.todoNum;
          this.headerLevelObj[120][1].count = data.alarmNum;
          this.headerLevelObj[120][2].count = data.complaintNum;
          this.todayReceiveWeight = data.dayCollectNum;
        } catch (error) {
          console.log(error);
        }
      },
      // 领导
      async homepageInfoFun110() {
        try {
          let { data } = await getInfoApiFun("", "/api/homepage/info");
          this.headerLevelObj[110][0].count = data.monthSignNum;
          this.headerLevelObj[110][1].count = data.daySignNum;
          this.headerLevelObj[110][2].count = data.dueNum;
          this.leaderInfo = data;
        } catch (error) {
          console.log(error);
        }
      },
      headerClick(item) {
        let { url } = item;
        if (url) {
          this.$commonSkip(url);
        }
      },
      login() {
        this.$commonSkip("authority");
      },
      checkTool() {
        this.$commonSkip("toolbox");
      },
      handleRouteJump(item) {
        let { url, menus } = item;
        if (url) {
          this.$commonSkip(url);
        } else if (menus && menus.length) {
          localStorage.setItem("nextMenus", JSON.stringify(item));
          this.$commonSkip("nextMenus");
        } else {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "暂未开通该功能",
            duration: 1500,
          });
        }
      },
      handleDriverMore() {
        this.$commonSkip("electronicWaybill");
      },
      toSelfTest() {
        this.$commonSkip("selfTestCreate");
      },
      goNewsDetail(item) {
        window.open(item.link);
      },
      viewNoticeDetail(item) {
        let { id } = item;
        this.$commonSkip("noticeRecord", { id });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    .page-main {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }
  }
  .main-title {
    padding: 16px 0;
    text-align: center;
    font-size: 17px;
    color: #fff;
    line-height: 18px;
    font-weight: 600;
    background-color: #4ca786;
  }
  .home-header {
    position: relative;
    padding-top: 12px;
    height: 160px;
    .header-img {
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
      width: 100%;
      height: 160px;
    }
    .header-list {
      display: grid;
    }

    .grid-list-1 {
      grid-template-columns: repeat(1, 1fr);
    }

    .grid-list-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    .grid-list-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    .grid-list-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    .header-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      padding-top: 20px;
      text-align: center;
    }

    .header-item-value {
      font-weight: 600;
      font-size: 20px;
      color: #ffffff;
      line-height: 28px;
    }

    .header-unit {
      font-size: 10px !important;
    }

    .header-item-title {
      margin-top: 4px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
    }
    .today-receive {
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 18px;
      text-align: center;
      margin-top: 24px;
    }
    .login-btn {
      width: 120px;
      background: rgba(255, 255, 255, 0.85);
      border-radius: 32px;
      font-weight: 400;
      font-size: 16px;
      color: #4ca786;
      line-height: 44px;
      text-align: center;
      margin: 0 auto;
      margin-top: 16px;
    }
    .tips-text {
      text-align: center;
      color: #ffffff;
      margin-top: 5px;
      font-size: 12px;
      font-weight: 400;
    }
  }
  .toolbar-box {
    position: relative;
    width: 100%;
    background: linear-gradient(180deg, #e4fff6 0%, #f7faf9 100%);
    border-radius: 16px 17px 0px 0px;
    flex: 1;
    overflow-y: auto;
    margin-top: -29px;
  }

  .toolbar-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px 16px 12px;
    font-weight: bold;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
  }

  .toolbar-title .right {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: #939c99;
    line-height: 20px;
    text-align: right;
  }

  .card-list {
    padding: 12px;
    padding-top: 16px;
    background: transparent;
  }

  .grid-list {
    display: grid;
    row-gap: 24px;
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .grid-item-image {
    width: 32px;
    height: 32px;
    overflow: hidden;
  }

  .grid-item-text {
    font-weight: 400;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    text-align: center;
    margin-top: 4px;
  }

  .driver-tip {
    padding: 12px 24px 0 24px;
  }

  .driver-tip-title {
    font-size: 14px;
    color: #d9001b;
    font-weight: bold;
    margin-left: 6px;
    line-height: 20px;
  }
  .news-item {
    padding: 12px;
  }

  .news-item-icon {
    color: #fabb28;
    font-size: 20px;
  }

  .news-item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    margin-left: 4px;
  }

  .news-item-time {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
  }
  .scroll-container {
    width: 90vw;
    height: 35vh;
    display: flex;
    flex-direction: column;
  }
  .scroll-title {
    text-align: center;
    padding-top: 16px;
    font-size: 18px;
    font-weight: bold;
  }
  .scroll-content {
    flex: 1;
    overflow-y: auto;
  }
  .scroll-box {
    width: 100%;
    height: 100%;
    padding: 0 4px;
    box-sizing: border-box;
  }
  .notice-list {
    padding: 0 4px;
  }
</style>
