import axios from "axios";
import qs from "qs";
import { getToken } from "@/utils/storage";
import lgNotify from "@/utils/notify.js";
import { wxAdvanceNavigateTo } from "@/utils/wechatRouter";
import { parseUA } from "@/utils";
import store from "@/store";

var instance = axios.create({
  baseURL: "",
  timeout: 60000,
});

// 添加请求拦截器
instance.interceptors.request.use(
  async (config) => {
    let token = "";
    token = await getToken();
    if (token) {
      config.headers.Authorization = token.includes("Bearer") ? token.slice(1, token.length - 1) : "Bearer " + token;
    }
    config.cancelToken = new axios.CancelToken((cancel) => {
      store.dispatch("pushCancel", { cancelToken: cancel });
    });
    // 发送请求之前
    let enc = new TextDecoder("utf-8");
    let res;

    try {
      res = JSON.parse(enc.decode(new Uint8Array(config)));
    } catch {
      res = config;
    }

    return res;
  },
  (error) => {
    // 请求错误
    return Promise.reject(error);
  },
);

// 添加响应拦截器
instance.interceptors.response.use(
  (response) => {
    let enc = new TextDecoder("utf-8");
    let res;

    try {
      res = JSON.parse(enc.decode(new Uint8Array(response.data)));
    } catch {
      res = response.data;
    }
    if (Object.prototype.toString.call(res) !== "[object Object]") {
      res = {
        errcode: 0,
        data: res,
      };
    }
    res.success = false;
    res.errcode = res.errcode || res.status;
    //返回文件流时，没有状态
    if (res.errcode === undefined) {
      res.errcode = 0;
    }
    switch (Number.parseInt(res.errcode)) {
      case 0:
      case 200:
        res.success = true;
        break;
      case 402: //token失效
      case 403: //token失效
      case 409: //token失效
        //默认提示：后端返回的信息
        store.dispatch("clearCancel");
        localStorage.clear();
        lgNotify.warning(res.errmsg || res.message);
        if (parseUA().miniProgram) {
          let url = `/pages/authority/index`;
          wxAdvanceNavigateTo(url);
        } else {
          window.Location.reload();
        }
        break;
      default:
        //默认提示：后端返回的信息
        lgNotify.warning(res.errmsg || res.message);
        break;
    }
    return res;
  },
  (error) => {
    if (axios.isCancel(error)) {
      // 使用isCancel 判断是否是主动取消请求
      return new Promise(() => {});
    }
    // 请求错误：提示错误信息
    lgNotify.error(error);
    return Promise.reject(error);
  },
);

//1.地址，2.参数，3.配置，4.是否表单格式（默认json格式）
export function post(url, data, config = {}, formData = false) {
  // 如果配置中已经指定了Content-Type，则使用指定的
  if (!config.headers || !config.headers["Content-Type"]) {
    // json格式请求头
    const headerJSON = {
      "Content-Type": "application/json",
    };
    // FormData格式请求头
    const headerFormData = {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    };

    if (!formData) {
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerJSON);
    } else {
      data = qs.stringify(data);
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerFormData);
    }
  }

  return instance.post(url, data, config);
}

//1.地址，2.参数，3.配置，4.是否表单格式（默认json格式）
export function put(url, data, config = {}, formData = false) {
  // 如果配置中已经指定了Content-Type，则使用指定的
  if (!config.headers || !config.headers["Content-Type"]) {
    // json格式请求头
    const headerJSON = {
      "Content-Type": "application/json",
    };
    // FormData格式请求头
    const headerFormData = {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    };

    if (!formData) {
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerJSON);
    } else {
      data = qs.stringify(data);
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerFormData);
    }
  }

  return instance.put(url, data, config);
}

export function get(url, params, config = {}) {
  config = Object.assign(config, {
    params,
  });
  return instance.get(url, config);
}

export function del(url, params, data = false, config = {}) {
  if (!data) {
    config = Object.assign(config, {
      params,
    });
  } else {
    config = Object.assign(config, {
      data: params,
    });
  }

  return instance.delete(url, config);
}

export function uploadFile(url, params, upProgress) {
  //文件上传
  return instance({
    method: "POST",
    headers: { "Content-Type": "multipart/form-data;" },
    data: params,
    url,
    timeout: 0,
    onUploadProgress: upProgress,
  });
}

//文件下载时接收为blob形式
export function getFile(url, params) {
  return instance({
    url: url,
    method: "get",
    params: params,
    responseType: "blob",
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
