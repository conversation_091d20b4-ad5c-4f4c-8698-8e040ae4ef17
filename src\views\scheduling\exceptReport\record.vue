<template>
  <div>
    <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList" v-show="!showPointRecord">
      <template #default="{ form }">
        <div class="record-item">
          <div class="record-left">上报地址</div>
          <div class="record-right">
            <div>{{ form.address }}</div>
            <span
              class="el-icon-map-location location-icon"
              @click="showPointRecord = true"
              v-if="recordForm.address && recordForm.longitude && recordForm.latitude"
            ></span>
          </div>
        </div>
        <div class="other-record">
          <baseTitle title="异常描述"></baseTitle>
          <div class="other-item">
            <div class="record-left">异常描述内容</div>
            <div class="record-right">{{ form.exceptionContent }}</div>
          </div>
        </div>
      </template>
    </pageRecord>
    <pointRecord
      v-if="showPointRecord"
      :formItem="recordForm"
      @closePointRecord="showPointRecord = false"
    ></pointRecord>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import pageRecord from "@/components/pageRecord";
  import { ABNORMAL_TYPE, CONTINUECARRYING_STATUS_MAP } from "@/enums";
  import pointRecord from "./components/pointRecord.vue";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: { pageRecord, pointRecord, baseTitle },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/abnormalreporting/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "reportingTime",
                value: "上报时间",
              },
              {
                key: "reportPerson",
                value: "上报人",
              },
              {
                key: "exceptionType",
                value: "异常事件类型",
                options: ABNORMAL_TYPE,
              },
              {
                key: "continueCarrying",
                value: "是否可以继续收运",
                options: CONTINUECARRYING_STATUS_MAP,
              },
            ],
          },
        ],
        showPointRecord: false,
        recordForm: {},
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.id, this.apis.info);
          if (res.success) {
            this.recordForm = res.data;
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-item {
    padding: 12px 24px;
  }
  .record-right {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
  }
  .location-icon {
    font-size: 16px;
    color: #909399;
    margin-left: 4px;
  }
  .other-record {
    padding: 0 24px;
  }
  .other-item {
    padding: 12px 0;
    display: flex;
    align-items: flex-start;
  }
</style>
