<template>
  <div class="create-container">
    <div class="create-box">
      <div class="create-content">
        <main class="create-main">
          <div class="create-title">个人信息</div>
          <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
            <van-field
              v-model="ruleForm.fullName"
              name="fullName"
              label="真实姓名"
              placeholder="请输入真实姓名"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.fullName"
              required
              maxlength="14"
            />
            <van-field
              v-model="ruleForm.email"
              name="email"
              label="邮箱"
              placeholder="请输入邮箱"
              input-align="right"
              error-message-align="right"
              :border="false"
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.sexName"
              name="sex"
              label="性别"
              placeholder="请选择性别"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.sex"
              required
              @click="showSexPicker = true"
            />
            <van-field
              v-model="ruleForm.phone"
              name="phone"
              label="联系电话"
              placeholder="请输入联系电话"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.phone"
              required
              maxlength="11"
              disabled
            />
            <van-field
              v-model="ruleForm.jobNo"
              name="jobNo"
              label="工号"
              placeholder="请输入工号"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.jobNo"
              required
              maxlength="20"
            />
            <van-field
              v-model="ruleForm.idCard"
              name="idCard"
              label="身份证编号"
              placeholder="请输入身份证编号"
              input-align="right"
              error-message-align="right"
              :border="false"
              maxlength="18"
            />
            <van-field
              v-model="ruleForm.companyName"
              name="company"
              label="所属公司"
              placeholder="请选择所属公司"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              @click="showCompanyPicker = true"
            />
            <van-field
              v-model="ruleForm.positionName"
              name="position"
              label="职位"
              placeholder="请选择职位"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              @click="showPositionPicker = true"
            />
          </van-form>
        </main>
      </div>
      <footer class="create-footer">
        <van-button native-type="button" class="round-6" block type="info" color="#4CA786" @click="submitFormThrottling"
          >保存</van-button
        >
      </footer>
    </div>
    <van-popup v-model="showSexPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="SEX_OPTIONS"
        :default-index="sexIndex"
        @confirm="onSexConfirm"
        @cancel="showSexPicker = false"
      />
    </van-popup>
    <van-popup v-model="showCompanyPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="companyOptions"
        :default-index="companyIndex"
        @confirm="onCompanyConfirm"
        @cancel="showCompanyPicker = false"
        value-key="name"
      />
    </van-popup>
    <van-popup v-model="showPositionPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="positionOptions"
        :default-index="positionIndex"
        @confirm="onPositionConfirm"
        @cancel="showPositionPicker = false"
        value-key="name"
      />
    </van-popup>
  </div>
</template>

<script>
  import { getUserInfo } from "@/utils/storage";
  import { SEX_OPTIONS } from "@/enums";
  import { getListApiFun, getInfoApiFun, updateApiFun } from "@/api/base";
  import { wxPostMessage } from "@/utils/wechatRouter";
  import { setUserInfo } from "@/utils/storage";
  export default {
    data() {
      return {
        SEX_OPTIONS,
        companyList: [],
        companyOptions: [],
        positionOptions: [],
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          companyList: "/api/company/list",
          positionList: "/api/dict/position/list",
          update: "/api/baseuser/updateUserApplet",
        },
        ruleForm: {
          userName: "", //用户名
          fullName: "", //真实姓名
          email: "", //邮箱
          sexName: "", //性别
          sex: "", //性别
          phone: "", //联系电话
          jobNo: "", //工号
          idCard: "", //身份证编号
          companyName: "", //所属公司
          company: "", //所属公司
          positionName: "", //职位
          position: "", //职位
          userType: "", //用户类型
        },
        rules: {
          fullName: [{ required: true, message: "请输入真实姓名" }],
          sex: [{ required: true, message: "请选择性别" }],
          phone: [{ required: true, message: "请输入联系电话" }],
          jobNo: [{ required: true, message: "请输入工号" }],
        },
        showSexPicker: false,
        showCompanyPicker: false,
        showDirectSuperiorPicker: false,
        showPositionPicker: false,
        sexIndex: 0,
        userIdentityIndex: 0,
        companyIndex: 0,
        directSuperiorIndex: 0,
        positionIndex: 0,
        validateList: ["userName", "fullName", "sex", "phone", "jobNo"],
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.ruleForm = Object.assign(this.ruleForm, this.userInfo);
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [getListApiFun({}, this.apis.companyList), getInfoApiFun("", this.apis.positionList)];
        let res = await Promise.all(promiseList);
        this.companyList = res[0].data;
        this.positionOptions = res[1].data;
        this.companyOptions = this.companyList.filter((list) => {
          if (this.ruleForm.userType === 1) {
            return list.isDefault !== 1;
          }
          return list.isDefault === 1;
        });
        for (let key in this.ruleForm) {
          if (this.ruleForm[key] || this.ruleForm[key] === 0) {
            if (key == "sex") {
              let filter = this.SEX_OPTIONS.filter((_, index) => this.ruleForm[key] === index);
              let index = this.SEX_OPTIONS.findIndex((_, index) => this.ruleForm[key] === index);
              if (filter.length > 0) {
                this.ruleForm.sexName = filter[0];
                this.sexIndex = index;
              }
            } else if (key == "company") {
              let filter = this.companyOptions.filter((item) => this.ruleForm[key] === item.id);
              let index = this.companyOptions.findIndex((item) => this.ruleForm[key] === item.id);
              if (filter.length > 0) {
                this.ruleForm.companyName = filter[0].name;
                this.companyIndex = index;
              }
            } else if (key == "position") {
              let filter = this.positionOptions.filter((item) => this.ruleForm[key] === item.id);
              let index = this.positionOptions.findIndex((item) => this.ruleForm[key] === item.id);
              if (filter.length > 0) {
                this.ruleForm.positionName = filter[0].name;
                this.positionIndex = index;
              }
            }
          }
        }
      },
      // 性别选择
      onSexConfirm(item, index) {
        this.ruleForm.sexName = item;
        this.ruleForm.sex = index;
        this.showSexPicker = false;
      },
      // 所属公司选择
      onCompanyConfirm(item) {
        this.ruleForm.companyName = item.name;
        this.ruleForm.company = item.id;
        this.showCompanyPicker = false;
      },
      // 职位选择
      onPositionConfirm(item) {
        this.ruleForm.positionName = item.name;
        this.ruleForm.position = item.id;
        this.showPositionPicker = false;
      },
      // 保存
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = JSON.parse(JSON.stringify(this.ruleForm));
            delete params.avatar;
            try {
              let res = await updateApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.update);
              this.$toast.clear();
              if (res.success) {
                setUserInfo(this.ruleForm);
                wxPostMessage({ userInfo: this.ruleForm });
                this.$toast({
                  type: "success",
                  message: "保存成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
    text-align: center;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .round-6 {
    border-radius: 6px;
  }
</style>
