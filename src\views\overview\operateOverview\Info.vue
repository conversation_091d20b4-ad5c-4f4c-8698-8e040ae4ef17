<template>
  <div class="info-wrapper">
    <ListItem title="今日收运量情况" :hasLabel="false" :list="rublishList"></ListItem>
    <ListItem
      title="各区点位分布情况"
      :hasLabel="true"
      :label="['区域名', '点位数量']"
      :list="summaryData.pointSituation"
    ></ListItem>
    <ListItem
      title="各区客商分布情况"
      :hasLabel="true"
      :label="['区域名', '客商数量']"
      :list="summaryData.merchantSituation"
    ></ListItem>
  </div>
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base";
  import ListItem from "./ListItem.vue";
  export default {
    components: { ListItem },
    props: {},
    data() {
      return {
        api: {
          getData: "/api/generalization/operate/point",
        },
        summaryData: {
          pointSituation: [],
          merchantSituation: [],
        },
        rublishList: [
          {
            key: "infectiousWaste",
            districtName: "感染性废物",
            pointNum: "",
          },
          {
            key: "sludge",
            districtName: "感染性废物（污泥）",
            pointNum: "",
          },
          {
            key: "damaginWaste",
            districtName: "损伤性废物",
            pointNum: "",
          },
          {
            key: "chemicalWaste",
            districtName: "化学性废物",
            pointNum: "",
          },
          {
            key: "pharmaceuticalWaste",
            districtName: "药物性废物",
            pointNum: "",
          },
          {
            key: "pathologicalWaste",
            districtName: "病理性废物",
            pointNum: "",
          },
        ],
      };
    },
    computed: {},
    created() {},
    async mounted() {
      await this.getData();
    },
    methods: {
      async getData() {
        try {
          const data = await getInfoApiFunByParams({}, this.api.getData);
          this.summaryData = data.data;
          let rublishSituation = data.data.rublishSituation;
          for (let index = 0; index < this.rublishList.length; index++) {
            this.rublishList[index].pointNum = rublishSituation[this.rublishList[index].key] + "（T）";
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .info-wrapper {
    padding: 12px 12px 1px 12px;
    background-color: #f7faf9;
    flex: 1;
    overflow: auto;
  }
</style>
