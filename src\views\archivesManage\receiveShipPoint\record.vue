<template>
  <div class="record-container">
    <div class="record-box">
      <baseTitle class="record-title" title="收运点位详情"></baseTitle>
      <div class="record-content">
        <div class="record-item">
          <div class="record-left">点位名称</div>
          <div class="record-right">{{ ruleForm.name }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">点位编号</div>
          <div class="record-right">{{ ruleForm.code }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">点位二维码信息</div>
          <div class="record-right record-qrcode" @click="openQrcodeDialog">查看</div>
        </div>
        <div class="record-item">
          <div class="record-left">省</div>
          <div class="record-right">{{ ruleForm.provinceName }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">市</div>
          <div class="record-right">{{ ruleForm.cityName }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">区</div>
          <div class="record-right">{{ ruleForm.districtName }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">详细地址</div>
          <div class="record-right">{{ ruleForm.address }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">经度</div>
          <div class="record-right">{{ ruleForm.longitude }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">纬度</div>
          <div class="record-right">{{ ruleForm.latitude }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">点位类型</div>
          <div class="record-right">{{ POINT_TYPE[ruleForm.type] }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">点位收运方式</div>
          <div class="record-right">{{ POINT_RECEIVING_METHOD[ruleForm.baggingMethod] }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">联系人名称</div>
          <div class="record-right">{{ ruleForm.contact }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">联系电话</div>
          <div class="record-right">{{ ruleForm.contactPhone }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">日排放量（kg）</div>
          <div class="record-right">{{ ruleForm.dailyEmissions }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">月排放量（kg）</div>
          <div class="record-right">{{ ruleForm.monthEmissions }}</div>
        </div>
        <div class="record-item" v-if="[0, 1, 3].includes(ruleForm.type)">
          <div class="record-left">关联客商</div>
          <div class="record-right">{{ ruleForm.merchantFileName }}</div>
        </div>
        <div class="record-item" v-if="[2].includes(ruleForm.type)">
          <div class="record-left">关联收集柜</div>
          <div class="record-right">{{ ruleForm.holdingTankDeviceName }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">点位收运周期</div>
          <div class="record-right">{{ COLLECTION_CYCLE[ruleForm.period] }}</div>
        </div>
        <div class="record-item">
          <div class="record-left">点位收运频次</div>
          <div class="record-right">{{ ruleForm.frequency }}</div>
        </div>
      </div>
    </div>
    <div class="record-footer">
      <van-button native-type="button" class="round-32" block type="info" color="#4CA786" @click="toEdit"
        >去编辑</van-button
      >
    </div>
    <van-popup v-model="showQrcode" position="center" round>
      <div class="qrcode-container">
        <canvas id="qrcode-canvas" ref="canvas"></canvas>
      </div>
    </van-popup>
  </div>
</template>

<script>
  import { POINT_TYPE, COLLECTION_CYCLE, POINT_RECEIVING_METHOD } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  import { getInfoApiFun } from "@/api/base";
  import QRCode from "qrcode";
  export default {
    components: {
      baseTitle,
    },
    data() {
      return {
        POINT_TYPE,
        COLLECTION_CYCLE,
        POINT_RECEIVING_METHOD,
        id: "",
        apis: {
          info: "/api/pickup/pickupPoint/get/",
        },
        ruleForm: {},
        showQrcode: false,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.contactPhone = this.ruleForm.contactPhone ? this.$sm2Decrypt(this.ruleForm.contactPhone) : "";
          for (let key in this.ruleForm) {
            this.ruleForm[key] =
              this.ruleForm[key] || this.ruleForm[key] === 0 || this.ruleForm[key] === false ? this.ruleForm[key] : "-";
          }
        }
      },
      toEdit() {
        this.$commonSkip("receiveShipPointCreate", { id: this.id });
      },
      async openQrcodeDialog() {
        this.showQrcode = true;
        await this.$nextTick();
        QRCode.toCanvas(
          document.getElementById("qrcode-canvas"),
          `${process.env.VUE_APP_H5_WEB_URL}qrcode?to=qrcode&id=${this.id}`,
          {
            width: 240,
            height: 240,
            errorCorrectionLevel: "H", //容错级别,指二维码被遮挡可以扫出结果的区域比例
            type: "image/png", //生成的二维码类型
            quality: 0.5, //二维码质量
            margin: 0, //二维码留白边距
            toSJISFunc: QRCode.toSJIS,
          },
          (error) => {
            console.log(error);
          },
        );
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .record-title {
    padding: 12px 24px;
  }
  .record-box {
    background-color: #fff;
    border-radius: 8px;
    flex: 1;
    overflow: auto;
  }
  .record-content {
    padding: 0 24px;
  }
  .record-item {
    padding: 12px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 16px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 16px;
    text-align: right;
    word-break: break-all;
  }
  .record-footer {
    width: 100%;
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .round-32 {
    border-radius: 32px;
  }
  .record-right.record-qrcode {
    color: #4ca786;
  }
  .qrcode-container {
    width: 300px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
  }
</style>
