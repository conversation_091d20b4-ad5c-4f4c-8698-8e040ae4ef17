@import "../../public/iconfont/iconfont.css";
@import "./common.scss";

body {
  margin: 0;
  padding: 0;
  font-family: PingFang SC, PingFang SC;
}

/*路由切换动画*/
.router-fade-enter-active {
  transition: all 0.3s cubic-bezier(0.6, 0.5, 0.3, 0.1);
}

.router-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.5, 0.5, 0.5, 0.5);
}

.router-fade-enter {
  transform: translateX(0px);
  opacity: 0;
}

.router-fade-leave-to {
  transform: translateX(50px);
  opacity: 0;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.tl {
  text-align: left;
}

.tr {
  text-align: right;
}

:focus {
  outline: -webkit-focus-ring-color auto 0px;
}

ul {
  padding: 0;
  margin: 0;
}

ul li {
  list-style-type: none;
}

.ov-tip {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-desc {
  font-size: small;
  color: #5e5e5e;
}

.max-fill {
  width: 100% !important;
}

.clear {
  zoom: 1;
}

.clear::after {
  content: "";
  display: block;
  clear: both;
}

.mb-12 {
  margin-bottom: 12px;
}

.text-center {
  text-align: center;
}

.has-padding {
  padding: 16px;
}

.has-padding-h {
  padding: 0 16px;
}

.has-padding-v {
  padding: 16px 0;
}

.has-padding-left {
  padding-left: 16px;
}

.has-padding-right {
  padding-right: 16px;
}

.has-padding-top {
  padding-top: 16px;
}

.has-padding-bottom {
  padding-bottom: 16px;
}

.has-margin {
  margin: 16px;
}

.has-margin-h {
  margin: 0 16px;
}

.has-margin-v {
  margin: 16px 0;
}

.has-margin-left {
  margin-left: 16px;
}

.has-margin-right {
  margin-right: 16px;
}

.has-margin-top {
  margin-top: 16px;
}

.has-margin-bottom {
  margin-bottom: 16px;
}

.is-flex {
  display: flex;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.one-line-clamp {
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.two-line-clamp {
  display: -webkit-box;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.relative {
  position: relative;
}

.form-detail-value {
  font-size: 14px;
  color: #303133;
  margin-top: 4px;
  word-break: break-all;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex-start-center {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.flex-center-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-center-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-center-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start-end {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.flex-start-between {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.flex-stretch-between {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex-column-center-start {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.flex-column-center-end {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

.flex-align-center {
  align-items: center;
}

.flex-align-start {
  align-items: flex-start;
}

.flex-align-end {
  align-items: flex-end;
}

.flex-h {
  display: flex;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.full-height {
  height: 100%;
}
