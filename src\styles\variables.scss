/**
 * @Date: 2022-03-28 14:09:32
 * @param {*}
 * @return {*}
 * @description: scss全局公共样式变量，持续完善
 */
:root {
  --color-primary-rgb: 76, 167, 134; //rgb主题颜色，例如: background-color: rgba(var(--color-primary-rgb), 0.5)
  --color-primary: #4ca786;
}
// font size
$font-size-xs: 12px; //辅助性文字、需要弱化的文字信息
$font-size-sm: 14px; //表格文字
$font-size-bs: 16px; //正文
$font-size-md: 20px; //卡片标题、菜单
$font-size-lg: 26px; //二级标题
$font-size-xl: 34px; //一级标题
$font-size-bx: 44px; //重要级别的数字

//layout
$header-height: 64px; //导航栏高度
$main-width: 1200px; //页面宽度
$footer-height: 200px; //页脚高度
$nav-width: var(--nav-width, 64px); //侧边栏宽度

//其它
$border-color: var(border-color, #d9d9d9); //边框颜色
$line-color: var(line-color, #e6e6e6); //分割线颜色

//主题色不同颜色值
$--color-primary-active: var(--color-primary-active, #4eb6a8); //悬浮色
$--color-primary-hover: var(--color-primary-hover, #026a5c); //点击色
$--color-primary-light: var(--color-primary-light, #def1ee); //浅背景颜色

//红色不同颜色值
$--color-danger-active: var(--color-danger-active, #ea5252); //悬浮色
$--color-danger-hover: var(--color-danger-hover, #d32d2d); //点击色
$--color-danger-light: var(--color-danger-light, #fcebeb); //浅背景颜色

//element-ui
$--color-primary: var(--color-primary, #039883); //主题颜色
$--color-success: var(--color-success, #63cc7c); //成功颜色
$--color-warning: var(--color-warning, #fabb28); //提醒、警告颜色
$--color-danger: var(--color-danger, #e83f3f); //失败、错误颜色
$--color-text-primary: var(--color-text-primary, #222222); //文字主要颜色
$--color-text-regular: var(--color-text-regular, #666666); //文字次要颜色
$--color-text-secondary: var(--color-text-secondary, #999999); //文字辅助、说明颜色
$--color-text-placeholder: var(--color-text-placeholder, #aaaaaa); //文字禁用、提示颜色
$--background-color-base: var(--background-color-base, #f7f7f7); //全局背景颜色
$--button-default-font-color: $--color-primary; //按钮字体颜色
