<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <van-index-bar>
        <div class="create-box">
          <div class="create-content">
            <main class="create-main">
              <van-index-anchor index="基础信息" class="header-box">
                <FormTitle :title="'基础信息'" />
              </van-index-anchor>
              <div class="create-title">基础信息</div>
              <van-field
                v-model="ruleForm.plateNumber"
                name="plateNumber"
                label="车牌号"
                placeholder="请选择车牌号"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules.plateNumber"
                required
                @click="showCarPicker = true"
              />
              <van-field
                :value="userInfo.fullName"
                label="驾驶司机"
                readonly
                input-align="right"
                error-message-align="right"
                :border="false"
                required
              />
              <van-field
                v-model="ruleForm.typeName"
                name="type"
                label="自检类型"
                placeholder=""
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules.type"
                required
              />
            </main>
            <main class="create-main">
              <van-index-anchor index="自检信息" class="header-box">
                <FormTitle :title="'自检信息'" />
              </van-index-anchor>
              <van-field
                v-model="ruleForm.inspectTime"
                name="inspectTime"
                label="自检时间"
                placeholder="请选择自检时间"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules.inspectTime"
                required
                @click="openDatePicker('inspectTime')"
              />
              <van-field :label="`${ruleForm.typeName}明细`" label-width="100%" :border="false" required></van-field>
              <div v-for="(item, index) in ruleForm.detailList" :key="index">
                <van-field
                  :label="item.configName"
                  input-align="right"
                  error-message-align="right"
                  :border="false"
                  required
                >
                  <template #input>
                    <el-switch
                      v-model="ruleForm.detailList[index].status"
                      active-text="正常"
                      inactive-text="异常"
                      active-color="#4CA786"
                      :active-value="0"
                      :inactive-value="1"
                    >
                    </el-switch>
                  </template>
                </van-field>
              </div>
              <van-field
                v-model="ruleForm.influencePickupName"
                name="influencePickup"
                label="是否可以正常收运"
                placeholder="请选择是否可以正常收运"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules.influencePickup"
                required
                label-width="120"
                @click="showInfluencePicker = true"
                v-if="showinfluence"
              />
            </main>
            <main class="create-main">
              <van-index-anchor index="照片信息" class="header-box">
                <FormTitle :title="'照片信息'" />
              </van-index-anchor>
              <van-field :border="false" label-width="100%">
                <template #label>
                  <div class="label-box">
                    <div class="label-title">车辆照片</div>
                    <div class="label-text">(请上传车辆安全自检照片)</div>
                  </div>
                </template>
              </van-field>
              <imageUpload
                v-model="ruleForm.fileList"
                :isRequired="false"
                formId="fileList"
                formPlaceHolder="请上传车辆安全自检照片"
              ></imageUpload>
            </main>
          </div>
          <footer class="create-footer">
            <van-button native-type="button" class="round-22" block type="info" color="#DCE0DF" @click="$commonBack()"
              >返回</van-button
            >
            <van-button
              native-type="button"
              class="round-22 ml-11"
              block
              type="info"
              color="#4CA786"
              @click="submitFormThrottling"
              >提交</van-button
            >
          </footer>
        </div>
      </van-index-bar>
    </van-form>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="onCarConfirm"
        @cancel="showCarPicker = false"
      />
    </van-popup>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker show-toolbar :columns="SELFTEST_STATUS" @confirm="onTypeConfirm" @cancel="showTypePicker = false" />
    </van-popup>
    <van-popup v-model="showInfluencePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="IS_NORMAL_OPERATION"
        @confirm="onInfluenceConfirm"
        @cancel="showInfluencePicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="datetime"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFunByParams, getListApiFun } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import { SELFTEST_STATUS, IS_NORMAL_OPERATION } from "@/enums";
  import FormTitle from "@/components/FormTitle";
  export default {
    components: {
      imageUpload,
      FormTitle,
    },
    computed: {
      showinfluence() {
        let flag = false;
        if (this.ruleForm.detailList && this.ruleForm.detailList.length > 0) {
          let filterList = this.ruleForm.detailList.filter((list) => list.status === 1);
          if (filterList.length > 0) {
            flag = true;
          }
        }
        return flag;
      },
    },
    data() {
      return {
        step: 0, //新增步骤
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/vehicle/inspect/create",
          configList: "/api/vehicle/evaluationConfig/list",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          defaultCar: "/api/vehicle/dossier/findByPhone",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
        },
        showCarPicker: false, //车牌号下拉弹框
        carOptions: [], //车牌号列表
        defaultCarIndex: 0, //默认车牌号下拉列表索引
        driverOptions: [], //司机列表
        defaultDriverIndex: 0, //默认司机下拉列表索引
        showTypePicker: false, //自检类型下拉框
        SELFTEST_STATUS, //自检类型下拉列表
        configList: [], //评价配置项列表
        showInfluencePicker: false, //是否可以正常收运下拉框
        IS_NORMAL_OPERATION,
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(),
        ruleForm: {
          plateNumber: "", //车牌号
          driverDossierId: "", //司机id
          driverDossierName: "", //司机名称
          type: "", //自检类型
          typeName: "", //自检类型名称
          inspectTime: moment(new Date()).format("YYYY-MM-DD HH:mm"), //自检时间
          detailList: [], //车辆状况明细
          fileList: [],
          influencePickup: "", //是否可以正常收运
          influencePickupName: "", //是否可以正常收运
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号" }],
          driverDossierId: [{ required: true, message: "请选择驾驶司机" }],
          type: [{ required: true, message: "请选择自检类型" }],
          inspectTime: [{ required: true, message: "请选择自检时间" }],
          influencePickup: [{ required: true, message: "请选择是否可以正常收运" }],
        },
        validateList: [["plateNumber", "driverDossierId", "type"], ["inspectTime"]],
        carTypeOptionMap: {
          selfTestBeforeCreate: "车前检-0",
          selfTestInCreate: "车中检-1",
          selfTestEdCreate: "车后检-2",
        },
      };
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
      if (this.$route.name !== "selfTestCreate") {
        this.ruleForm.typeName = this.carTypeOptionMap[this.$route.name].split("-")[0];
        this.ruleForm.type = +this.carTypeOptionMap[this.$route.name].split("-")[1];
      } else {
        this.ruleForm.typeName = this.carTypeOptionMap["selfTestBeforeCreate"].split("-")[0];
        this.ruleForm.type = +this.carTypeOptionMap["selfTestBeforeCreate"].split("-")[1];
      }
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          createApiFun({ type: 1 }, this.apis.configList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.ruleForm.detailList = res[1].data.map((list) => {
          return {
            configId: list.id,
            configName: list.name,
            status: 0,
          };
        });
        this.driverOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.getUserDefaultCar();
      },
      // 获取当前用户默认车牌号
      async getUserDefaultCar() {
        let res = await getInfoApiFunByParams(
          { phone: this.userInfo.phone ? this.$sm2Encrypt(this.userInfo.phone) : "" },
          this.apis.defaultCar,
        );
        if (res.data) {
          if (res.data.status === 2) {
            return;
          }
          this.ruleForm.plateNumber = res.data.plateNumber;
          this.ruleForm.driverDossierId = res.data.lgUnionId;
          this.ruleForm.driverDossierName = res.data.fullName;
          let index = this.carOptions.findIndex((option) => option.id == res.data.id);
          if (index > 0) {
            this.defaultCarIndex = index;
          }
          let driverIndex = this.driverOptions.findIndex((option) => option.lgUnionId == res.data.lgUnionId);
          if (driverIndex > 0) {
            this.defaultDriverIndex = driverIndex;
          }
        }
      },
      // 确认选择车牌号
      async onCarConfirm(item) {
        // let res = await createApiFun({ userIdentity: 3, plateNumber: item.name }, this.apis.driverAndWorkerInfo);
        // if (res.data) {
        //   let driverIndex = this.driverOptions.findIndex((option) => option.lgUnionId == res.data.lgUnionId);
        //   if (driverIndex > 0) {
        //     this.defaultDriverIndex = driverIndex;
        //   }
        // }
        this.ruleForm.plateNumber = item.name;
        this.showCarPicker = false;
      },
      // 确认选择自检类型
      onTypeConfirm(item, index) {
        this.ruleForm.typeName = item;
        this.ruleForm.type = index;
        this.showTypePicker = false;
      },
      onInfluenceConfirm(item, index) {
        this.ruleForm.influencePickupName = item;
        this.ruleForm.influencePickup = index;
        this.showInfluencePicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD HH:mm");
        this.showDatePicker = false;
      },
      // 步骤点击事件
      handleStepClick() {
        this.$refs.formCreate
          .validate(this.validateList[this.step])
          .then(() => {
            this.step++;
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            if (!this.showinfluence) {
              delete this.ruleForm.influencePickup;
            }
            let params = this.ruleForm;
            params.driverDossierId = this.userInfo.lgUnionId;
            try {
              let res = await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增车辆安全自检记录成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100%;
    background-color: #f7faf9;
    position: relative;
    padding-bottom: 80px;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    width: 100%;
    bottom: 0;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  ::v-deep .van-tabs__wrap {
    display: none;
  }
  ::v-deep .van-form,
  ::v-deep .van-tabs,
  ::v-deep .van-tabs__content,
  ::v-deep .van-tab__pane {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
  ::v-deep .van-index-bar__sidebar {
    display: none;
  }
  .header-box {
    margin: 0 -12px;
  }
</style>
