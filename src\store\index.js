import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    axiosCancelArr: [],
  },
  mutations: {
    PUSH_CANCEL(state, cancel) {
      state.axiosCancelArr.push(cancel.cancelToken);
    },
    CLEAR_CANCEL(state) {
      state.axiosCancelArr.forEach((e) => {
        if (e) {
          e();
        }
      });
      state.axiosCancelArr = [];
    },
  },
  actions: {
    pushCancel({ commit }, cancel) {
      commit("PUSH_CANCEL", cancel);
    },
    clearCancel({ commit }) {
      commit("CLEAR_CANCEL");
    },
  },
});
