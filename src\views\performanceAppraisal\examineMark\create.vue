<template>
  <div class="create-container">
    <div class="create-box">
      <div class="create-content">
        <main class="create-main">
          <baseTitle title="考核方案信息">
            <template #right>
              <van-icon
                :name="moreList[0] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(0)"
              />
            </template>
          </baseTitle>
          <div v-show="moreList[0]">
            <van-field
              :value="formItem.assessSchemeName"
              label="考核方案名称"
              placeholder="请输入考核方案名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              readonly
            />
            <van-field
              value="月度"
              label="考核周期"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              readonly
            />
            <van-field
              :value="formItem.fullName"
              label="被考核人员名称"
              placeholder="请输入被考核人员名称"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              label-width="120px"
              readonly
            />
            <van-field
              :value="`${formItem.year}-${formItem.month >= 10 ? '' : '0'}${formItem.month}`"
              label="考核月度"
              input-align="right"
              error-message-align="right"
              :border="false"
              required
              readonly
            />
          </div>
        </main>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="绩效总览">
            <template #right>
              <van-icon
                :name="moreList[1] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(1)"
              />
            </template>
          </baseTitle>
          <div class="pb-10" v-show="moreList[1]">
            <overview
              canScore
              :form.sync="overviewForm"
              :detailItem="detailItem"
              :performanceDetailJson="performanceDetailJson"
              :dataDetailFields="dataDetailFields"
            ></overview>
          </div>
        </main>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="绩效明细">
            <template #right>
              <van-icon
                :name="moreList[2] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(2)"
              />
            </template>
          </baseTitle>
          <div class="pb-10" v-show="moreList[2]">
            <dataDetail
              showTotal
              :tableData="performanceDetailJson?.dataDetail"
              :dataDetailFields="dataDetailFields"
            ></dataDetail>
          </div>
        </main>
        <template v-if="formItem.isReturnAssessForm == 1">
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="上一级评分情况">
              <template #right>
                <van-icon
                  :name="moreList[3] ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#909399"
                  @click="toggleMore(3)"
                />
              </template>
            </baseTitle>
            <div v-show="moreList[3]">
              <van-field
                :value="formItem.beforeScore"
                label="总分"
                input-align="right"
                error-message-align="right"
                :border="false"
                readonly
              />
              <van-field
                :value="formItem.beforeAssessGradeName"
                label="考核等级"
                input-align="right"
                error-message-align="right"
                :border="false"
                readonly
              />
              <el-table :data="formItem.beforeAssessDimensions" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column prop="name" label="维度名称" align="center"></el-table-column>
                <el-table-column prop="weight" label="维度满分" align="center"></el-table-column>
                <el-table-column prop="score" label="分数" align="center"></el-table-column>
              </el-table>
            </div>
          </main>
        </template>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="考核信息">
            <template #right>
              <van-icon
                :name="moreList[4] ? 'arrow-down' : 'arrow-up'"
                size="20"
                color="#909399"
                @click="toggleMore(4)"
              />
            </template>
          </baseTitle>
          <div v-show="moreList[4]">
            <van-form :show-error-message="false" :submit-on-enter="false" ref="formDemeritPoints" label-width="120px">
              <ul v-if="ruleForm.demeritPoints.length > 0">
                <li class="create-main mt-12" v-for="(item, index) in ruleForm.demeritPoints" :key="index">
                  <div class="delete-icon">
                    <van-icon name="delete-o" color="#f56c6c" size="24" @click="deleteDeductionItem(index)" />
                  </div>
                  <van-field
                    :value="DEDUCTION_ITEM[item.type]"
                    name="type"
                    label="扣分项"
                    placeholder="请选择扣分项"
                    readonly
                    input-align="right"
                    error-message-align="right"
                    is-link
                    :border="false"
                    :rules="fieldRules.type"
                    required
                    @click="openTypePicker(index)"
                  />
                  <van-field
                    name="deductSalary"
                    label="扣除绩效工资金额"
                    input-align="right"
                    error-message-align="right"
                    label-width="160px"
                    :border="false"
                    :rules="fieldRules.deductSalary"
                    required
                  >
                    <template #input>
                      <van-stepper
                        v-model="item.deductSalary"
                        integer
                        min="0"
                        max="99999999"
                        :default-value="''"
                        input-width="80px"
                      />
                    </template>
                  </van-field>
                  <van-field
                    name="deductScore"
                    label="扣除总分"
                    input-align="right"
                    error-message-align="right"
                    label-width="160px"
                    :border="false"
                    :rules="fieldRules.deductScore"
                    required
                  >
                    <template #input>
                      <van-stepper
                        v-model="item.deductScore"
                        min="0"
                        max="100"
                        :decimal-length="2"
                        :default-value="''"
                        input-width="80px"
                      />
                    </template>
                  </van-field>
                  <van-field
                    v-model="item.memo"
                    label="备注"
                    placeholder="请输入备注"
                    input-align="right"
                    :border="false"
                    maxlength="50"
                  />
                </li>
              </ul>
            </van-form>
            <van-button class="mt-12" icon="plus" type="primary" size="small" color="#4CA786" @click="addDeductionItem"
              >添加扣分项</van-button
            >
            <el-form class="mt-12" :model="ruleForm" :rules="rules" ref="ruleForm">
              <el-form-item label="" prop="assessDimensionScores">
                <el-table
                  :data="ruleForm.assessDimensionScores"
                  :header-cell-style="{ background: '#F5F7F9' }"
                  border
                  max-height="500px"
                >
                  <el-table-column prop="assessDimensionName" label="维度名称" align="center"></el-table-column>
                  <el-table-column prop="assessDimensionWeight" label="维度满分" align="center"></el-table-column>
                  <el-table-column prop="score" label="打分" align="center" min-width="140px">
                    <template #default="{ row, $index }">
                      <el-form-item :prop="`assessDimensionScores.${$index}.score`" :rules="dimensionRules.score">
                        <el-input-number
                          v-model="row.score"
                          size="small"
                          :min="0"
                          :precision="2"
                          :max="row.assessDimensionWeight"
                        ></el-input-number>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
            <van-field
              :value="totalScore"
              label="总分"
              placeholder=""
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
            />
            <van-field
              :value="level ? level : '无'"
              label="考核等级"
              placeholder=""
              input-align="right"
              error-message-align="right"
              :border="false"
              readonly
            />
          </div>
        </main>
      </div>
      <footer class="create-footer">
        <van-button
          native-type="button"
          class="round-22 mr-10"
          block
          type="danger"
          v-if="formItem.isReturnAssessForm == 1"
          @click="returnSuperior"
          >退回上一级</van-button
        >
        <van-button
          native-type="button"
          class="round-22"
          block
          type="info"
          color="#4CA786"
          @click="submitFormThrottling"
          >完成</van-button
        >
      </footer>
    </div>
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker show-toolbar :columns="DEDUCTION_ITEM" @confirm="onTypeConfirm" @cancel="showTypePicker = false" />
    </van-popup>
  </div>
</template>

<script>
  import { DEDUCTION_ITEM } from "@/enums";
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { floatAdd, floatSub, roundReserveDecimals } from "@/utils";
  import overview from "./components/overview.vue";
  import dataDetail from "@/views/performanceAppraisal/examineConfirm/components/dataDetail.vue";
  export default {
    components: {
      baseTitle,
      overview,
      dataDetail,
    },
    computed: {
      // 考核总分
      totalScore() {
        let score = 0;
        if (this.ruleForm?.assessDimensionScores.length > 0) {
          let dimensionScore = this.ruleForm.assessDimensionScores.reduce((total, currentValue) => {
            return floatAdd(total, currentValue.score);
          }, 0);
          let demeritScore = this.ruleForm.demeritPoints.reduce((total, currentValue) => {
            return floatAdd(total, currentValue.deductScore);
          }, 0);
          let scoreTotal = Number(floatSub(dimensionScore, demeritScore));
          score = scoreTotal > 0 ? scoreTotal : 0;
        }
        return roundReserveDecimals(score, 2);
      },
      // 考核等级
      level() {
        let value = "";
        let totalScore = this.totalScore;
        if (this.ruleForm?.assessGrades?.length > 0) {
          this.ruleForm.assessGrades.forEach((list) => {
            if (list.min <= totalScore && totalScore <= list.max) {
              value = list.name;
            }
          });
        }
        return value;
      },
    },
    data() {
      return {
        formItem: {},
        submitFormThrottling: () => {},
        showTypePicker: false,
        DEDUCTION_ITEM,
        demeritPointsIndex: 0,
        apis: {
          record: "/api/assess/scheme/get/",
          info: "/api/assess/demeritPoint/list",
          create: "/api/assess/form/appletAssessFormMark",
          return: "/api/assess/form/returnAssessForm",
          get: "/api/assess/form/get/",
          form: "/api/assess/form/score/get/",
          save: "/api/assess/form/clinicParam",
        },
        ruleForm: {
          demeritPoints: [], //扣分项列表
          assessDimensionScores: [], //评分项列表
        },
        fieldRules: {
          type: [{ required: true, message: "请选择扣分项" }],
          deductSalary: [{ required: true, message: "请填写扣除绩效工资金额" }],
          deductScore: [{ required: true, message: "请填写扣除总分" }],
        },
        rules: {},
        dimensionRules: {
          score: [{ required: true, message: "请输入评分", trigger: "blur" }],
        },
        moreList: [true, true, true, true, true],
        assessFlowWeight: 0,
        beforeDimensions: [],
        detailItem: {},
        performanceDetailJson: {},
        dataDetailFields: [],
        overviewForm: {},
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
      this.initData();
    },
    methods: {
      // 展开/收缩
      toggleMore(index) {
        this.moreList.splice(index, 1, !this.moreList[index]);
      },
      async initData() {
        let resp = await createApiFun({ assessFormId: this.$route.query.assessFormId }, this.apis.info);
        if (resp.success) {
          this.ruleForm.demeritPoints = resp.data;
        }
        let req = await getInfoApiFun(this.$route.query.id, this.apis.form);
        if (req.success) {
          this.formItem = req.data;
        }
        let rsp = await getInfoApiFun(this.formItem.assessSchemeId, this.apis.record);
        if (rsp.success) {
          let assessDimensions = rsp.data.assessDimensions;
          this.ruleForm.assessDimensionScores = assessDimensions.map((item) => {
            return {
              assessDimensionId: item.id,
              assessDimensionName: item.name,
              score: 0,
              assessDimensionWeight: item.weight,
            };
          });
          this.ruleForm.assessGrades = rsp.data.assessGrades;
          let flowList = rsp.data.assessFlows.filter((list) => list.enable === 1);
          this.assessFlowWeight = flowList.filter((list) => list.flowCode == this.formItem.flowCode)[0]?.weight || 0;
        }
        let res = await getInfoApiFun(this.formItem.assessFormId, this.apis.get);
        if (res.success) {
          try {
            res.data.performanceDetailJson = JSON.parse(res.data.performanceDetailJson);
            res.data.clinicGroupParam = JSON.parse(res.data.clinicGroupParam);
          } catch (error) {
            res.data.performanceDetailJson = {};
            res.data.clinicGroupParam = {};
          }
          this.detailItem = res.data;
          this.performanceDetailJson = res.data.performanceDetailJson;
          this.performanceDetailJson.dataDetail.forEach((list) => {
            for (let key in list) {
              this.dataDetailFields.push(key);
            }
          });
          this.dataDetailFields = [...new Set(this.dataDetailFields)];
          // console.log("this.detailItem ==> ", this.detailItem);
          // console.log("this.performanceDetailJson ==> ", this.performanceDetailJson);
        }
      },
      // 添加扣分项
      addDeductionItem() {
        this.ruleForm.demeritPoints.push({
          type: "",
          deductSalary: "",
          deductScore: 0,
          memo: "",
        });
      },
      // 删除扣分项
      deleteDeductionItem(index) {
        this.ruleForm.demeritPoints.splice(index, 1);
      },
      // 打开扣分项选择器
      openTypePicker(index) {
        this.demeritPointsIndex = index;
        this.showTypePicker = true;
      },
      // 确认选择扣分项类型
      onTypeConfirm(_, index) {
        this.ruleForm.demeritPoints[this.demeritPointsIndex].type = index;
        this.showTypePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formDemeritPoints
          .validate()
          .then(async () => {
            if (!this.overviewForm.pathFactor) {
              this.$toast({
                forbidClick: true,
                type: "fail",
                message: "请选择线路系数",
                duration: 1500,
              });
              return;
            }
            if (!(this.overviewForm.lessType || this.overviewForm.lessType === 0)) {
              this.$toast({
                forbidClick: true,
                type: "fail",
                message: "请选择不足基准时的规则",
                duration: 1500,
              });
              return;
            }
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let rsp = await createApiFun(
                { assessFormId: this.formItem.assessFormId, ...this.overviewForm },
                this.apis.save,
              );
              if (rsp.success) {
                let params = {
                  assessFormId: this.formItem.assessFormId,
                  ...this.ruleForm,
                };
                let res = await createApiFun(params, this.apis.create);
                this.$toast.clear();
                if (res.success) {
                  this.$toast({
                    type: "success",
                    message: "完成评分成功",
                    forbidClick: true,
                    duration: 1500,
                  });
                  setTimeout(() => {
                    this.$commonBack();
                  }, 1500);
                }
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
      // 退回上一级
      returnSuperior() {
        this.$dialog
          .confirm({
            title: "提示",
            message: "确认要退回上一级吗？",
          })
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun({ assessFormId: this.formItem.assessFormId }, this.apis.return);
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "退回上级成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .mt-12 {
    margin-top: 12px;
  }
  .delete-icon {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .pb-10 {
    padding-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
  .pb-10 {
    padding-bottom: 10px;
  }
</style>
