<template>
  <div class="point-container">
    <van-button class="cancel-button" type="default" size="small" @click="closePointRecord">返回</van-button>
    <mapContainer @initMap="initMap" class="map-box"></mapContainer>
  </div>
</template>

<script>
  import mapContainer from "@/components/mapContainer";
  export default {
    props: {
      formItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      mapContainer,
    },
    data() {
      return {
        map: "",
      };
    },
    beforeDestroy() {
      if (this.map) {
        this.map.destroy();
        this.map = "";
      }
    },
    mounted() {},
    methods: {
      // 初始化地图
      async initMap(map) {
        if (map) {
          this.map = map;
          this.initPosition();
        }
      },
      initPosition() {
        if (!this.formItem.address && (!this.formItem.longitude || !this.formItem.latitude)) return;
        this.generateMark([this.formItem.longitude, this.formItem.latitude]);
      },
      // 生成点位
      generateMark(lnglat) {
        const icon = new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
          imageSize: new window.AMap.Size(25, 34),
        });
        let marker = new window.AMap.Marker({
          position: lnglat,
          icon: icon,
          offset: new window.AMap.Pixel(-13, -30),
        });
        this.map.add(marker);
        this.map.setCenter(marker.getPosition());
        this.generateInfoWindowContent(lnglat);
      },
      // 信息窗体结构生成
      generateInfoWindowContent(lnglat) {
        let infoWindowContent = `<div style="font-size: 16px;width: 300px">${this.formItem.address}</div>
        <div style="margin: 10px 0"><span>经度：${this.formItem.longitude}</span>&nbsp;&nbsp;<span>纬度：${this.formItem.latitude}</span></div>`;
        let infoWindow = new window.AMap.InfoWindow({
          position: lnglat,
          offset: new window.AMap.Pixel(0, -40),
          autoMove: true, // 自动移动到标记位置
          content: infoWindowContent,
        });
        infoWindow.open(this.map);
      },
      // 关闭弹窗
      closePointRecord() {
        this.$emit("closePointRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .point-container {
    position: relative;
    height: 100%;
  }
  .map-box {
    height: 100vh;
    height: 100dvh;
  }

  .cancel-button {
    position: absolute;
    border-radius: 4px;
    top: 10px;
    left: 10px;
    z-index: 100;
  }
</style>
