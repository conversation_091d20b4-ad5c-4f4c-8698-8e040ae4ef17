<template>
  <div class="detail-container" v-if="ruleForm">
    <van-field
      value="顶班司机绩效工资"
      label="绩效方案名称："
      input-align="right"
      :border="false"
      required
      label-width="120px"
      readonly
    />
    <van-field value="" label="桶装垃圾(重量计费）收运绩效总计(元）：" :border="false" label-width="100%" readonly />
    <van-field :value="ruleForm.bucketTotalPrice" label="" label-width="0" readonly />
    <van-field value="" label="袋装垃圾（重量计费）收运绩效总计(元）：" :border="false" label-width="100%" readonly />
    <van-field :value="ruleForm.bagTotalPrice" label="" label-width="0" readonly />
    <van-field value="" label="桶装点位(小型床位）收运绩效总计(元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`${ruleForm.bucketTotalCount} x ${ruleForm.bucketPrice} = ${ruleForm.bucketTotalPrice}（元）`"
      label=""
      label-width="0"
      readonly
    />
    <van-field value="" label="袋装点位(小型床位）收运绩效总计(元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`${ruleForm.bagTotalCount} x ${ruleForm.bagPrice} = ${ruleForm.bagTotalPrice}（元）`"
      label=""
      label-width="0"
      readonly
    />
    <van-field
      :value="ruleForm.totalPoint"
      label="诊所点位总收运数（个）："
      input-align="right"
      :border="false"
      label-width="200"
      readonly
    />
    <van-field
      :value="ruleForm.factor"
      label="诊所线路系数："
      input-align="right"
      :border="false"
      label-width="120px"
      readonly
    />
    <van-field
      :value="ruleForm.nsTotalPoint"
      label="南沙诊所点位总收运数（个）："
      input-align="right"
      :border="false"
      label-width="230"
      readonly
    />
    <van-field
      :value="ruleForm.nsFactor"
      label="南沙诊所线路系数："
      input-align="right"
      :border="false"
      label-width="150"
      readonly
    />
    <van-field
      :value="ruleForm.ratio"
      label="考核系数："
      input-align="right"
      :border="false"
      label-width="120"
      readonly
    />
    <van-field
      :value="deductSalary"
      label="扣款金额（元）："
      input-align="right"
      :border="false"
      label-width="140"
      readonly
    />
    <van-field value="" label="绩效工资（元）：" :border="false" label-width="100%" readonly />
    <van-field
      :value="`${ruleForm.totalPrice} x ${ruleForm.ratio} - ${deductSalary} = ${ruleForm.payIncentives}`"
      label=""
      label-width="0"
      readonly
    />
    <van-field value="" label="点位收运绩效明细：" input-align="right" :border="false" label-width="100%" readonly />
    <ul class="detail-list">
      <li class="detail-item" v-for="(item, index) in ruleForm.detailInfo" :key="index">
        <div class="detail-left">
          <div class="detail-title">日期</div>
          <div class="detail-value">{{ item.date }}</div>
        </div>
        <div class="detail-middle">
          <div class="middle-item">
            <div class="middle-item-title">桶装点位（重量计费）收运数量</div>
            <div class="middle-item-value">{{ item.bucketCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">袋装点位（重量计费）收运数量</div>
            <div class="middle-item-value">{{ item.bagCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">桶装点位（小型床位）收运数量</div>
            <div class="middle-item-value">{{ item.collectionBucketCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">袋装点位（小型床位）收运数量</div>
            <div class="middle-item-value">{{ item.collectionBagCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">诊所点位收运数量</div>
            <div class="middle-item-value">{{ item.clinicGroupPointCount }}</div>
          </div>
          <div class="middle-item">
            <div class="middle-item-title">南沙点位收运数量</div>
            <div class="middle-item-value">{{ item.nsClinicGroupPointCount }}</div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { floatAdd, roundReserveDecimals } from "@/utils";
  export default {
    props: {
      detailId: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 5,
      },
      deductSalary: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        floatAdd,
        roundReserveDecimals,
        apis: {
          info: "/api/access/record/detail/",
        },
        ruleForm: {},
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import "./detail.scss";
  .detail-middle {
    border-right: none;
  }
  .detail-list {
    max-height: 915px;
    overflow-y: auto;
  }
</style>
