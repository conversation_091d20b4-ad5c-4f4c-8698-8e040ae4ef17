<template>
  <div class="page-container">
    <header class="page-header">
      <van-field
        v-model="yearMonth"
        placeholder="请选择考核月度"
        input-align="center"
        arrow-direction="down"
        label-width="120"
        readonly
        :border="false"
        @click="showYearMonthPicker = true"
        clearable
      />
      <van-popup v-model="showYearMonthPicker" position="bottom">
        <van-datetime-picker
          type="year-month"
          v-model="filterForm.yearMonth"
          :min-date="filterList[1].minDate"
          :max-date="filterList[1].maxDate"
          cancel-button-text="重置"
          @confirm="onConfirm"
          @cancel="onCancel"
        />
      </van-popup>
    </header>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="item-title"
                >考核月度：{{ item.year }}-{{ item.month >= 10 ? item.month : "0" + item.month }}</div
              >
              <van-button
                v-if="item.verifyStatus === 0"
                type="info"
                size="small"
                color="#4ca786"
                @click="itemClick(item)"
                >去确认</van-button
              >
              <div class="item-text" v-else>已确认</div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import components from "@/components/pageList/componentImport";
  import moment from "moment";
  export default {
    components: components,
    data() {
      return {
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        listPageApi: "/api/assess/form/myListPage",
        tableList: [],
        filterForm: {},
        filterList: [
          {
            type: "Input",
            key: "assessSchemeName",
            value: "考核方案名称",
          },
          {
            type: "YearMonth",
            key: "yearMonth",
            value: "考核月度",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
        yearMonth: "",
        showYearMonthPicker: false,
      };
    },
    mounted() {
      this.onLoad();
      this.filterForm.yearMonth = new Date();
    },
    methods: {
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          verifyStatus: 0,
          // ...this.filterForm,
        };
        if (this.filterForm.yearMonth) {
          params.year = moment(this.filterForm.yearMonth).format("YYYY");
          params.month = moment(this.filterForm.yearMonth).format("M");
        }
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas;
            dataList.forEach((list) => {
              for (let key in list) {
                list[key] = list[key] || list[key] === 0 ? list[key] : "-";
              }
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 列表项点击事件
      itemClick(item) {
        this.$commonSkip("examineConfirmCreate", { id: item.id });
      },
      // 月份筛选
      onConfirm() {
        this.refreshing = true;
        this.yearMonth =
          moment(this.filterForm.yearMonth).format("YYYY") + "-" + moment(this.filterForm.yearMonth).format("MM");
        this.showYearMonthPicker = false;
        this.onLoad();
      },
      onCancel() {
        this.refreshing = true;
        this.filterForm.yearMonth = "";
        this.yearMonth = "";
        this.showYearMonthPicker = false;
        this.onLoad();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f7faf9;
  }
  .page-header {
    background-color: #fff;
  }
  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    background: #f7faf9;
    border-radius: 4px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 20px;
      margin-right: 4px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
    }
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
    flex: 1;
    overflow: hidden;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    word-break: break-all;
  }
  .filter-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    margin-bottom: 16px;
  }
  .reset-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #dce0df;
    border-radius: 20px;
  }
  .search-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #4ca786;
    border-radius: 20px;
    margin-left: 8px;
  }
</style>
