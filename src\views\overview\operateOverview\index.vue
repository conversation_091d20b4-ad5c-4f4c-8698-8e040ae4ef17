<template>
  <div class="operateOverview-wrapper">
    <TopBar @updateActive="updateActive" :list="['①点位客商信息', '②安全统计', '③客服统计']"></TopBar>
    <Info v-if="active == 1"></Info>
    <Safe v-if="active == 2"></Safe>
    <Follow v-if="active == 3"></Follow>
  </div>
</template>
<script>
  import TopBar from "./TopBar";
  import Info from "./Info";
  import Safe from "./Safe";
  import Follow from "./Follow";

  export default {
    components: { TopBar, Info, Safe, Follow },
    props: {},
    data() {
      return {
        active: 1,
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      updateActive(active) {
        this.active = active;
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .operateOverview-wrapper {
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
  }
</style>
