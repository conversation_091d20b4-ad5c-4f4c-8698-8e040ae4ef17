import { Notify } from "vant";

export default {
  /**
   * 错误危险提示
   * @param {*} message
   * @param {*} time
   */
  error(message, time) {
    Notify({
      type: "danger",
      message: message,
      duration: time ? time : 2000,
      className: "lg-vant-notify",
    });
  },
  /**
   * 警告
   * @param {*} message
   * @param {*} time
   */
  warning(message, time) {
    Notify({
      type: "warning",
      message: message,
      duration: time ? time : 2000,
      className: "lg-vant-notify",
    });
  },
  /**
   * 成功提示
   * @param {*} message
   * @param {*} time
   */
  success(message, time) {
    Notify({
      type: "success",
      message: message,
      duration: time ? time : 2000,
      className: "lg-vant-notify",
    });
  },
};
