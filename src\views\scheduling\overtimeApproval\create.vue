<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息">
              <template #right>
                <van-icon
                  :name="moreList[0] ? 'arrow-down' : 'arrow-up'"
                  size="20"
                  color="#909399"
                  @click="toggleMore(0)"
                />
              </template>
            </baseTitle>
            <div v-show="moreList[0]">
              <van-field
                v-model="ruleForm.applyDate"
                name="applyDate"
                label="申请日期"
                placeholder="请选择申请日期"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules.applyDate"
                required
                @click="openDatePicker('applyDate')"
              />
              <van-field
                :value="ruleForm.defaultVehiclePlateNumber"
                name="defaultVehiclePlateNumber"
                label="选择车辆"
                placeholder="请选择车辆"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules.defaultVehiclePlateNumber"
                required
                @click="showPlateNumberPicker = true"
              />
              <van-field
                :value="ruleForm.defaultDriverDossierName"
                name="defaultDriverDossierId"
                label="选择司机"
                placeholder="请选择司机"
                readonly
                input-align="right"
                error-message-align="right"
                is-link
                :border="false"
                :rules="rules.defaultDriverDossierId"
                required
                @click="showDriverPicker = true"
              />
              <van-field
                :value="ruleForm.supercargoDossierOneName"
                label="选择押运工1"
                placeholder="请选择押运工1"
                readonly
                input-align="right"
                is-link
                :border="false"
                @click="showDossierOnePicker = true"
              />
              <van-field
                :value="ruleForm.supercargoDossierTwoName"
                label="选择押运工2"
                placeholder="请选择押运工2"
                readonly
                input-align="right"
                is-link
                :border="false"
                @click="showDossierTwoPicker = true"
              />
            </div>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="收运点位">
              <template #right>
                <div class="flex-h" @click="showPopup = true">
                  <van-icon name="plus" size="20" color="#4CA786" />
                  <div class="add-text">添加点位</div>
                </div>
              </template>
            </baseTitle>
            <ul class="list" v-if="ruleForm.overtimeApplyPointList.length > 0">
              <li class="list-item" v-for="item in ruleForm.overtimeApplyPointList" :key="item.id">
                <div class="list-content">
                  <div class="item-title">{{ item.productionUnit }}</div>
                  <baseTitle title="基础信息">
                    <template #right>
                      <van-icon
                        :name="item.moreList[0] ? 'arrow-down' : 'arrow-up'"
                        size="20"
                        color="#909399"
                        @click="toggleItemMore(item, 0)"
                      />
                    </template>
                  </baseTitle>
                  <div v-show="item.moreList[0]">
                    <van-field
                      v-model="item.productionUnit"
                      name="productionUnit"
                      label="产废单位名称"
                      placeholder="请填写产废单位名称"
                      input-align="right"
                      error-message-align="right"
                      :border="false"
                      :rules="[{ required: true, message: '请填写产废单位名称' }]"
                      required
                      maxlength="50"
                      readonly
                    />
                    <van-field
                      v-model="item.productionUnitOperator"
                      name="productionUnitOperator"
                      label="产废单位经办人"
                      placeholder="请填写产废单位经办人"
                      input-align="right"
                      error-message-align="right"
                      label-width="120"
                      :border="false"
                      maxlength="50"
                      readonly
                    />
                    <van-field
                      v-model="item.address"
                      name="address"
                      label="产废单位地址"
                      placeholder="请填写产废单位地址"
                      input-align="right"
                      error-message-align="right"
                      :border="false"
                      readonly
                    >
                    </van-field>
                    <van-field
                      name="operation"
                      label="点位是否正常经营"
                      input-align="right"
                      error-message-align="right"
                      label-width="130"
                      :border="false"
                      :rules="[{ required: true, message: '请选择点位是否正常经营' }]"
                      required
                    >
                      <template #input>
                        <van-radio-group v-model="item.operation" direction="horizontal" checked-color="#4CA786">
                          <van-radio :name="1">是</van-radio>
                          <van-radio :name="0">否</van-radio>
                        </van-radio-group>
                      </template>
                    </van-field>
                    <van-field
                      name="isRubbish"
                      label="点位是否产生垃圾"
                      input-align="right"
                      error-message-align="right"
                      label-width="130"
                      :border="false"
                      :rules="[{ required: true, message: '请选择点位是否产生垃圾' }]"
                      required
                      v-if="item.operation === 1"
                    >
                      <template #input>
                        <van-radio-group v-model="item.isRubbish" direction="horizontal" checked-color="#4CA786">
                          <van-radio :name="1">是</van-radio>
                          <van-radio :name="0">否</van-radio>
                        </van-radio-group>
                      </template>
                    </van-field>
                    <van-field label="废物类型" label-width="100%" :border="false" required />
                    <van-field
                      :name="child.key"
                      :label="child.name + '重量(kg)'"
                      input-align="right"
                      error-message-align="right"
                      label-width="120"
                      required
                      :rules="[{ required: true, message: `请填写${child.name}` }]"
                      :border="false"
                      v-for="(child, childIndex) in trashList"
                      :key="childIndex"
                    >
                      <template #input>
                        <van-stepper
                          v-model="item[child.key]"
                          min="0"
                          max="99999999"
                          :decimal-length="2"
                          default-value=""
                          input-width="80px"
                        />
                      </template>
                    </van-field>
                    <van-field
                      name="baggingMethod"
                      label="垃圾存储方式"
                      input-align="right"
                      error-message-align="right"
                      label-width="120px"
                      :border="false"
                      :rules="[{ required: true, message: '请选择垃圾存储方式' }]"
                      required
                    >
                      <template #input>
                        <van-radio-group v-model="item.baggingMethod" direction="horizontal" checked-color="#4CA786">
                          <van-radio :name="childIndex" v-for="(child, childIndex) in BARRELS_BAGS" :key="childIndex">{{
                            child
                          }}</van-radio>
                        </van-radio-group>
                      </template>
                    </van-field>
                  </div>
                  <baseTitle title="点位状态">
                    <template #right>
                      <van-icon
                        :name="item.moreList[1] ? 'arrow-down' : 'arrow-up'"
                        size="20"
                        color="#909399"
                        @click="toggleItemMore(item, 1)"
                      />
                    </template>
                  </baseTitle>
                  <div v-show="item.moreList[1]">
                    <van-field
                      name="isClear"
                      label="是否清空"
                      input-align="right"
                      error-message-align="right"
                      label-width="120px"
                      :border="false"
                      :rules="[{ required: true, message: '请选择是否清空' }]"
                      required
                    >
                      <template #input>
                        <van-radio-group v-model="item.isClear" direction="horizontal" checked-color="#4CA786">
                          <van-radio :name="1">是</van-radio>
                          <van-radio :name="0">否</van-radio>
                        </van-radio-group>
                      </template>
                    </van-field>
                    <van-field
                      name="residueRubbish"
                      label="点位剩余废物重量(kg)"
                      input-align="right"
                      error-message-align="right"
                      label-width="120"
                      :border="false"
                      :rules="[{ required: true, message: '请填写点位剩余废物重量' }]"
                      required
                      v-if="item.isClear == 0"
                    >
                      <template #input>
                        <van-stepper
                          v-model="item.residueRubbish"
                          min="0"
                          max="99999999"
                          :decimal-length="2"
                          default-value=""
                          input-width="80px"
                        />
                      </template>
                    </van-field>
                  </div>
                  <baseTitle title="图片信息">
                    <template #right>
                      <van-icon
                        :name="item.moreList[2] ? 'arrow-down' : 'arrow-up'"
                        size="20"
                        color="#909399"
                        @click="toggleItemMore(item, 2)"
                      />
                    </template>
                  </baseTitle>
                  <div v-show="item.moreList[2]">
                    <van-field :border="false" required label-width="100%">
                      <template #label>
                        <div class="label-box">
                          <span class="label-title">收运照片</span>
                          <span class="label-text">(请拍摄收运完成后的图片/点位剩余医疗废物照片)</span>
                        </div>
                      </template>
                    </van-field>
                    <imageUpload
                      v-model="item.picture"
                      :isRequired="true"
                      :capture="'camera'"
                      :formId="'picture_' + item.id"
                      formPlaceHolder="请拍摄收运完成后的图片/点位剩余医疗废物照片"
                    ></imageUpload>
                  </div>
                </div>
                <van-icon class="delete-icon" name="cross" size="20" color="#939c99" @click="deleteItem(item)" />
              </li>
            </ul>
            <van-empty description="暂无收运点位数据" v-else />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showPlateNumberPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="finalName"
        :columns="carOptions"
        :default-index="defaultPlateNumberIndex"
        @confirm="onCarConfirm"
        @cancel="onCarCancel"
      />
    </van-popup>
    <van-popup v-model="showDriverPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="driverOptions"
        :default-index="defaultDriverIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'defaultDriverDossier', 'showDriverPicker')"
        @cancel="onDriverShipWorkerCancel('defaultDriverDossier', 'showDriverPicker')"
      />
    </van-popup>
    <van-popup v-model="showDossierOnePicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierOneIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierOne', 'showDossierOnePicker')"
        @cancel="onDriverShipWorkerCancel('supercargoDossierOne', 'showDossierOnePicker')"
      />
    </van-popup>
    <van-popup v-model="showDossierTwoPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="fullName"
        :columns="shipWorkerOptions"
        :default-index="defaultDossierTwoIndex"
        @confirm="onDriverShipWorkerConfirm($event, 'supercargoDossierTwo', 'showDossierTwoPicker')"
        @cancel="onDriverShipWorkerCancel('supercargoDossierTwo', 'showDossierTwoPicker')"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showPopup" position="right">
      <template v-if="showPopup">
        <div class="popup-container">
          <point
            :originalTable="ruleForm.overtimeApplyPointList"
            @closePopup="showPopup = false"
            @selectItem="selectItem"
          ></point>
        </div>
      </template>
    </van-popup>
    <mapContainer @initMap="initMap" class="map-box" mapId="current-position"></mapContainer>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, RECEIVING_CONDITION, POINT_TASK_TYPE, BARRELS_BAGS } from "@/enums";
  import moment from "moment";
  import point from "@/views/archivesManage/receiveRoute/components/point";
  import imageUpload from "@/components/imageUpload";
  import mapContainer from "@/components/mapContainer";
  import { deepCopy } from "@/utils";
  export default {
    components: {
      baseTitle,
      point,
      imageUpload,
      mapContainer,
    },
    data() {
      return {
        submitFormThrottling: () => {},
        apis: {
          info: "/api/waybill/waybillDetail/listPage",
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          create: "/api/overtime/apply",
        },
        baseForm: {}, //业务基础信息
        ruleForm: {
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //司机
          defaultDriverDossierName: "", //司机
          supercargoDossierOneId: "", //押运工1
          supercargoDossierOneName: "", //押运工1
          supercargoDossierTwoId: "", //押运工2
          supercargoDossierTwoName: "", //押运工2
          applyDate: moment().format("YYYY-MM-DD"), //申请日期
          overtimeApplyPointList: [], //点位列表
          userLongitude: "", //经度
          userLatitude: "", //纬度
        },
        rules: {
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机" }],
        },
        pointObj: {
          id: "",
          productionUnit: "", //产废单位名称
          productionUnitOperator: "", //产废单位经办人
          address: "", //产废单位地址
          operation: 1, //点位是否正常经营
          isRubbish: 1, //点位是否产生垃圾
          infectiousWaste: "", //感染性废物
          damagingWaste: "", //损伤性废物
          pharmaceuticalWaste: "", //药物性废物
          pathologicalWaste: "", //病理性废物
          chemicalWaste: "", //化学性废物
          sludge: "", //污泥
          baggingMethod: "", //垃圾存储方式
          isClear: "", //是否清空
          residueRubbish: "", //点位剩余废物重量
          rubbishTotal: 0, //废物总量
          picture: [],
        },
        trashList: [
          {
            name: "感染性",
            value: "",
            key: "infectiousWaste",
          },
          {
            name: "损伤性",
            value: "",
            key: "damagingWaste",
          },
          {
            name: "药物性",
            value: "",
            key: "pharmaceuticalWaste",
          },
          {
            name: "病理性",
            value: "",
            key: "pathologicalWaste",
          },
          {
            name: "化学性",
            value: "",
            key: "chemicalWaste",
          },
          {
            name: "感染性一污泥",
            value: "",
            key: "sludge",
          },
        ],
        carOptions: [],
        showPlateNumberPicker: false,
        defaultPlateNumberIndex: 0,
        driverOptions: [],
        showDriverPicker: false,
        defaultDriverIndex: 0,
        shipWorkerOptions: [],
        showDossierOnePicker: false,
        defaultDossierOneIndex: 0,
        showDossierTwoPicker: false,
        defaultDossierTwoIndex: 0,
        tableData: [],
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        RECEIVING_CONDITION,
        POINT_TASK_TYPE,
        BARRELS_BAGS,
        showTypePicker: false,
        showDatePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        minDate: new Date(moment().startOf("month").format("YYYY/MM/DD")),
        maxDate: new Date(),
        moreList: [true],
        showPopup: false,
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 初始化地图
      initMap(map) {
        if (map) {
          this.map = map;
          window.AMap.plugin("AMap.Geolocation", () => {
            let geolocation = new window.AMap.Geolocation({
              enableHighAccuracy: true, //是否使用高精度定位，默认:true
              timeout: 10000, //超过10秒后停止定位，默认：无穷大
              maximumAge: 0, //定位结果缓存0毫秒，默认：0
              convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
              showButton: true, //显示定位按钮，默认：true
              buttonPosition: "LB", //定位按钮停靠位置，默认：'LB'，左下角
              showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
              showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
              panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
              zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
            });
            geolocation.getCurrentPosition((status, result) => {
              if (status == "complete") {
                this.ruleForm.userLongitude = result.position.lng;
                this.ruleForm.userLatitude = result.position.lat;
              }
            });
          });
        }
      },
      // 展开/收缩
      toggleMore(index) {
        this.moreList.splice(index, 1, !this.moreList[index]);
      },
      // 展开/收缩
      toggleItemMore(item, index) {
        item.moreList.splice(index, 1, !item.moreList[index]);
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data.map((item) => {
          return {
            ...item,
            finalName: item.plateNumber + (item.isFree === 1 ? "" : "（空闲）"),
          };
        });
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 打开选择日期弹窗
      openDatePicker(field) {
        this.currentDateField = field;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm[this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 选择收运车辆
      async onCarConfirm(item) {
        if (this.ruleForm.defaultVehiclePlateNumber == item.plateNumber) {
          this.showPlateNumberPicker = false;
          return;
        }
        let promiseList = [
          createApiFun({ userIdentity: "3", plateNumber: item.plateNumber }, this.apis.driverAndWorkerInfo),
          createApiFun({ userIdentity: "4", plateNumber: item.plateNumber }, this.apis.driverAndWorkerInfo),
        ];
        let res = await Promise.all(promiseList);
        let driverInfo = res[0].data;
        let workerInfo = res[1].data;
        if (driverInfo) {
          this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
          this.ruleForm.defaultDriverDossierName = driverInfo.fullName;
          let defaultDriverIndex = this.driverOptions.findIndex((item) => item.lgUnionId == driverInfo.lgUnionId);
          if (defaultDriverIndex >= 0) {
            this.defaultDriverIndex = defaultDriverIndex;
          }
        } else {
          this.ruleForm.defaultDriverDossierId = "";
          this.ruleForm.defaultDriverDossierName = "";
        }
        if (workerInfo) {
          this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
          this.ruleForm.supercargoDossierOneName = workerInfo.fullName;
          let defaultDossierOneIndex = this.shipWorkerOptions.findIndex(
            (item) => item.lgUnionId == workerInfo.lgUnionId,
          );
          if (defaultDossierOneIndex >= 0) {
            this.defaultDossierOneIndex = defaultDossierOneIndex;
          }
        } else {
          this.ruleForm.supercargoDossierOneId = "";
          this.ruleForm.supercargoDossierOneName = "";
        }
        this.ruleForm.defaultVehiclePlateNumber = item.plateNumber;
        this.showPlateNumberPicker = false;
      },
      // 取消选择
      onCarCancel() {
        if (this.ruleForm.defaultVehiclePlateNumber) {
          this.ruleForm.defaultVehiclePlateNumber = "";
        }
        this.showPlateNumberPicker = false;
      },
      // 选择驾驶司机、押运工
      onDriverShipWorkerConfirm(item, field, pickerPopup) {
        this.ruleForm[`${field}Id`] = item.lgUnionId;
        this.ruleForm[`${field}Name`] = item.fullName;
        this[pickerPopup] = false;
      },
      // 取消选择
      onDriverShipWorkerCancel(field, pickerPopup) {
        if (this.ruleForm[`${field}Id`]) {
          this.ruleForm[`${field}Id`] = "";
          this.ruleForm[`${field}Name`] = "";
        }
        this[pickerPopup] = false;
      },
      // 添加/删除点位
      selectItem(obj) {
        if (obj.flag) {
          let index = this.ruleForm.overtimeApplyPointList.findIndex((item) => item.id == obj.item.id);
          if (index >= 0) {
            this.ruleForm.overtimeApplyPointList.splice(index, 1);
          }
        } else {
          let point = deepCopy(this.pointObj);
          point.productionUnit = obj.item.name;
          point.productionUnitOperator = obj.item.contact;
          point.address = obj.item.address;
          point.id = obj.item.id;
          point.baggingMethod = obj.item.baggingMethod;
          this.ruleForm.overtimeApplyPointList.push({ ...point, moreList: [true, true, true] });
        }
      },
      // 删除点位
      deleteItem(item) {
        let index = this.ruleForm.overtimeApplyPointList.findIndex((list) => list.id == item.id);
        if (index >= 0) {
          this.ruleForm.overtimeApplyPointList.splice(index, 1);
        }
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            if (this.ruleForm.overtimeApplyPointList.length == 0) {
              this.$toast({
                forbidClick: true,
                type: "fail",
                message: `收运点位不能为空`,
                duration: 1500,
              });
              return;
            }
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            try {
              let res = await createApiFun({ ...this.ruleForm, id: this.id }, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "提交成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-flex {
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .warning-text {
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      text-align: center;
      margin-top: 10px;
    }
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
    flex: 1;
  }
  .tips-icon {
    font-size: 50px;
    color: #ff7d00;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .list {
    padding-bottom: 12px;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    .list-index {
      width: 18px;
      height: 18px;
      background-color: #4ca786;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      margin-right: 10px;
    }
    .list-content {
      flex: 1;
      overflow: hidden;
    }
    .delete-icon {
      position: absolute;
      top: 12px;
      right: 12px;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
    margin-bottom: 10px;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .add-text {
    color: #4ca786;
    font-size: 14px;
    margin-left: 4px;
  }
  .map-box {
    position: fixed;
    top: -9999px;
    left: -9999px;
    visibility: hidden;
  }
  .popup-container {
    width: 100vw;
    height: 100vh;
    height: 100dvh;
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .el-switch__label.is-active {
    color: #4ca786;
  }
  ::v-deep .detail-type {
    padding: 0;
    padding-left: 5px;
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  ::v-deep .detail-type::before {
    left: 0;
  }
  ::v-deep .detail-type .van-cell__right-icon {
    font-size: 14px;
    height: 0;
    line-height: 13px;
  }
</style>
