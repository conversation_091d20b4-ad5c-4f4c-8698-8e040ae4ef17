<template>
  <div class="page-container">
    <header class="page-header">
      <van-field
        v-model="appletKeyword"
        label=""
        placeholder="请输入路线名称/车牌号/司机名称"
        right-icon="search"
        clearable
        @change="onRefresh"
        @clear="onRefresh"
      >
        <template #right-icon>
          <span class="sctmp-iconfont icon-ic_sousuo search-icon"></span>
        </template>
      </van-field>
    </header>
    <main class="page-main">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" @load="onLoad" :immediate-check="false">
          <ul class="list" v-if="tableList.length > 0">
            <li class="list-item" v-for="(item, index) in tableList" :key="index">
              <div class="item-content" @click="itemClick(item)">
                <div class="item-left">
                  <div class="item-title">路线名称：{{ item.name }}</div>
                  <div class="item-text">路线编号：{{ item.code }}</div>
                  <div class="item-text">路线版本号：{{ item.versionNumber.toFixed(1) }}</div>
                  <div class="item-text">路线状态：{{ ROUTE_STATUS[item.status] }}</div>
                  <div class="item-text">点位数量：{{ item.pointNumber }}</div>
                  <div class="item-text">收运单生效日期：{{ item.effectiveDate }}</div>
                  <div class="item-text">收运单编号：{{ item.waybillCode }}</div>
                  <div class="item-text">所属区域：{{ item.districtName }}</div>
                  <div class="item-text">路线属性：{{ ROUTE_PROPERTY[item.type] }}</div>
                  <div class="item-text">默认车辆：{{ item.defaultVehiclePlateNumber }}</div>
                  <div class="item-text">默认司机：{{ item.defaultDriverDossierName }}</div>
                  <div class="item-text">司机联系方式：{{ item.defaultDriverDossierPhone }}</div>
                  <div v-show="item.showMore">
                    <div class="item-text">押运工1：{{ item.supercargoDossierOneName }}</div>
                    <div class="item-text">押运工联系方式：{{ item.supercargoDossierOnePhone }}</div>
                    <div class="item-text">押运工2：{{ item.supercargoDossierTwoName }}</div>
                    <div class="item-text">押运工联系方式：{{ item.supercargoDossierTwoPhone }}</div>
                  </div>
                </div>
                <div class="item-right">
                  <span class="sctmp-iconfont icon-ic_xiazuan"></span>
                </div>
              </div>
              <div class="item-footer">
                <div class="footer-box" @click="item.showMore = !item.showMore">
                  <div class="footer-text">{{ item.showMore ? "收起" : "展开" }}更多</div>
                  <span class="sctmp-iconfont icon-ic_zhankai footer-icon" v-show="!item.showMore"></span>
                  <span class="sctmp-iconfont icon-ic_shouqi footer-icon" v-show="item.showMore"></span>
                </div>
              </div>
            </li>
          </ul>
          <van-empty description="暂无数据" v-else />
        </van-list>
      </van-pull-refresh>
    </main>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { ROUTE_STATUS, ROUTE_PROPERTY } from "@/enums";
  import components from "@/components/pageList/componentImport";
  export default {
    components: components,
    data() {
      return {
        loading: false,
        finished: false,
        refreshing: false,
        pageNo: 1,
        pageSize: 10,
        total: 0,
        listPageApi: "/api/waybill/listPage",
        tableList: [],
        ROUTE_STATUS,
        ROUTE_PROPERTY,
        appletKeyword: "",
      };
    },
    mounted() {
      this.onLoad();
    },
    methods: {
      // 初始化数据
      async onLoad() {
        if (this.refreshing) {
          this.tableList = [];
          this.pageNo = 1;
          this.refreshing = false;
        }
        let params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          effectiveDate: this.$route.query.effectiveDate,
          appletKeyword: this.appletKeyword,
          issueStatus: 0,
        };
        // 请求接口分页列表数据
        try {
          let res = await getListPageApiFun(params, this.listPageApi);
          if (res.success) {
            let dataList = res.data.datas.map((item) => {
              return {
                ...item,
                defaultDriverDossierPhone: item.defaultDriverDossierPhone
                  ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                  : "",
                supercargoDossierOnePhone: item.supercargoDossierOnePhone
                  ? this.$sm2Decrypt(item.supercargoDossierOnePhone)
                  : "",
                supercargoDossierTwoPhone: item.supercargoDossierTwoPhone
                  ? this.$sm2Decrypt(item.supercargoDossierTwoPhone)
                  : "",
              };
            });
            dataList.forEach((list) => {
              for (let key in list) {
                list[key] = list[key] || list[key] === 0 ? list[key] : "-";
                list.showMore = false;
              }
            });
            this.tableList = this.tableList.concat(dataList);
            this.total = res.data.total;
            this.pageNo++;
            if (this.tableList.length >= this.total) {
              this.finished = true;
            }
          } else {
            this.finished = true;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新
      onRefresh() {
        this.finished = false;
        this.refreshing = true;
        this.loading = true;
        this.onLoad();
      },
      // 列表项点击事件
      itemClick(item) {
        let params = {
          waybillId: item.id,
        };
        this.$commonSkip("electronicWaybillAuditRecord", params);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f0f2f1;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
  }
  .page-main {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .item-left {
    flex: 1;
    overflow: hidden;
    padding-right: 4px;
  }
  .item-right {
    span {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2e3432;
    line-height: 16px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 14px;
    margin-top: 8px;
    word-break: break-all;
  }
  .item-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    .footer-box {
      padding: 6px 8px;
      background: #f0f2f1;
      border-radius: 3px;
      opacity: 0.5;
      display: flex;
      align-items: center;
      .footer-text {
        font-weight: 400;
        font-size: 12px;
        color: #5c6663;
        line-height: 14px;
        margin-right: 4px;
      }
      .footer-icon {
        font-size: 14px;
        color: #5c6663;
      }
    }
  }
  .search-icon {
    font-size: 16px;
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell {
    padding: 10px 12px;
    background: #f7faf9;
    border-radius: 4px;
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
  ::v-deep .page-header .van-field__right-icon {
    color: #c4ccc9;
  }
  ::v-deep .page-header .van-cell .van-field__body .van-field__control::placeholder {
    font-size: 14px;
    color: #c4ccc9;
    line-height: 16px;
  }
</style>
