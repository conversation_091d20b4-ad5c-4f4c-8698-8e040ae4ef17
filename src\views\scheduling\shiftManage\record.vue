<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { IS_NORMAL_OPERATION } from "@/enums";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/waybill/change/get/",
        },
        recordList: [
          {
            title: "换班详情",
            recordFieldArr: [
              {
                key: "applyFullName",
                value: "申请人名称",
              },
              {
                key: "applyPhone",
                value: "申请人联系电话",
              },
              {
                key: "changeStartDate",
                value: "换班开始日期",
              },
              {
                key: "changeEndDate",
                value: "换班结束日期",
              },
              {
                key: "remark",
                value: "详情备注",
              },
              {
                key: "applyStatus",
                value: "是否批准换班",
                options: IS_NORMAL_OPERATION,
              },
              {
                key: "isChange",
                value: "是否安排顶班人员",
                options: IS_NORMAL_OPERATION,
                connectField: "applyStatus",
                connectValue: [1],
              },
              {
                key: "changeFullName",
                value: "顶班人员",
                connectField: "isChange",
                connectValue: [1],
              },
              {
                key: "auditDate",
                value: "处理日期",
              },
              {
                key: "auditFullName",
                value: "处理人员",
              },
            ],
          },
        ],
        fileList: [],
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped></style>
