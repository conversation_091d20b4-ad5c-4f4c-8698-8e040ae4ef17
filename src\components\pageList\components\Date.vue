<template>
  <div>
    <van-field
      v-model="_value"
      :label="filterItem.value"
      :placeholder="`请选择${filterItem.value}`"
      input-align="right"
      is-link
      arrow-direction="down"
      :label-width="labelWidth"
      readonly
      :border="false"
      @click="handleClick"
    />
    <van-popup v-model="showPicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="filterItem.minDate"
        :max-date="filterItem.maxDate"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import componentMinxins from "./mixins";
  import moment from "moment";
  export default {
    mixins: [componentMinxins],
    data() {
      return {
        showPicker: false,
        currentDate: new Date(),
      };
    },
    methods: {
      // 点击
      handleClick() {
        this.showPicker = true;
      },
      // 确认
      onConfirm(value) {
        this._value = moment(value).format("YYYY-MM-DD");
        this.showPicker = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import "./common.scss";
</style>
