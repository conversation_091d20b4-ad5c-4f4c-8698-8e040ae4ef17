<template>
  <pageRecord class="main" :id="id" :recordApi="apis.info" :recordList="recordList">
    <template #default="{ form }">
      <ul class="record-box" v-if="form.detailList">
        <div class="record-title"
          ><svg class="title-icon mr-4" aria-hidden="true">
            <use xlink:href="#icon-ic_biaotizhuangshi"></use></svg
          >{{ SELFTEST_STATUS[form.type] }}</div
        >
        <li class="record-item" v-for="(item, index) in form.detailList" :key="index">
          <div class="record-left">{{ item.configName }}</div>
          <div class="record-right" :class="{ active: item.status == 1 }">{{ EVALUATE_STATUS[item.status] }}</div>
        </li>
      </ul>
    </template>
  </pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { SELFTEST_STATUS, EVALUATE_STATUS } from "@/enums";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicle/inspect/get/",
        },
        recordList: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "driverName",
                value: "驾驶司机",
              },
              {
                key: "type",
                value: "自检类型",
                options: SELFTEST_STATUS,
              },
              {
                key: "inspectTime",
                value: "自检时间",
              },
              {
                key: "status",
                value: "检查状态",
                options: EVALUATE_STATUS,
                activeValue: 1,
                activeColor: "#f53f3f",
              },
              {
                key: "fileList",
                value: "车辆照片",
                isImage: true,
              },
            ],
          },
        ],
        EVALUATE_STATUS,
        SELFTEST_STATUS,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
  };
</script>

<style lang="scss" scoped>
  .main {
    background: #f7faf9;
    ::v-deep .record-box:first-child {
      margin: 12px;
    }
  }
  .record-title {
    font-weight: 500;
    font-size: 16px;
    color: #4ca786;
    line-height: 16px;
    padding: 10px 0;
    display: flex;
    align-items: center;
  }
  .record-box {
    margin: 12px;
    padding: 12px 24px;
    background-color: #fff;
    border-radius: 8px;
    margin-top: 12px;
    .title-icon {
      width: 16px;
      height: 16px;
    }
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    &.active {
      color: #f53f3f;
    }
  }
</style>
