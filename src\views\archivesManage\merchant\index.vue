<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    @itemClick="itemClick"
    searchField="keyword"
    searchPlaceholder="请输入客商名称/客商编号"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import { CUSTOMER_TYPE, CUSTOMER_STATUS, CREDIT_STATUS } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "name",
            value: "客商名称",
          },
          {
            key: "code",
            value: "客商编号",
          },
          {
            key: "type",
            value: "客户类型",
            enums: CUSTOMER_TYPE,
          },
          {
            key: "status",
            value: "客商状态",
            enums: CUSTOMER_STATUS,
          },
          {
            key: "creditStatus",
            value: "信用状态",
            enums: CREDIT_STATUS,
          },
          {
            key: "address",
            value: "详细地址",
          },
        ],
        apis: {
          listPage: "/api/merchant/merchantFile/listPage",
        },
        filterList: [
          {
            type: "Options",
            key: "type",
            value: "客户类型",
            enums: CUSTOMER_TYPE,
          },
          {
            type: "Options",
            key: "status",
            value: "客商状态",
            enums: CUSTOMER_STATUS,
          },
          {
            type: "Options",
            key: "creditStatus",
            value: "信用状态",
            enums: CREDIT_STATUS,
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("merchantRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
