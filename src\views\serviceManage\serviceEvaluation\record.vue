<template>
  <div class="create-container">
    <div class="create-box">
      <div class="create-content">
        <div v-for="(field, fieldIndex) in fieldList" :key="fieldIndex">
          <div class="border-line" v-if="fieldIndex > 0"></div>
          <main class="create-main">
            <baseTitle :title="field.title"></baseTitle>
            <div class="record-item" v-for="(item, index) in field.dataList" :key="index">
              <div class="file-box" v-if="item.isFile">
                <div class="record-left"
                  >{{ item.name }}<el-link type="info" :underline="false">（文件请前往管理端下载）</el-link></div
                >
                <div
                  class="file-right"
                  v-if="ruleForm[item.key] && (isJSON(ruleForm[item.key]) || Array.isArray(ruleForm[item.key]))"
                >
                  <div class="link-block" v-for="(fileItem, fileIndex) in ruleForm[item.key]" :key="fileIndex">
                    <el-link type="primary" @click="previewImage(fileItem)">{{ fileItem.name }}</el-link>
                  </div>
                </div>
                <div class="file-right" v-else>-</div>
              </div>
              <template v-else>
                <div class="record-left">{{ item.name }}</div>
                <div class="record-right">
                  <van-rate
                    :value="ruleForm[item.key]"
                    color="#ffd21e"
                    void-icon="star"
                    void-color="#eee"
                    v-if="item.type == 'rate'"
                  />
                  <span v-else>{{ item.enums ? item.enums[ruleForm[item.key]] : ruleForm[item.key] }}</span>
                </div>
              </template>
            </div>
          </main>
        </div>
        <div class="border-line"></div>
        <main class="create-main">
          <baseTitle title="对话情况"></baseTitle>
          <ul class="dialogue-list">
            <li class="dialogue-item" v-for="item in ruleForm.dialogueList" :key="item.id">
              <div class="dialogue-left"></div>
              <div class="dialogue-right">
                <div class="dialogue-box">
                  <div class="dialogue-label">对话日期：</div>
                  <div class="dialogue-content">{{ item.dialogueDate }}</div>
                </div>
                <div class="dialogue-box">
                  <div class="dialogue-label">状态：</div>
                  <div class="dialogue-content" :class="[item.status == 0 ? 'warning' : 'success']">{{
                    DIALOGUE_STATUS[item.status]
                  }}</div>
                </div>
                <div class="dialogue-box">
                  <div class="dialogue-label">对话内容：</div>
                  <div class="dialogue-content">{{ item.dialogueContent }}</div>
                </div>
                <div class="dialogue-box">
                  <div class="dialogue-label">回复内容：</div>
                  <div class="dialogue-content">{{ item.replyContent || "-" }}</div>
                </div>
              </div>
            </li>
          </ul>
        </main>
      </div>
    </div>
    <footer class="create-footer" v-if="currentLevel == 199 && userInfo.lgUnionId === ruleForm.lgUnionId">
      <van-button native-type="button" class="round-22 ml-11" block type="info" color="#4CA786" @click="openDialogue"
        >发起对话</van-button
      >
    </footer>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="0"></van-image-preview>
    <van-popup v-model="showDialogue" round position="bottom">
      <div class="popup-content">
        <van-field
          v-model.trim="dialogueContent"
          rows="5"
          autosize
          label=""
          type="textarea"
          placeholder="请输入对话内容"
          border
        />
      </div>
      <div class="popup-footer">
        <van-button
          native-type="button"
          class="round-22"
          block
          type="info"
          color="#4CA786"
          @click="submitFormThrottling"
          >留言</van-button
        >
      </div>
    </van-popup>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { isJSON } from "@/utils";
  import { getUserInfo, getCurrentLevel } from "@/utils/storage";
  export default {
    components: {
      baseTitle,
    },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/serviceEvaluationRecord/get/",
          create: "/api/serviceEvaluationRecord/dialogue/create",
        },
        fieldList: [
          {
            title: "基础信息",
            dataList: [
              { key: "customerName", name: "客商名称" },
              { key: "customerContactNumber", name: "客商联系电话" },
              { key: "customerEmail", name: "客商电子邮件" },
              { key: "evaluationDate", name: "评价日期" },
              { key: "returnDocumentNumber", name: "回单据编号" },
            ],
          },
          {
            title: "评价情况",
            dataList: [
              { key: "star", name: "评价等级", type: "rate" },
              { key: "content", name: "评价情况" },
            ],
          },
          {
            title: "附件信息",
            dataList: [{ key: "fileList", name: "附件", isFile: true }],
          },
        ],
        ruleForm: {},
        isJSON,
        previewImages: [],
        showPreview: false,
        DIALOGUE_STATUS: ["待回复", "已回复"],
        showDialogue: false,
        dialogueContent: "",
        submitFormThrottling: () => {},
        userInfo: "",
        currentLevel: "",
      };
    },
    created() {
      this.id = this.$route.query.id;
      this.userInfo = getUserInfo();
      this.currentLevel = getCurrentLevel();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取合同详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          for (let key in this.ruleForm) {
            this.ruleForm[key] = this.ruleForm[key] || this.ruleForm[key] === 0 ? this.ruleForm[key] : "-";
          }
        }
      },
      // 预览附件
      previewImage(item) {
        let suffix = item.url.slice(item.url.lastIndexOf("."));
        if ([".png", ".jpg", ".jpeg", ".gif"].includes(suffix)) {
          this.previewImages = [item.url];
          this.showPreview = true;
        }
      },
      // 打开对话
      openDialogue() {
        this.dialogueContent = "";
        this.showDialogue = true;
      },
      async onSubmit() {
        if (!this.dialogueContent) {
          this.$toast({
            forbidClick: true,
            type: "fail",
            message: "对话内容不能为空",
            duration: 1500,
          });
          return;
        }
        this.$toast({
          type: "loading",
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });
        try {
          let res = await createApiFun(
            { id: this.$route.query.id, dialogueContent: this.dialogueContent },
            this.apis.create,
          );
          if (res.success) {
            this.$toast.clear();
            if (res.success) {
              this.showDialogue = false;
              this.getRecord();
              this.$toast({
                type: "success",
                message: "留言成功",
                forbidClick: true,
                duration: 1500,
              });
            }
          }
        } catch (error) {
          this.$toast.clear();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .create-box {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 24px;
  }
  .record-item {
    padding: 12px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 16px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 16px;
    text-align: right;
    word-break: break-all;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .file-right {
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    word-break: break-all;
    margin-top: 4px;
  }
  .link-block {
    padding-bottom: 4px;
  }
  .dialogue-list {
    padding-bottom: 10px;
  }
  .dialogue-item {
    display: flex;
    padding: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 12px;
    .dialogue-left {
      width: 16px;
      height: 16px;
      background-color: #4ca786;
      border-radius: 50%;
      margin-right: 10px;
    }
    .dialogue-right {
      flex: 1;
      overflow: hidden;
      .dialogue-box {
        display: flex;
        padding-bottom: 4px;
        &:last-child {
          padding-bottom: 0;
        }
        .dialogue-label {
          font-weight: 400;
          font-size: 14px;
          color: #162e25;
          line-height: 16px;
          margin-right: 4px;
        }
        .dialogue-content {
          flex: 1;
          font-size: 14px;
          color: #5c6663;
          line-height: 16px;
          word-break: break-all;
          &.warning {
            color: #e6a23c;
          }
          &.success {
            color: #4ca786;
          }
        }
      }
    }
  }
  .round-22 {
    border-radius: 22px;
  }
  .ml-11 {
    margin-left: 11px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .popup-content {
    padding: 10px;
  }
  .popup-footer {
    padding: 12px;
    background-color: #fff;
  }
</style>
