<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              v-model="ruleForm.plateNumber"
              name="plateNumber"
              label="车牌号"
              placeholder="请选择车牌号"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.plateNumber"
              required
              @click="showCarPicker = true"
            />
            <van-field
              :value="headerOptions.join('、')"
              name="type"
              label="维保类型"
              placeholder="请选择维保类型"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rules.type"
              required
              @click="showTypePicker = true"
            />
          </main>
          <div class="border-line"></div>
          <main class="create-main" v-if="ruleForm.type.includes(0)">
            <baseTitle title="常规保养"></baseTitle>
            <van-field
              v-model="ruleForm.dataList[0].operatorName"
              name="operatorName"
              label="经办人"
              placeholder="请输入经办人"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.operatorName"
              required
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.dataList[0].organizationName"
              name="organizationName"
              label="保养单位"
              placeholder="请输入保养单位"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rulesList[0].organizationName"
              required
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.dataList[0].recentMaintenanceTime"
              name="recentMaintenanceTime"
              label="最近保养日期"
              placeholder="请选择最近保养日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rulesList[0].recentMaintenanceTime"
              required
              @click="openDatePicker('recentMaintenanceTime', 0)"
            />
            <div class="tips">
              <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
              <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
            </div>
            <van-field
              v-model="ruleForm.dataList[0].nextMaintenanceTime"
              label="下次保养日期"
              placeholder="请选择下次保养日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              @click="openDatePicker('nextMaintenanceTime', 0)"
            />
            <div class="tips">
              <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
              <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
            </div>
            <van-field
              v-model.number="ruleForm.dataList[0].driveMileage"
              name="driveMileage"
              label="行驶总里程(Km)"
              placeholder="请输入行驶总里程"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rulesList[0].driveMileage"
              required
              label-width="120"
            />
            <van-field
              v-model.number="ruleForm.dataList[0].upkeepMileage"
              name="upkeepMileage"
              label="下次保养里程(Km)"
              placeholder="请输入下次保养里程"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rulesList[0].upkeepMileage"
              required
              label-width="120"
            />
            <div class="tips">
              <span>以当前行驶总里程+5000~10000（km）为下次保养里程</span>
            </div>
            <van-field
              v-model.number="ruleForm.dataList[0].costs"
              name="costs"
              label="保养费用(元)"
              placeholder="请输入保养费用"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rulesList[0].costs"
              required
            />
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">凭证</div>
                  <div class="label-text">(请上传保养凭证)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.dataList[0].vehicleFileList"
              :isRequired="true"
              formId="vehicleFileList"
              formPlaceHolder="请上传保养凭证"
            ></imageUpload>
          </main>
          <div class="border-line"></div>
          <main class="create-main" v-if="ruleForm.type.includes(1)">
            <baseTitle title="二级维护"></baseTitle>
            <van-field
              v-model="ruleForm.dataList[1].operatorName"
              name="operatorName"
              label="经办人"
              placeholder="请输入经办人"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.operatorName"
              required
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.dataList[1].organizationName"
              name="organizationName"
              label="维护单位"
              placeholder="请输入维护单位"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rulesList[1].organizationName"
              required
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.dataList[1].recentMaintenanceTime"
              name="recentMaintenanceTime"
              label="最近二级维护日期"
              placeholder="请选择最近二级维护日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rulesList[1].recentMaintenanceTime"
              required
              label-width="120"
              @click="openDatePicker('recentMaintenanceTime', 1)"
            />
            <div class="tips">
              <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
              <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
            </div>
            <van-field
              v-model="ruleForm.dataList[1].nextMaintenanceTime"
              name="nextMaintenanceTime"
              label="下次二级维护日期"
              placeholder="请选择下次二级维护日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rulesList[1].nextMaintenanceTime"
              required
              label-width="120"
              @click="openDatePicker('nextMaintenanceTime', 1)"
            />
            <div class="tips">
              <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
              <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
            </div>
            <van-field
              v-model.number="ruleForm.dataList[1].costs"
              name="costs"
              label="二级维护费用(元)"
              placeholder="请输入二级维护费用"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rulesList[1].costs"
              required
              label-width="120"
            />
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">凭证</div>
                  <div class="label-text">(请上传二级维护凭证)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.dataList[1].vehicleFileList"
              :isRequired="true"
              formId="vehicleFileList"
              formPlaceHolder="请上传二级维护凭证"
            ></imageUpload>
          </main>
          <div class="border-line"></div>
          <main class="create-main" v-if="ruleForm.type.includes(2)">
            <baseTitle title="维修记录"></baseTitle>
            <van-field
              v-model="ruleForm.dataList[2].operatorName"
              name="operatorName"
              label="经办人"
              placeholder="请输入经办人"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rules.operatorName"
              required
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.dataList[2].organizationName"
              name="organizationName"
              label="维修单位"
              placeholder="请输入维修单位"
              input-align="right"
              error-message-align="right"
              :border="false"
              :rules="rulesList[2].organizationName"
              required
              maxlength="50"
            />
            <van-field
              v-model="ruleForm.dataList[2].recentMaintenanceTime"
              name="recentMaintenanceTime"
              label="最近维修日期"
              placeholder="请选择最近维修日期"
              readonly
              input-align="right"
              error-message-align="right"
              is-link
              :border="false"
              :rules="rulesList[2].recentMaintenanceTime"
              required
              @click="openDatePicker('recentMaintenanceTime', 2)"
            />
            <div class="tips">
              <span class="sctmp-iconfont icon-ic_tishi tips-icon"></span>
              <span>该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
            </div>
            <van-field
              v-model.number="ruleForm.dataList[2].costs"
              name="costs"
              label="维修费用(元)"
              placeholder="请输入维修费用"
              input-align="right"
              error-message-align="right"
              type="number"
              :border="false"
              :rules="rulesList[2].costs"
              required
            />
            <van-field :border="false" required label-width="100%">
              <template #label>
                <div class="label-box">
                  <div class="label-title">凭证</div>
                  <div class="label-text">(请上传维修凭证)</div>
                </div>
              </template>
            </van-field>
            <imageUpload
              v-model="ruleForm.dataList[2].vehicleFileList"
              :isRequired="true"
              formId="vehicleFileList"
              formPlaceHolder="请上传维修凭证"
            ></imageUpload>
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-32"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >提交</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="onCarConfirm"
        @cancel="showCarPicker = false"
      />
    </van-popup>
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        type="date"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showTypePicker" position="bottom" :style="{ height: '20%' }">
      <div class="checkbox-box">
        <van-checkbox-group v-model="ruleForm.type">
          <van-checkbox class="mb-12" :name="item.id" v-for="item in typeOptions" :key="item.id">{{
            item.name
          }}</van-checkbox>
        </van-checkbox-group>
      </div>
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFunByParams } from "@/api/base";
  import { getUserInfo } from "@/utils/storage";
  import moment from "moment";
  import imageUpload from "@/components/imageUpload";
  import { MAINTENANCE_TYPE } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: {
      imageUpload,
      baseTitle,
    },
    data() {
      return {
        userInfo: "",
        submitFormThrottling: () => {},
        apis: {
          create: "/api/vehicleMaintenance/createBatch",
          carList: "/api/vehicle/dossier/list",
          defaultCar: "/api/vehicle/dossier/findByPhone",
        },
        showCarPicker: false,
        carOptions: [],
        defaultCarIndex: 0,
        showDatePicker: false,
        showTypePicker: false,
        currentDate: new Date(),
        currentDateField: "",
        currentDateIndex: "",
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        ruleForm: {
          plateNumber: "",
          type: [], //维保类型
          dataList: [
            {
              type: 0,
              operatorName: "", //经办人
              organizationName: "", //保养单位
              recentMaintenanceTime: "", //最近保养日期
              nextMaintenanceTime: "", //下次保养日期
              driveMileage: "", //行驶总里程
              upkeepMileage: "", //下次保养里程
              costs: "", //保养费用
              vehicleFileList: [], //保养凭证
            },
            {
              type: 1,
              operatorName: "", //经办人
              organizationName: "", //维护单位
              recentMaintenanceTime: "", //最近二级维护日期
              nextMaintenanceTime: "", //下次二级维护日期
              costs: "", //二级维护费用
              vehicleFileList: [], //二级维护凭证
            },
            {
              type: 2,
              operatorName: "", //经办人
              organizationName: "", //维修单位
              recentMaintenanceTime: "", //最近维修日期
              costs: "", //维修费用
              vehicleFileList: [], //维修凭证
            },
          ],
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号" }],
          type: [{ required: true, message: "请选择维保类型" }],
          operatorName: [{ required: true, message: "请输入经办人" }],
        },
        rulesList: [
          {
            organizationName: [{ required: true, message: "请输入保养单位" }],
            recentMaintenanceTime: [{ required: true, message: "请选择最近保养日期" }],
            driveMileage: [{ required: true, message: "请输入行驶总里程" }],
            upkeepMileage: [{ required: true, message: "请输入下次保养里程" }],
            costs: [{ required: true, message: "请输入保养费用" }],
          },
          {
            organizationName: [{ required: true, message: "请输入维护单位" }],
            recentMaintenanceTime: [{ required: true, message: "请选择最近二级维护日期" }],
            nextMaintenanceTime: [{ required: true, message: "请选择下次二级维护日期" }],
            costs: [{ required: true, message: "请输入二级维护费用" }],
          },
          {
            organizationName: [{ required: true, message: "请输入维修单位" }],
            recentMaintenanceTime: [{ required: true, message: "请选择最近维修日期" }],
            costs: [{ required: true, message: "请输入维修费用" }],
          },
        ],
        MAINTENANCE_TYPE,
        // 0-常规保养 1-二级维护 2-维修
        maintenanceTypeOptions: [
          {
            id: 0,
            name: "常规保养",
          },
          {
            id: 1,
            name: "二级维护",
          },
          {
            id: 2,
            name: "维修",
          },
        ],
        operationalNature: "",
      };
    },
    computed: {
      typeOptions() {
        let options = [];
        if (this.operationalNature == 1) {
          options = this.maintenanceTypeOptions.filter((item) => item.id != 1);
        } else {
          options = this.maintenanceTypeOptions;
        }
        return options;
      },
      headerOptions() {
        let options = [];
        let typeArr = this.ruleForm.type;
        if (this.ruleForm.type.length > 0) {
          typeArr = typeArr.sort((a, b) => a - b);
          typeArr.forEach((item) => {
            options.push(MAINTENANCE_TYPE[item]);
          });
        }
        return options;
      },
    },
    created() {
      this.userInfo = getUserInfo();
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.getUserDefaultCar();
      },
      // 获取当前用户默认车牌号
      async getUserDefaultCar() {
        let res = await getInfoApiFunByParams(
          { phone: this.userInfo.phone ? this.$sm2Encrypt(this.userInfo.phone) : "" },
          this.apis.defaultCar,
        );
        if (res.data) {
          this.ruleForm.plateNumber = res.data.plateNumber;
          this.operationalNature = res.data.operationalNature;
          let index = this.carOptions.findIndex((option) => option.id == res.data.id);
          if (index > 0) {
            this.defaultCarIndex = index;
          }
        }
      },
      // 确认选择车牌号
      onCarConfirm(item) {
        if (this.ruleForm.plateNumber === item.name) {
          return;
        }
        this.ruleForm.plateNumber = item.name;
        this.operationalNature = item.operationalNature;
        this.ruleForm.type = [];
        this.showCarPicker = false;
      },
      // 打开选择日期弹窗
      openDatePicker(field, index) {
        this.currentDateField = field;
        this.currentDateIndex = index;
        this.showDatePicker = true;
      },
      // 确认选择日期
      onDateConfirm(value) {
        this.ruleForm.dataList[this.currentDateIndex][this.currentDateField] = moment(value).format("YYYY-MM-DD");
        this.showDatePicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = {};
            let dataList = JSON.parse(JSON.stringify(this.ruleForm.dataList)).filter((list) =>
              this.ruleForm.type.includes(list.type),
            );
            params = JSON.parse(JSON.stringify(this.ruleForm));
            params.dataList = dataList;
            try {
              let res = await createApiFun(params, this.apis.create);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "新增车辆维保记录成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .create-header {
    display: grid;
    background-color: #fff;
    .header-title {
      padding: 17px 0;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #939c99;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-sort {
        margin-bottom: 2px;
      }
      &.active {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        line-height: 22px;
      }
    }
  }
  .ml-11 {
    margin-left: 11px;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #c4ccc9;
    line-height: 20px;
    padding: 0 16px 10px 16px;
    .tips-icon {
      font-size: 16px;
      color: #ff7d00;
    }
  }
  .label-box {
    height: 100%;
    display: flex;
    align-items: center;
  }
  .label-title {
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .label-text {
    font-weight: 400;
    font-size: 12px;
    color: #939c99;
    line-height: 17px;
    margin-left: 4px;
  }
  .checkbox-box {
    padding: 12px;
  }
  .mb-12 {
    margin-bottom: 12px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
