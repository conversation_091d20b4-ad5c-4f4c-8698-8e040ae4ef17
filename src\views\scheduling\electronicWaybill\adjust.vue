<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <van-field
              v-model="ruleForm.waybillName"
              name="waybillId"
              label="路线收运单"
              placeholder="请选择路线收运单"
              readonly
              is-link
              arrow-direction="down"
              colon
              class="custom-field"
              required
              :border="false"
              :rules="rules.waybillId"
              @click="showRouteTaskPicker = true"
            />
            <van-field colon :value="ruleForm.routeName || '-'" label="路线名称" :border="false" readonly />
            <van-field
              v-model="ruleForm.plateNumber"
              name="plateNumber"
              label="车牌号"
              placeholder="请选择车牌号"
              readonly
              is-link
              :border="false"
              :rules="rules.plateNumber"
              required
              colon
              class="custom-field"
              arrow-direction="down"
              @click="showCarPicker = true"
            />
            <van-field colon :value="ruleForm.driverName || '-'" label="驾驶司机" :border="false" readonly />
          </main>
        </div>
        <footer class="create-footer">
          <van-button
            native-type="button"
            class="round-22"
            block
            type="info"
            color="#4CA786"
            @click="submitFormThrottling"
            >完成</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-popup v-model="showRouteTaskPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="routeTaskList"
        @confirm="onRouteTaskConfirm"
        @cancel="showRouteTaskPicker = false"
        value-key="fieldName"
      />
    </van-popup>
    <van-popup v-model="showCarPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="carOptions"
        :default-index="defaultCarIndex"
        @confirm="onCarConfirm"
        @cancel="showCarPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import { filterObjectByFieldArr } from "@/utils";
  export default {
    data() {
      return {
        submitFormThrottling: () => {},
        apis: {
          adjust: "/api/waybill/vehicle/adjust",
          carList: "/api/vehicle/dossier/list",
          routeTaskList: "/api/waybill/commissionList",
        },
        ruleForm: {
          waybillName: "", //路线收运单名称
          waybillId: "", //路线收运单id
          routeName: "", //路线名称
          plateNumber: "", //车牌号
          driverName: "", //司机名称
        },
        rules: {
          waybillId: [{ required: true, message: "请选择路线收运单" }],
          plateNumber: [{ required: true, message: "请选择车牌号" }],
        },
        showRouteTaskPicker: false,
        routeTaskList: [],
        showCarPicker: false,
        carOptions: [],
        defaultCarIndex: 0,
      };
    },
    created() {
      this.submitFormThrottling = this.$throttling(this.onSubmit, 500);
    },
    async mounted() {
      await this.getOptions();
      this.getRouteWaybillList();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      // 获取路线收运单列表
      async getRouteWaybillList() {
        let res = await createApiFun({}, this.apis.routeTaskList);
        this.routeTaskList = res.data;
        if (this.routeTaskList.length > 0) {
          this.routeTaskList.forEach((list) => {
            list.fieldName = `${list.waybill_code}/${list.effective_date}${list.is_temp === 1 ? "/（临时）" : ""}`;
          });
          let carIndex = this.carOptions.findIndex((list) => list.name === this.ruleForm.plateNumber);
          if (carIndex >= 0) {
            this.defaultCarIndex = carIndex;
          }
        }
      },
      // 路线收运单选择事件
      onRouteTaskConfirm(item) {
        if (this.ruleForm.waybillId === item.id) {
          this.showRouteTaskPicker = false;
          return;
        }
        this.ruleForm.waybillId = item.id;
        this.ruleForm.waybillName = `${item.waybill_code}/${item.effective_date}${
          item.is_temp === 1 ? "/（临时）" : ""
        }`;
        this.ruleForm.routeName = item.name;
        this.ruleForm.plateNumber = item.default_vehicle_plate_number;
        this.ruleForm.driverName = item.default_driver_dossier_name;
        this.showRouteTaskPicker = false;
      },
      // 确认选择车牌号
      onCarConfirm(item) {
        this.ruleForm.plateNumber = item.name;
        this.showCarPicker = false;
      },
      // 提交
      onSubmit() {
        this.$refs.formCreate
          .validate()
          .then(async () => {
            this.$toast({
              type: "loading",
              message: "加载中...",
              forbidClick: true,
              duration: 0,
            });
            let params = filterObjectByFieldArr(this.ruleForm, ["waybillId", "plateNumber"]);
            try {
              let res = await createApiFun(params, this.apis.adjust);
              this.$toast.clear();
              if (res.success) {
                this.$toast({
                  type: "success",
                  message: "调整车辆成功",
                  forbidClick: true,
                  duration: 1500,
                });
                setTimeout(() => {
                  this.$commonBack();
                }, 1500);
              }
            } catch (error) {
              this.$toast.clear();
            }
          })
          .catch((err) => {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: err[0].message,
              duration: 1500,
            });
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f7faf9;
    position: relative;
  }
  .round-22 {
    border-radius: 22px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
  }
  .create-main {
    background-color: #fff;
    border-radius: 8px;
  }
  .create-title {
    font-weight: 500;
    font-size: 16px;
    color: #162e25;
    line-height: 22px;
    padding: 12px 12px 10px 12px;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  ::v-deep .van-form {
    height: 100%;
  }
  ::v-deep .custom-field {
    display: flex;
    align-items: center;
  }
  ::v-deep .custom-field .van-field__value {
    padding: 6px 0;
    padding-left: 8px;
    background-color: #f7faf9;
  }
  ::v-deep .custom-field .van-cell__right-icon {
    height: 36px;
    background-color: #f7faf9;
    display: flex;
    align-items: center;
    margin-left: 0 !important;
  }
</style>
