<template>
  <div class="record-container">
    <div class="type-box" v-for="(item, index) in formList" :key="index">
      <div class="type-title">{{ item.title }}</div>
      <div v-for="(field, fieldIndex) in item.fieldList" :key="fieldIndex">
        <template v-if="field.isImage && ruleForm[field.key] && ruleForm[field.key].url">
          <div class="img-box">
            <div class="record-left">{{ field.value }}</div>
            <div class="img-list">
              <img
                class="img-item"
                :src="ruleForm[field.key].url"
                alt=""
                @click="imagePreview([ruleForm[field.key]], 0)"
              />
            </div>
          </div>
        </template>
        <div class="record-item" v-else>
          <div class="record-left"
            >{{ field.value }}<span v-if="field.unit">({{ field.unit }})</span></div
          >

          <div class="record-right">
            <template v-if="field.options">{{ field.options[ruleForm[field.key]] || "-" }}</template>
            <template v-else>
              {{ ruleForm[field.key] || "-" }}
            </template>
          </div>
        </div>
      </div>
    </div>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex"></van-image-preview>
  </div>
</template>

<script>
  import { SEX_OPTIONS, JOB_STATUS } from "@/enums";
  import { getInfoApiFun } from "@/api/base";
  export default {
    data() {
      return {
        id: "",
        apis: {
          info: "/api/supercargouser/get/",
        },
        ruleForm: {},
        formList: [
          {
            title: "基础信息",
            fieldList: [
              {
                key: "userName",
                value: "用户名",
              },
              {
                key: "fullName",
                value: "真实姓名",
              },
              {
                key: "email",
                value: "邮箱",
              },
              {
                key: "sex",
                value: "性别",
                options: SEX_OPTIONS,
              },
              {
                key: "phone",
                value: "联系电话",
              },
              {
                key: "jobNo",
                value: "工号",
              },
              {
                key: "idCard",
                value: "身份证编号",
              },
              {
                key: "deptName",
                value: "所属部门",
              },
              {
                key: "deptHeadName",
                value: "部门负责人",
              },
              {
                key: "directSuperiorName",
                value: "直属上级",
              },
              {
                key: "positionName",
                value: "职位",
              },
              {
                key: "plateNumber",
                value: "默认押运车辆",
              },
              {
                key: "avatar",
                value: "用户头像",
                isImage: true,
                isObject: true,
              },
              {
                key: "jobStatus",
                value: "在职状态",
                options: JOB_STATUS,
              },
            ],
          },
          {
            title: "证件信息",
            fieldList: [
              {
                key: "qualificationCertificateNumber",
                value: "从业资格证编号",
              },
              {
                key: "qualificationCertificateBeginDate",
                value: "从业资格证有效期开始日期",
              },
              {
                key: "qualificationCertificateEndDate",
                value: "从业资格证有效期结束日期",
              },
              {
                key: "qualificationCertificateRegistrationDate",
                value: "从业资格证登记日期",
              },
              {
                key: "qualificationCertificatePhotoFront",
                value: "从业资格证主页照片",
                isImage: true,
                isObject: true,
              },
              {
                key: "qualificationCertificatePhotoBack",
                value: "从业资格证副页照片",
                isImage: true,
                isObject: true,
              },
            ],
          },
        ],
        previewImages: [],
        previewIndex: 0,
        showPreview: false,
      };
    },
    created() {
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.phone = this.ruleForm.phone ? this.$sm2Decrypt(this.ruleForm.phone) : "";
          this.ruleForm.idCard = this.ruleForm.idCard ? this.$sm2Decrypt(this.ruleForm.idCard) : "";
          try {
            this.ruleForm.avatar = JSON.parse(this.ruleForm.avatar);
          } catch (error) {
            this.ruleForm.avatar = "";
          }
        }
      },
      // 图片预览
      imagePreview(fileList, index) {
        this.previewImages = fileList.map((list) => list.url);
        this.previewIndex = index;
        this.showPreview = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-container {
    padding: 12px;
  }
  .type-box {
    background: linear-gradient(180deg, #eefcf7 0%, #ffffff 100%);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
  }
  .type-title {
    font-weight: 500;
    font-size: 16px;
    color: #4ca786;
    line-height: 22px;
  }
  .record-item {
    padding: 10px 0;
    display: flex;
    align-items: flex-start;
  }
  .record-left {
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #162e25;
    line-height: 20px;
  }
  .record-right {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    text-align: right;
    word-break: break-all;
  }
  .img-box {
    width: 100%;
    padding-top: 10px;
  }
  .img-list {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 8px;
  }
  .img-item {
    width: 60px;
    height: 60px;
    overflow: hidden;
    margin-left: 6px;
  }
  .img-item:first-child {
    margin-left: 0;
  }
  .img-item:nth-child(5n + 6) {
    margin-left: 0;
  }
</style>
