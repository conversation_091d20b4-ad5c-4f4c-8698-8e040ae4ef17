<template>
  <div class="page-container">
    <!-- <div class="countWrapper">
      <div>车辆总数</div>
      <div>{{ total }}(辆)</div>
    </div>
    <div class="countWrapper-2">
      <div>经营性车辆 {{ num1 }}</div>
      <div>非经营性车辆 {{ num2 }}</div>
    </div> -->
    <div class="echart-wrapper" id="monitorOverview"></div>
    <main class="page-main">
      <div class="title">
        <div> 行驶台账记录 </div>
        <div @click="clickMore" class="more">更多 ></div>
      </div>
      <ul class="list" v-if="carOptions.length > 0">
        <li class="list-item" v-for="(item, index) in carOptions" :key="index" @click="selectPlateNumber(item)">
          <div class="item-title">车牌号：{{ item.name }}</div>
        </li>
      </ul>
      <van-empty description="暂无数据" v-else />
    </main>
    <van-dialog
      v-model="showDialog"
      title=""
      show-cancel-button
      cancel-button-text="重置"
      confirm-button-text="查询"
      confirm-button-color="#4ca786"
      :before-close="beforeClose"
    >
      <van-field label="记录开始时间" label-width="100%" />
      <van-field
        v-model="startTime"
        label=""
        placeholder="请选择开始时间"
        is-link
        arrow-direction="down"
        readonly
        :border="false"
        @click="handleClick('startTime')"
      />
      <van-field label="记录结束时间" label-width="100%" />
      <van-field
        v-model="endTime"
        label=""
        placeholder="请选择结束时间"
        is-link
        arrow-direction="down"
        readonly
        :border="false"
        @click="handleClick('endTime')"
      />
    </van-dialog>
    <van-popup v-model="showPicker" position="bottom">
      <van-datetimesec-picker
        v-model="currentDate"
        :datetimePickerProps="{ 'visible-item-count': 6, title: '选择完整时间' }"
        :pickerProps="{ 'visible-item-count': 6 }"
        @cancel="showPicker = false"
        @input="onConfirm"
      />
    </van-popup>
  </div>
</template>

<script>
  import * as echarts from "echarts";

  import { createApiFun } from "@/api/base";
  import moment from "moment";
  import VanDatetimesecPicker from "@/components/van-datetimesec-picker";
  export default {
    components: { VanDatetimesecPicker },
    data() {
      return {
        showPop: false,
        filterForm: {
          plateNumber: "",
        },
        total: 0,
        num1: 0,
        num2: 0,
        plateNumber: "",
        carOptions: [],
        apis: {
          carList: "/api/vehicle/dossier/list",
        },
        showDialog: false,
        showPicker: false,
        currentDate: new Date().getTime(),
        minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
        maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
        startTime: "",
        endTime: "",
        currentField: "",
      };
    },
    async mounted() {
      await this.getOptions();
      this.init();
    },
    methods: {
      init() {
        var myChart = echarts.init(document.getElementById("monitorOverview"));
        myChart.setOption({
          color: ["#A6BEFE", "#4E87FC"],
          legend: {
            selectedMode: false,
            x: "60%",
            // y 设置垂直安放位置，默认全图顶端，可选值：'top' | 'bottom' | 'center' | {number}（y坐标，单位px）
            y: "center",
            orient: "vertical",
            data: [`经营性车辆 ${this.num1}`, `非经营性车辆 ${this.num2}`],
          },
          grid: {
            containLabel: true,
          },
          series: [
            {
              center: ["30%", "50%"],
              type: "pie",
              selectedMode: "single",
              radius: ["0%", "50%"],
              silent: true,
              clockwise: false,
              label: {
                position: "center",
                formatter: `{value|${this.total}} \n {name|车辆总数} `,
                rich: {
                  name: {
                    fontSize: 14,
                  },
                  value: {
                    fontSize: 26,
                    fontWeight: 500,
                    padding: [10, 0, 0, 0],
                    fontFamily: "PangMenZhengDao",
                  },
                },
              },
              itemStyle: {
                normal: {
                  color: "#F7FAF9",
                },
              },
              labelLine: {
                show: false,
              },
              data: [{ value: 0 }],
              right: "10%",
            },
            {
              clockwise: false,
              type: "pie",
              center: ["30%", "50%"],
              silent: true, // 禁止点击事件
              radius: ["55%", "85%"],
              label: {
                show: false,
              },
              emphasis: {
                label: {
                  show: false, // 是否展示中间提示文字
                },
              },
              labelLine: {
                show: false,
              },
              data: [
                {
                  value: this.num1,
                  name: `经营性车辆 ${this.num1}`,
                },
                {
                  value: this.num2,
                  name: `非经营性车辆 ${this.num2}`,
                  itemStyle: {
                    color: "transparent",
                  },
                },
              ],
              right: "10%",
            },
            {
              clockwise: false,
              type: "pie",
              center: ["30%", "50%"],
              silent: true, // 禁止点击事件
              radius: ["60%", "80%"],
              label: {
                show: false,
              },
              emphasis: {
                label: {
                  show: false, // 是否展示中间提示文字
                },
              },
              labelLine: {
                show: false,
              },
              data: [
                {
                  value: this.num1,
                  name: `经营性车辆 ${this.num1}`,
                  itemStyle: {
                    color: "transparent",
                  },
                },
                {
                  value: this.num2,
                  name: `非经营性车辆 ${this.num2}`,
                },
              ],
              right: "10%",
            },
          ],
        });
      },
      clickMore() {
        this.$commonSkip("travel", {});
      },
      // 获取数据列表
      async getOptions() {
        let res = await createApiFun({ statusList: [0, 1] }, this.apis.carList);
        this.total = res.data.length;
        let num1List = res.data.filter((item) => {
          return item.operationalNature == 0;
        });
        this.num1 = num1List.length;
        let num2List = res.data.filter((item) => {
          return item.operationalNature == 1;
        });
        this.num2 = num2List.length;
        this.carOptions = res.data.slice(0, 10);
      },
      // 选择车牌号
      selectPlateNumber(item) {
        this.plateNumber = item.name;
        this.showDialog = true;
      },
      // 点击
      handleClick(field) {
        this.currentField = field;
        this.showPicker = true;
      },
      // 确认
      onConfirm(value) {
        this[this.currentField] = moment(value).format("YYYY-MM-DD HH:mm:ss");
        this.showPicker = false;
      },
      // 关闭前回调
      beforeClose(action, done) {
        if (action === "cancel") {
          this.startTime = "";
          this.endTime = "";
          done();
        }
        if (action === "confirm") {
          if (!this.startTime) {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: "请选择开始时间",
              duration: 1500,
            });
            done(false);
            return;
          }
          if (!this.endTime) {
            this.$toast({
              forbidClick: true,
              type: "fail",
              message: "请选择结束时间",
              duration: 1500,
            });
            done(false);
            return;
          }
          this.$commonSkip("travelRecord", {
            plateNumber: this.plateNumber,
            startTime: this.startTime,
            endTime: this.endTime,
          });
          done();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    background: #f7faf9;
  }
  .countWrapper {
    margin: 20px 60px 0px 60px;
    display: flex;
    justify-content: space-between;
  }
  .countWrapper-2 {
    font-size: 12px;
    margin: 20px 60px 20px 60px;
    display: flex;
    justify-content: space-between;
  }
  .page-header {
    padding: 12px;
    background-color: #fff;
  }
  .filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    background: #f7faf9;
    border-radius: 4px;
    .filter-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 20px;
      margin-right: 4px;
    }
    .filter-icon {
      font-size: 20px;
      color: #939c99;
    }
  }
  .title {
    display: flex;
    justify-content: space-between;
    padding-left: 12px;
    padding-right: 12px;
    line-height: 42px;
    font-size: 16px;
    color: #162e25;
  }
  .more {
    font-size: 14px;
    color: #939c99;
  }
  .page-main {
    background-color: #fff;
    flex: 1;
    overflow-y: auto;
    margin: 12px;
    padding-bottom: 0;
  }
  .list-item {
    padding: 12px;
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #2e3432;
    line-height: 20px;
    word-break: break-all;
  }
  .item-text {
    font-weight: 400;
    font-size: 12px;
    color: #5c6663;
    line-height: 17px;
    margin-top: 8px;
    word-break: break-all;
  }
  .filter-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    margin-bottom: 16px;
  }
  .reset-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #dce0df;
    border-radius: 20px;
  }
  .search-button {
    padding: 10px 22px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    background: #4ca786;
    border-radius: 20px;
    margin-left: 8px;
  }
  .echart-wrapper {
    // width: 140px;
    width: 100%;
    height: 140px;
  }
</style>
