<template>
  <div class="chat-container">
    <div class="content">
      <MsgContent
        :key="checkSide"
        @saveList="saveList"
        :AIlist="listObj[checkSide]"
        :isChange="isChange"
        :exmpleList="exmpleList"
      ></MsgContent>
    </div>
  </div>
</template>

<script>
  import MsgContent from "./components/content.vue";
  export default {
    name: "Chat",
    components: { MsgContent },
    data() {
      return {
        checkSide: null,
        isChange: false,
        exmpleList: [],
        listObj: {},
      };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {
      this.changeSider("demo");
    },

    methods: {
      changeSider(val) {
        this.checkSide = val;
        switch (val) {
          case "demo":
            this.exmpleList = [
              "告诉我今天，某车辆/某人员的收运情况。",
              "帮我分析一下，最近一个月车辆运维成本情况。",
              "请你针对某路线提供一下优化建议。",
            ];
            break;
          default:
            this.exmpleList = [];
            this.isChange = false;

            break;
        }
      },
      saveList(list) {
        this.listObj[this.checkSide] = list;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .chat-container {
    width: 100%;
    height: 100vh;
    min-height: 100dvh;
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    overflow: hidden;
  }
  .content {
    flex: 1;
  }
</style>
