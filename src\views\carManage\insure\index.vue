<template>
  <pageList
    :listPageApi="listPageApi"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入车牌号/经办人名称"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import { INSURANCE_TYPE } from "@/enums";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listPageApi: "/api/vehicleInsure/listPage",
        listFieldArr: [
          {
            key: "plateNumber",
            value: "车牌号",
          },
          {
            key: "year",
            value: "所属年份",
          },
          {
            key: "insuranceType",
            value: "投保类型",
            enums: INSURANCE_TYPE,
            isMultiple: true,
          },
        ],
        filterList: [
          {
            type: "Year",
            key: "year",
            value: "所属年份",
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("insureRecord", { id: item.id, plateNumber: item.plateNumber, year: item.year });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
