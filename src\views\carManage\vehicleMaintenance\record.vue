<template>
  <pageRecord :id="id" :recordApi="apis.info" :recordList="recordList"></pageRecord>
</template>

<script>
  import pageRecord from "@/components/pageRecord";
  import { MAINTENANCE_TYPE } from "@/enums";
  export default {
    components: { pageRecord },
    data() {
      return {
        id: "",
        apis: {
          info: "/api/vehicleMaintenance/get/",
        },
        //保养
        maintainFields: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "type",
                value: "维保类型",
                options: MAINTENANCE_TYPE,
              },
            ],
          },
          {
            title: "常规保养",
            recordFieldArr: [
              {
                key: "operatorName",
                value: "经办人",
              },
              {
                key: "organizationName",
                value: "保养单位",
              },
              {
                key: "recentMaintenanceTime",
                value: "最近保养日期",
              },
              {
                key: "nextMaintenanceTime",
                value: "下次保养日期",
              },
              {
                key: "driveMileage",
                value: "行驶总里程",
                unit: "KM",
              },
              {
                key: "upkeepMileage",
                value: "下次保养里程",
                unit: "KM",
              },
              {
                key: "costs",
                value: "保养费用",
                unit: "元",
              },
              {
                key: "vehicleFileList",
                value: "凭证",
                isImage: true,
              },
            ],
          },
        ],
        //二级维护
        completeMaintenanceFields: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "type",
                value: "维保类型",
                options: MAINTENANCE_TYPE,
              },
            ],
          },
          {
            title: "二级维护",
            recordFieldArr: [
              {
                key: "operatorName",
                value: "经办人",
              },
              {
                key: "organizationName",
                value: "维保单位",
              },
              {
                key: "recentMaintenanceTime",
                value: "最近二级维护日期",
              },
              {
                key: "nextMaintenanceTime",
                value: "下次二级维护日期",
              },
              {
                key: "costs",
                value: "二级维护费用",
                unit: "元",
              },
              {
                key: "vehicleFileList",
                value: "凭证",
                isImage: true,
              },
            ],
          },
        ],
        //维修
        maintainCarFields: [
          {
            title: "基础信息",
            recordFieldArr: [
              {
                key: "plateNumber",
                value: "车牌号",
              },
              {
                key: "type",
                value: "维保类型",
                options: MAINTENANCE_TYPE,
              },
            ],
          },
          {
            title: "维修记录",
            recordFieldArr: [
              {
                key: "operatorName",
                value: "经办人",
              },
              {
                key: "organizationName",
                value: "维修单位",
              },
              {
                key: "recentMaintenanceTime",
                value: "最近维修日期",
              },
              {
                key: "costs",
                value: "维修费用",
                unit: "元",
              },
              {
                key: "vehicleFileList",
                value: "凭证",
                isImage: true,
              },
            ],
          },
        ],
        recordList: [],
      };
    },
    created() {
      this.id = this.$route.query.id;
      let type = Number(this.$route.query.type);
      switch (type) {
        case 0:
          this.recordList = this.maintainFields;
          break;
        case 1:
          this.recordList = this.completeMaintenanceFields;
          break;
        case 2:
          this.recordList = this.maintainCarFields;
          break;
      }
    },
  };
</script>

<style lang="scss" scoped></style>
