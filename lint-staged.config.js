module.exports = {
  "/src*.{js,jsx,vue}": "vue-cli-service lint",
  "/src*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"],
  "/src{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"],
  "package.json": ["prettier --write"],
  "/src*.vue": ["eslint --fix", "prettier --write", "stylelint --fix --allow-empty-input"],
  "/src*.{scss,less,styl,html}": ["stylelint --fix --allow-empty-input", "prettier --write"],
  "*.md": ["prettier --write"],
};
