<template>
  <div class="title-box">
    <div class="title-left">
      <svg class="title-icon" aria-hidden="true">
        <use xlink:href="#icon-ic_biaotizhuangshi"></use>
      </svg>
      <div class="title-text">{{ title }}</div>
    </div>
    <slot name="right"></slot>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: "",
      },
    },
  };
</script>

<style lang="scss" scoped>
  .title-box {
    display: flex;
    align-items: center;
    padding: 12px 0;
    .title-left {
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      .title-icon {
        width: 16px;
        height: 16px;
      }
      .title-text {
        font-weight: 500;
        font-size: 16px;
        color: #4ca786;
        margin-left: 4px;
        margin-top: 2px;
      }
    }
  }
</style>
