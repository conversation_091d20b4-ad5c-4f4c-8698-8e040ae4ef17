<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0,viewport-fit=cover" />

  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <% if (process.env.NODE_ENV==='development' ) { %>
    <script defer src="<%= BASE_URL %>libs/vue.js" ignore></script>
    <script defer src="<%= BASE_URL %>libs/vue-router.js" ignore></script>
    <script defer src="<%= BASE_URL %>libs/vuex.js" ignore></script>
    <% } else { %>
      <script defer src="<%= BASE_URL %>libs/vue.min.js" ignore></script>
      <script defer src="<%= BASE_URL %>libs/vue-router.min.js" ignore></script>
      <script defer src="<%= BASE_URL %>libs/vuex.min.js" ignore></script>
      <% } %>
        <script src="<%= BASE_URL %>iconfont/iconfont.js"></script>
        <script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>
</head>

<body style="overflow: visible !important;overflow-x: visible !important;overflow-y: visible !important;">
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>