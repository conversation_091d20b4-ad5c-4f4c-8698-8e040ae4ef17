<template>
  <pageList
    :listPageApi="apis.listPage"
    :listFieldArr="listFieldArr"
    :filterList="filterList"
    searchField="keyword"
    searchPlaceholder="请输入客商名称/回单据编号"
    @itemClick="itemClick"
  ></pageList>
</template>

<script>
  import pageList from "@/components/pageList";
  import moment from "moment";
  export default {
    components: {
      pageList,
    },
    data() {
      return {
        listFieldArr: [
          {
            key: "customerName",
            value: "客商名称",
          },
          {
            key: "evaluationDate",
            value: "评价日期",
          },
          {
            key: "returnDocumentNumber",
            value: "回单据编号",
          },
        ],
        apis: {
          listPage: "/api/serviceEvaluationRecord/listPage",
        },
        filterList: [
          {
            type: "Date",
            key: "evaluationDate",
            value: "评价日期",
            minDate: new Date(moment().add(-100, "years").format("YYYY/MM/DD")),
            maxDate: new Date(moment().add(100, "years").format("YYYY/MM/DD")),
          },
        ],
      };
    },
    methods: {
      // 查看详情
      itemClick(item) {
        this.$commonSkip("serviceEvaluationRecord", { id: item.id });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
