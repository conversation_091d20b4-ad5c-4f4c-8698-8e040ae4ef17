<template>
  <div class="create-container">
    <van-form :show-error-message="false" :submit-on-enter="false" ref="formCreate">
      <div class="create-box">
        <div class="create-content">
          <main class="create-main">
            <baseTitle title="基础信息"></baseTitle>
            <van-field
              :value="ruleForm.productionUnit"
              label="产废单位名称"
              placeholder="请填写产废单位名称"
              :border="false"
              required
              maxlength="50"
              input-align="right"
            />
            <van-field
              :value="ruleForm.productionUnitOperator"
              label="产废单位经办人"
              placeholder="请填写产废单位经办人"
              label-width="120"
              :border="false"
              maxlength="50"
              input-align="right"
            />
            <van-field label="点位是否正常经营" label-width="130" :border="false" required input-align="right">
              <template #input>
                <van-radio-group :value="ruleForm.operation" direction="horizontal" checked-color="#4CA786">
                  <van-radio :name="1">是</van-radio>
                  <van-radio :name="0">否</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              label="点位是否产生垃圾"
              label-width="130"
              :border="false"
              required
              v-if="ruleForm.operation === 1"
              input-align="right"
            >
              <template #input>
                <van-radio-group :value="ruleForm.isRubbish" direction="horizontal" checked-color="#4CA786">
                  <van-radio :name="1">是</van-radio>
                  <van-radio :name="0">否</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <template v-if="ruleForm.operation === 1 && ruleForm.isRubbish === 1">
              <van-field label="废物类型" label-width="100%" :border="false" required input-align="right" />
              <van-field
                :label="item.name + '重量（kg）'"
                label-width="220"
                :border="false"
                v-for="(item, index) in trashList"
                :key="index"
                :value="ruleForm[item.key]"
                input-align="right"
                required
              />
              <van-field label="垃圾存储方式" label-width="120px" :border="false" required input-align="right">
                <template #input>
                  <van-radio-group :value="ruleForm.baggingMethod" direction="horizontal" checked-color="#4CA786">
                    <van-radio :name="index" v-for="(item, index) in BARRELS_BAGS" :key="index">{{ item }}</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
            </template>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="点位状态"></baseTitle>
            <van-field label="是否清空" label-width="120px" :border="false" required input-align="right">
              <template #input>
                <van-radio-group :value="ruleForm.isClear" direction="horizontal" checked-color="#4CA786">
                  <van-radio :name="1">是</van-radio>
                  <van-radio :name="0">否</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              label="点位剩余废物重量（kg）"
              label-width="160px"
              :border="false"
              required
              v-if="ruleForm.isClear == 0"
              :value="ruleForm.residueRubbish"
              input-align="right"
            >
            </van-field>
          </main>
          <div class="border-line"></div>
          <main class="create-main">
            <baseTitle title="图片信息"></baseTitle>
            <van-field :border="false" required label="收运照片" input-align="right">
              <template #input>
                <img
                  class="image-item"
                  :src="imgItem.url"
                  alt=""
                  v-for="(imgItem, imgIndex) in ruleForm.picture"
                  :key="imgIndex"
                  @click="imagePreview(ruleForm.picture, imgIndex)"
                />
              </template>
            </van-field>
          </main>
        </div>
        <footer class="create-footer" v-if="ruleForm?.id && !ruleForm.verifyStatus">
          <van-button native-type="button" class="round-12" block type="default" @click="verifyThrottling"
            >申请核实</van-button
          >
          <van-button
            native-type="button"
            class="round-12 ml-11"
            block
            type="info"
            color="#4CA786"
            @click="confirmThrottling"
            >确认</van-button
          >
        </footer>
      </div>
    </van-form>
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex"></van-image-preview>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import { BARRELS_BAGS } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  export default {
    components: {
      baseTitle,
    },
    data() {
      return {
        id: "",
        BARRELS_BAGS,
        apis: {
          info: "/api/waybill/waybillDetail/getWaybillDetailById/",
          verify: "/api/waybill/waybillDetail/verify",
        },
        ruleForm: {},
        trashList: [
          {
            name: "感染性废物",
            value: "",
            key: "infectiousWaste",
          },
          {
            name: "损伤性废物",
            value: "",
            key: "damagingWaste",
          },
          {
            name: "药物性废物",
            value: "",
            key: "pharmaceuticalWaste",
          },
          {
            name: "病理性废物",
            value: "",
            key: "pathologicalWaste",
          },
          {
            name: "化学性废物",
            value: "",
            key: "chemicalWaste",
          },
          {
            name: "感染性废物一污泥",
            value: "",
            key: "sludge",
          },
        ],
        previewImages: [],
        previewIndex: 0,
        showPreview: false,
        verifyThrottling: () => {},
        confirmThrottling: () => {},
      };
    },
    created() {
      this.verifyThrottling = this.$throttling(this.onVerify, 500);
      this.confirmThrottling = this.$throttling(this.onConfirm, 500);
      this.id = this.$route.query.id;
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.ruleForm = {
            ...res.data,
            picture: JSON.parse(res.data.picture),
          };
        }
      },
      // 切换tab
      handleStepClick(item) {
        this.step = item.id;
      },
      // 图片预览
      imagePreview(fileList, index) {
        this.previewImages = fileList.map((list) => list.url);
        this.previewIndex = index;
        this.showPreview = true;
      },
      // 申请核实
      async onVerify() {
        this.$toast({
          type: "loading",
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });
        let params = {
          id: this.id,
          verifyStatus: 2,
        };
        try {
          let res = await createApiFun(params, this.apis.verify);
          this.$toast.clear();
          if (res.success) {
            this.$toast({
              type: "success",
              message: "申请核实成功",
              forbidClick: true,
              duration: 1500,
            });
            setTimeout(() => {
              this.$commonBack();
            }, 1500);
          }
        } catch (error) {
          this.$toast.clear();
        }
      },
      // 确认
      async onConfirm() {
        this.$toast({
          type: "loading",
          message: "加载中...",
          forbidClick: true,
          duration: 0,
        });
        let params = {
          id: this.id,
          verifyStatus: 1,
        };
        try {
          let res = await createApiFun(params, this.apis.verify);
          this.$toast.clear();
          if (res.success) {
            this.$toast({
              type: "success",
              message: "确认成功",
              forbidClick: true,
              duration: 1500,
            });
            setTimeout(() => {
              this.$commonBack();
            }, 1500);
          }
        } catch (error) {
          this.$toast.clear();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .create-container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background-color: #f0f2f1;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .round-32 {
    border-radius: 32px;
  }
  .round-12 {
    border-radius: 12px;
  }
  .create-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .create-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 12px;
  }
  .create-main {
    background-color: #fff;
    padding: 0 12px;
  }
  .image-item {
    width: 60px;
    height: 60px;
    overflow: hidden;
    margin-left: 6px;
  }
  .border-line {
    width: 100%;
    height: 2px;
    background-color: #f0f2f1;
  }
  .create-footer {
    padding: 12px 12px 0 12px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
  }
  .ml-11 {
    margin-left: 11px;
  }
  ::v-deep .van-form {
    height: 100%;
  }
</style>
