.formula {
  padding-left: 16px;
  padding-bottom: 4px;
  font-size: 14px;
  color: #909399;
  line-height: 16px;
  border-bottom: 1px solid #f0f2f1;
}
.detail-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #f0f2f1;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
}
.detail-title {
  font-size: 12px;
  color: #909399;
}
.detail-value {
  font-size: 14px;
  margin-top: 4px;
}
.detail-left {
  text-align: center;
  padding-right: 4px;
}
.detail-right {
  text-align: center;
  padding-left: 4px;
}
.detail-middle {
  flex: 1;
  overflow: hidden;
  border-right: 1px solid #909399;
  border-left: 1px solid #909399;
  .middle-item {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    padding: 0 10px;
    margin-bottom: 10px;
    color: #666;
    &:last-child {
      margin-bottom: 0;
    }
    .middle-item-title {
      flex: 1;
      padding-right: 4px;
      line-height: 14px;
    }
  }
}
